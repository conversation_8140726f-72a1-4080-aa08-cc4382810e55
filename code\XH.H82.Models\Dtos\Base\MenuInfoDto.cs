﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.Base
{
    public class MenuInfoDto
    {
        public bool collapased { get; set; }
        public List<navMenus> navMenus { get; set; }
    }
    public class navMenus
    {
        public string key { get; set; }
        public string name { get; set; }
        public string customName { get; set; }
        public bool hidden { get; set; }
        public string icon { get; set; }
        public string iconName { get; set; }
        public string menLevel { get; set; }
        public string parentCode { get; set; }
        public string sort { get; set; }
        public string menuCode { get; set; }
        public string menuUrl { get; set; }
        public string remark { get; set; }
        public List<navMenus> children { get; set; } = new List<navMenus>();
    }

}
