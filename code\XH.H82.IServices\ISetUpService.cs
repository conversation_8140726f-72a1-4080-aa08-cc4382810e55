﻿using H.Utility;
using XH.H82.Models.Entities;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices
{
    public interface ISetUpService
    {
        List<SYS6_SETUP_DICT> GetSysSetUp(string mgroupId);
        ResultDto UpdateSysSetUpInfo(SYS6_SETUP_DICT record);
        ResultDto ApplyAllSetUpInfo(string userNo, string hospitalId, string userName, string choiceValue, string labId);
        bool NeedReviewProcess(string hospitalId);

        bool NeedNeedCheckPasswordByEditEquipment(string hospitalId);

        /// <summary>
        /// 查询房间位置信息列表
        /// </summary>
        /// <returns></returns>
        List<SYS6_POSITION_DICT> GetPositions();
    }
}
