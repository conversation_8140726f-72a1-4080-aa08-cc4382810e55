﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.Extensions.Logging;
using XH.H82.IServices;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class SetUpService : ISetUpService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly ILogger<OperationRecordService> _logger;

        private readonly IAuthorityService _authorityService;

        public SetUpService(ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IAuthorityService authorityService)
        {
            _dbContext = sqlSugarUow;
            _authorityService = authorityService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public List<SYS6_SETUP_DICT> GetSysSetUp(string mgroupId)
        {
            var res = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(p => p.UNIT_ID == mgroupId && p.SETUP_STATE == "1")
                .ToList();
            return res;
        }

        public ResultDto UpdateSysSetUpInfo(SYS6_SETUP_DICT record)
        {
            ResultDto result = new ResultDto();
            try
            {
                var old = _dbContext
                    .Db.Queryable<SYS6_SETUP_DICT>()
                    .Where(p => p.SETUP_NO == record.SETUP_NO)
                    .First();
                if (old == null)
                {
                    throw new KeyNotFoundException("数据不存在");
                }
                old.CHOICE_VALUE = record.CHOICE_VALUE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext
                    .Db.Updateable(old)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "修改失败";
                _logger.LogError("修改失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto ApplyAllSetUpInfo(
            string userNo,
            string hospitalId,
            string userName,
            string choiceValue,
            string labId
        )
        {
            ResultDto result = new ResultDto();
            try
            {
                var  groupList =  _authorityService.GetUserPermissionPgroup(_dbContext.Db, userNo, "H82", hospitalId, labId);
                List<string> arrPgroupList = new List<string>();
                groupList.ForEach(p =>
                {
                    if (p.MGROUP_ID.IsNotNullOrEmpty())
                    {
                        arrPgroupList.Add(p.MGROUP_ID);
                    }
                });
                //所有符合条件的数据
                var old = _dbContext
                    .Db.Queryable<SYS6_SETUP_DICT>()
                    .Where(p => arrPgroupList.Contains(p.UNIT_ID) && p.SETUP_STATE == "1")
                    .ToList();
                old.ForEach(item =>
                {
                    item.CHOICE_VALUE = choiceValue;
                    item.LAST_MPERSON = userName;
                    item.LAST_MTIME = DateTime.Now;
                });
                _dbContext
                    .Db.Updateable(old)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "应用失败";
                _logger.LogError("应用失败:\n" + ex.Message);
            }
            return result;
        }

        public bool NeedReviewProcess(string hospitalId)
        {
            const string KEY = "H820101";
            var result = true;
            var SetUpValue1 = _dbContext
                .Db.Queryable<SYS6_SETUP>()
                .Where(x => x.SETUP_NO == KEY && x.HOSPITAL_ID == hospitalId)
                .First();
            if (SetUpValue1 is not null)
            {
                result = SetUpValue1.SETUP_VALUE == "1";
                return result;
            }
            var SetUpValue2 = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1")
                .First();

            if (SetUpValue2 is not null)
            {
                result = SetUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }

        public bool NeedNeedCheckPasswordByEditEquipment(string hospitalId)
        {
            const string KEY = "H820102";
            var result = true;
            var setUpValue1 = _dbContext
                .Db.Queryable<SYS6_SETUP>()
                .Where(x => x.SETUP_NO == KEY && x.HOSPITAL_ID == hospitalId)
                .First();
            if (setUpValue1 is not null)
            {
                result = setUpValue1.SETUP_VALUE == "1";
                return result;
            }
            var setUpValue2 = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1")
                .First();
            if (setUpValue2 is not null)
            {
                result = setUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }

        public List<SYS6_POSITION_DICT> GetPositions()
        {
            return _dbContext
                .Db.Queryable<SYS6_POSITION_DICT>()
                .Where(x => x.POSITION_STATE == "1")
                .ToList();
        }
    }
}
