//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("aa5482e1-df92-433a-b2e8-b0e0610f3989")]
[assembly: System.Reflection.AssemblyCompanyAttribute("Copyright 杏和软件®")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("6.25.300")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("6.25.300+9e9cf16c08978389d69481fee5d306190af89af2")]
[assembly: System.Reflection.AssemblyProductAttribute("XH.H82.API")]
[assembly: System.Reflection.AssemblyTitleAttribute("XH.H82.API")]
[assembly: System.Reflection.AssemblyVersionAttribute("6.25.300.0")]

// 由 MSBuild WriteCodeFragment 类生成。

