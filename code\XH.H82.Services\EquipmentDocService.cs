﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.SerilogStopwatch;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ReportService;
using RestSharp;
using Serilog;
using SkiaSharp;
using Spire.Doc;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using Spire.Pdf;
using SqlSugar;
using System.ServiceModel;
using System.Text;
using System.Text.RegularExpressions;
using XH.H82.IServices;
using XH.H82.IServices.EquipmentCodeCustom;
using XH.H82.IServices.IoTDevice;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Enums;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.H82.Services.EquipmentEnabled;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class EquipmentDocService : IEquipmentDocService
    {
        private readonly IIoTDeviceService _iotDeviceService;
        private readonly IBaseService _baseService;
        private readonly ILogger<EquipmentDocService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IAuthorityService2 _authorityService;
        private readonly RestClient _clientS16;
        private XingHePrintServiceSoapClient _client;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly string file_preview_address = "";
        private readonly ICustomCodeService _customCodeService;
        private readonly string FileHttpUrl = "/H82pdf/";
        //接口服务地址
        private readonly string _serviceAddress;
        public EquipmentDocService(IBaseService baseService, ISqlSugarUow<SugarDbContext_Master> dbContext, IConfiguration configuration, IHttpContextAccessor httpContext, IHostingEnvironment hostingEnvironment, IMapper mapper, ILogger<EquipmentDocService> logger, IAuthorityService2 authorityService, IIoTDeviceService iotDeviceService, ICustomCodeService customCodeService)
        {
            _baseService = baseService;
            _dbContext = dbContext;
            _httpContext = httpContext;
            _mapper = mapper;
            _hostingEnvironment = hostingEnvironment;
            _configuration = configuration;
            var binding = new BasicHttpBinding();
            binding.MaxReceivedMessageSize = 241000000;
            _serviceAddress = configuration["ReportService"];
            _client = new XingHePrintServiceSoapClient(binding, new EndpointAddress(_serviceAddress));

            var addressS16 = configuration["S16"];
// #if DEBUG
//             //114里面一直配置错误
//             addressS16 = "https://d01.lis-china.com:18706/api/pdf";
// #endif
            if (addressS16.IsNotNullOrEmpty())
            {
                _clientS16 = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (Sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressS16),
                    ThrowOnAnyError = true
                });
            }
            file_preview_address = configuration["S54"];
            _logger = logger;
            _authorityService = authorityService;
            _iotDeviceService = iotDeviceService;
            _customCodeService = customCodeService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }




        public List<EquipmentInfoDto> GetEquipments(string userNo, string labId, string? areaId, string? mgroupId, string? pgroupId, string? state, string? type, string? keyWord)
        {
            var equimentContext = new EquipmentContext(_dbContext);
            var equipments = GetEquipmentsBycondition(true,labId, areaId,mgroupId,pgroupId , state, type, keyWord , null);
            equimentContext.Injection(equipments);
            var result = equimentContext.equipments.Select(equipment => new EquipmentInfoDto
            {
                EQUIPMENT_CLASS_NAME = equimentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                EQUIPMENT_STATE_VALUE = equimentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                EQUIPMENT_STATE = equipment.EQUIPMENT_STATE,
                EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                EQUIPMENT_NAME = equipment.EQUIPMENT_NAME,
                EQUIPMENT_ENAME = equipment.EQUIPMENT_ENAME,
                HOSPITAL_NAME = equimentContext.ExchangeHosptailName(equipment.HOSPITAL_ID),
                LAB_NAME = equimentContext.ExchangeLabName(equipment.LAB_ID),
                EQUIPMENT_MODEL = equipment.EQUIPMENT_MODEL,
                DEPT_SECTION_NO = equipment.DEPT_SECTION_NO,
                REGISTRATION_NUM = equipment.REGISTRATION_NUM,
                REGISTRATION_ENUM = equipment.REGISTRATION_ENUM,
                EQUIPMENT_CODE = equipment.EQUIPMENT_CODE,
                KEEP_PERSON = equipment.KEEP_PERSON,
                SECTION_NO = equipment.SECTION_NO,
                INSTALL_AREA = equipment.INSTALL_AREA,
                SERIAL_NUMBER = equipment.SERIAL_NUMBER,
                VEST_PIPELINE = equipments.Where(x => x.EQUIPMENT_ID == equipment.VEST_PIPELINE).FirstOrDefault()?.EQUIPMENT_CODE,
                EQUIPMENT_NUM = equipment.EQUIPMENT_NUM,
                EQUIPMENT_CLASS = equimentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                CONTACT_PHONE = equipment.CONTACT_PHONE,
                MGROUP_NAME = equimentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID),
                MANUFACTURER = equipment.MANUFACTURER,
                DEALER = equipment.DEALER,
                ENABLE_TIME = equipment.ENABLE_TIME != null ? equipment.ENABLE_TIME.Value.ToString("yyyy-MM-dd") : "",
                INSTALL_DATE = equipment.INSTALL_DATE.IsNotNullOrEmpty() ? DateTime.Parse(equipment.INSTALL_DATE).ToString("yyyy-MM-dd") : "",
                MAINTAIN_DATE = equipment.LAST_MAINTAIN_DATE,
                NEXT_MAINTAIN_DATE = equipment.NEXT_MAINTAIN_DATE,
                COMPARISON_DATE = equipment.LAST_COMPARISON_DATE,
                NEXT_COMPARISON_DATE = equipment.NEXT_COMPARISON_DATE,
                VERIFICATION_DATE = equipment.LAST_VERIFICATION_DATE,
                NEXT_VERIFICATION_DATE = equipment.NEXT_VERIFICATION_DATE,
                CORRECT_DATE = equipment.LAST_CORRECT_DATE,
                NEXT_CORRECT_DATE = equipment.NEXT_CORRECT_DATE,
                MAINTAIN_INTERVALS = equipment.MAINTAIN_INTERVALS,
                CORRECT_INTERVALS = equipment.CORRECT_INTERVALS,
                COMPARISON_INTERVALS = equipment.COMPARISON_INTERVALS,
                VERIFICATION_INTERVALS = equipment.VERIFICATION_INTERVALS,
                IS_HIDE = equipment.IS_HIDE == "1" ? "1" : "0",
                REMARK = equipment.REMARK,
                SMBL_STATE = equipment.SMBL_STATE,
                SMBL_CLASS = equimentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS),
                SMBL_FLAG = equipment.SMBL_FLAG =="1" ? "1": "0",
                SMBL_LAB_ID =  equipment.SMBL_LAB_ID,
                SMBL_LAB_NAME = equimentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID),
                UNIT_ID = equipment.UNIT_ID,
                EQUIPMENT_STAT_SBML  = $"{(equipment.SMBL_FLAG =="1" ? "1": "")}{equipment.EQUIPMENT_STATE}"
            }).OrderBy(i => i.EQUIPMENT_CLASS)
                .ThenBy(x => x.EQUIPMENT_CODE)
                .ToList();
            return result;
        }

        public List<EMS_EQUIPMENT_INFO> GetEquipmentsBycondition(
            bool isInclude,
            string labId, 
            string? areaId, 
            string? mgroupId,
            string? pgroupId, 
            string? state, 
            string? type, 
            string? keyWord,
            string? smblFlag
            )
        {
            var authContext = new AuthorityContext(_dbContext, _authorityService);
            authContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),  labId, areaId);
            var groupList = authContext.GetAccessibleProfessionalGroups(labId, areaId)
                .Where(x => x.MGROUP_ID != null).ToList();
            var MGroupIds = groupList.Select(x => x.MGROUP_ID).Distinct().ToList();
            
            if (mgroupId.IsNotNullOrEmpty())
            {
                groupList = groupList.Where(x => x.MGROUP_ID == mgroupId).ToList();
            }
            
            var MGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(p => MGroupIds.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                .ToList();
            groupList.RemoveAll(x => MGroups.All(p => p.MGROUP_ID != x.MGROUP_ID));
            
            var equipments = new List<EMS_EQUIPMENT_INFO>();

            var equipmentsQuery = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(i => groupList.Select(x => x.PGROUP_ID).Contains(i.UNIT_ID))
                .WhereIF(state.IsNotNullOrEmpty(), i => i.EQUIPMENT_STATE == state)
                .WhereIF(type.IsNotNullOrEmpty(), i => i.EQUIPMENT_CLASS == type)
                .WhereIF(smblFlag.IsNotNullOrEmpty(), x => x.SMBL_FLAG == smblFlag)
                .WhereIF(keyWord.IsNotNullOrEmpty(),
                    i => i.EQUIPMENT_NAME.ToLower().Contains(keyWord.ToLower()) ||
                         i.EQUIPMENT_NUM.ToLower().Contains(keyWord.ToLower()) ||
                         i.EQUIPMENT_CODE.ToLower().Contains(keyWord.ToLower()) ||
                         i.MANUFACTURER.ToLower().Contains(keyWord.ToLower()) ||
                         i.DEALER.ToLower().Contains(keyWord.ToLower()));
            if (isInclude)
            {
                equipmentsQuery
                    .Includes(i => i.eMS_WORK_PLAN)
                    .Includes(i => i.eMS_MAINTAIN_INFO)
                    .Includes(i => i.eMS_CORRECT_INFO)
                    .Includes(i => i.eMS_COMPARISON_INFO)
                    .Includes(i => i.eMS_VERIFICATION_INFO);
            }
            equipmentsQuery.ForEach(it => equipments.Add(it), 200);
            return equipments;
        }


        public List<EquipmentInfoDto> GetEquipmentList(string areaId, string userNo, string hospitalId, string state, string type, string mgroupId, string keyWord, string labId, string pgroupId)
        {
            return GetEquipments(userNo, labId, areaId, mgroupId, pgroupId, state, type, keyWord);
        }
        
        
        #region GetEquipmentInfo 新接口方法  优化保养日期计算
        public EMS_EQUIPMENT_INFO GetEquipmentInfo(string equipmentId)
        {
            var equipmentInfoCotext = new EquipmentContext(_dbContext);
            equipmentInfoCotext.Injection(equipmentId);
            var equipment = equipmentInfoCotext.GetEquipment(equipmentId);
            equipment!.HOSPITAL_NAME = equipmentInfoCotext.ExchangeHosptailName(equipment.HOSPITAL_ID);
            equipment.EARLIEST_ENABLE_DATE = equipment.eMS_START_STOP
                .Where(p => p.START_STOP_STATE != "2" && p.START_CAUSE != "首次启用")
                .OrderBy(i => i.OPER_TIME)
                .Select(m => m.OPER_TIME)
                .FirstOrDefault();
            equipment.LAB_NAME = equipmentInfoCotext.ExchangeLabName(equipment.LAB_ID);
            equipment.IS_HIDE = equipment.IS_HIDE == "1" ? "1" : "0";
            equipment.SMBL_STATE = equipment.SMBL_STATE;
            equipment.SMBL_FLAG = equipment.SMBL_FLAG == "1" ? "1" : "0";
            equipment.SMBL_LAB_ID = equipment.SMBL_LAB_ID;
            equipment.UNIT_ID = equipment.UNIT_ID;
            equipment.EQUIPMENT_STAT_SBML = $"{(equipment.SMBL_FLAG == "1" ? "1" : "")}{equipment.EQUIPMENT_STATE}";

            try
            {
                if (_configuration.GetSection("IsSmbl").Value == "1")
                {
                    equipment.SMBL_LAB_NAME = equipmentInfoCotext.ExchangeSmblLabName(equipment.SMBL_LAB_ID);
                    equipment.SMBL_CLASS_NAME = equipmentInfoCotext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
                    equipment.SWITCH_STATUS = _iotDeviceService.GetEquipmentSwitchStatus(equipment,DateTime.Now);
                    var list = _iotDeviceService.GetObtainMonitoringEquipments();
                    equipment.IS_MONITOR = list.Count(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID) > 0;
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"当前机构不支持生安版本！{e}");
            }


            var dict = _customCodeService.GetEquipmentCodeCustomDictByEquipment(equipment);
            if (dict is not  null)
            {
                var  ucode =  _customCodeService.ExchangeEquipmentUCode(dict.DisplayContent,equipment);
                if (ucode.IsNullOrEmpty())
                {
                    equipment.EQUIPMENT_UCODE = 
                        equipment.EQUIPMENT_UCODE.IsNullOrEmpty()
                            ? equipment.EQUIPMENT_CODE
                            : equipment.EQUIPMENT_UCODE;
                }
                else
                {
                    equipment.EQUIPMENT_UCODE = ucode;
                }
            }
            else
            {
                equipment.EQUIPMENT_UCODE = 
                    equipment.EQUIPMENT_UCODE.IsNullOrEmpty()
                        ? equipment.EQUIPMENT_CODE
                        : equipment.EQUIPMENT_UCODE;
            }

           
            if (equipment.POSITION_ID.IsNotNullOrEmpty())
            {  
                var position =  _dbContext.Db.Queryable<SYS6_POSITION_DICT>().First(X => X.POSITION_ID == equipment.POSITION_ID);
                if (position is not null)
                {
                    equipment.INSTALL_AREA = position.POSITION_NAME;
                }
            }
            
            return equipment;
        }


        public List<EMS_EQUIPMENT_INFO> FindBaseEquipmentInfo(List<string> equipmentIds)
        {
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .Where(p => equipmentIds.Contains(p.EQUIPMENT_ID))
                        .ToList();
            if (equipments is null)
            {
                equipments = new List<EMS_EQUIPMENT_INFO>();
            }

            return equipments;
        }

        #endregion


        public ResultDto SaveEquipmentInfo(EMS_EQUIPMENT_INFO input)
        {
            ResultDto result = new ResultDto();
            var equipmentEnabledContext = new EquipmentEnabledContext(_dbContext);

            try
            {
                var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == input.EQUIPMENT_ID)
                    .Includes(i => i.eMS_INSTALL_INFO)
                    .First();
                if (equipment != null)
                {
                    if (equipment.INSTRUMENT_ID.IsNullOrEmpty())
                    {
                        if (input.EQUIPMENT_CODE.IsNotNullOrEmpty())
                        {
                            var oldEquipmentCode = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().Where(p => p.EQUIPMENT_CODE == input.EQUIPMENT_CODE).First();
                            if (oldEquipmentCode != null && oldEquipmentCode.EQUIPMENT_ID != input.EQUIPMENT_ID)
                            {
                                return new ResultDto { success = false, msg = "设备代号重复" };
                            }
                        }
                        if (input.EQUIPMENT_NUM.IsNotNullOrEmpty())
                        {
                            var oldEquipmentNo = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().Where(p => p.EQUIPMENT_NUM == input.EQUIPMENT_NUM).First();
                            if (oldEquipmentNo != null && oldEquipmentNo.EQUIPMENT_ID != input.EQUIPMENT_ID)
                            {
                                return new ResultDto { success = false, msg = "设备序号重复" };
                            }
                        }
                    }
                    equipment.EQUIPMENT_CODE = input.EQUIPMENT_CODE;
                    equipment.EQUIPMENT_NAME = input.EQUIPMENT_NAME;
                    equipment.EQUIPMENT_ENAME = input.EQUIPMENT_ENAME;
                    equipment.EQUIPMENT_NUM = input.EQUIPMENT_NUM;
                    equipment.EQUIPMENT_MODEL = input.EQUIPMENT_MODEL;
                    equipment.EQUIPMENT_CLASS = input.EQUIPMENT_CLASS;
                    equipment.PROFESSIONAL_CLASS = input.PROFESSIONAL_CLASS;
                    equipment.DEPT_SECTION_NO = input.DEPT_SECTION_NO;
                    if (input.ENABLE_TIME != null)
                    {
                        if (equipment.EQUIPMENT_STATE == "1")
                        {
                            if (input.ENABLE_TIME <= DateTime.Now)
                            {
                                equipment.ENABLE_TIME = input.ENABLE_TIME;
                            }
                            else
                            {
                                throw new BizException("已经启动的设备不可修改初次启动时间大于当前时间");
                            }
                        }
                        else
                        {
                            equipment.ENABLE_TIME = input.ENABLE_TIME;
                        }
                    }
                    equipment.DEPT_NAME = input.DEPT_NAME;
                    equipment.REGISTRATION_NUM = input.REGISTRATION_NUM;
                    equipment.REGISTRATION_ENUM = input.REGISTRATION_ENUM;
                    equipment.UNIT_ID = input.UNIT_ID;
                    equipment.KEEP_PERSON = input.KEEP_PERSON;
                    equipment.PROFESSIONAL_CLASS = input.PROFESSIONAL_CLASS;
                    if (equipment.INSTALL_AREA != input.INSTALL_AREA ||
                        equipment.INSTALL_DATE != input.INSTALL_DATE)
                    {
                        equipment.INSTALL_AREA = input.INSTALL_AREA;
                        equipment.INSTALL_DATE = input.INSTALL_DATE;
                        if (input.POSITION_ID is null)
                        {
                            if (equipment.eMS_INSTALL_INFO != null)
                            {
                                DateTime? installDate = input.INSTALL_DATE == null ? null : Convert.ToDateTime(input.INSTALL_DATE);
                                _dbContext.Db.Updateable<EMS_INSTALL_INFO>()
                                    .SetColumns(p => 
                                        new EMS_INSTALL_INFO
                                        {
                                            INSTALL_AREA = input.INSTALL_AREA,
                                            INSTALL_DATE = installDate,
                                            LAST_MPERSON = input.LAST_MPERSON,
                                            LAST_MTIME = DateTime.Now
                                        }).Where(p => p.EQUIPMENT_ID == input.EQUIPMENT_ID).ExecuteCommand();
                            }
                            else
                            {
                                CreatInstallRecore(input);
                            }    
                        }
                        else
                        {
                            equipment.POSITION_ID = input.POSITION_ID;
                        }
                    }
                    equipment.CONTACT_PHONE = input.CONTACT_PHONE;
                    equipment.SECTION_NO = input.SECTION_NO;
                    equipment.EQ_OUT_TIME = input.EQ_OUT_TIME;
                    equipment.EQ_IN_TIME = input.EQ_IN_TIME;
                    equipment.DEPRECIATION_TIME = input.DEPRECIATION_TIME;
                    equipment.SELL_PRICE = input.SELL_PRICE;
                    equipment.SERIAL_NUMBER = input.SERIAL_NUMBER;
                    equipment.EQ_SCRAP_TIME = input.EQ_SCRAP_TIME;
                    equipment.DEALER_ID = input.DEALER_ID;
                    equipment.MANUFACTURER_ID = input.MANUFACTURER_ID;
                    equipment.MANUFACTURER = input.MANUFACTURER;
                    equipment.DEALER = input.DEALER;
                    equipment.DEALER_ENAME = input.DEALER_ENAME;
                    equipment.MANUFACTURER_ENAME = input.MANUFACTURER_ENAME;
                    equipment.PROVIDER = input.PROVIDER;
                    equipment.PROVIDER_ID = input.PROVIDER_ID;
                    equipment.LAST_MPERSON = input.LAST_MPERSON;
                    equipment.LAST_MTIME = input.LAST_MTIME;
                    equipment.VEST_PIPELINE = input.VEST_PIPELINE;
                    equipment.REMARK = input.REMARK;
                    equipment.EQ_SERVICE_LIFE = input.EQ_SERVICE_LIFE;
                    equipment.SMBL_FLAG = input.SMBL_FLAG;
                    equipment.SMBL_CLASS = input.SMBL_CLASS;
                    equipment.SMBL_STATE = input.SMBL_STATE;
                    equipment.SMBL_LAB_ID = input.SMBL_LAB_ID;
                    equipment.EQUIPMENT_UCODE = $"{equipment.EQUIPMENT_NUM??""}_{equipment.EQUIPMENT_NAME??""}_{equipment.EQUIPMENT_MODEL??""}";
                    equipment.EQUIPMENT_JSON = input.EQUIPMENT_JSON;
                    if (input.SMBL_CLASS == "8")
                    {
                        equipment.EQUIPMENT_JSON = input.EQUIPMENT_JSON;
                    }
                    else
                    {
                        equipment.EQUIPMENT_JSON = null;
                    }
                    _dbContext.Db.Updateable(equipment)
                        .IgnoreColumns(ignoreAllNullColumns: true)
                        .ExecuteCommand();
                    if (input.ENABLE_TIME != null)
                    {
                        equipmentEnabledContext.Injection(equipment);
                        equipmentEnabledContext.SaveEnabled(input.LAST_MPERSON, input.ENABLE_TIME.Value);
                    }
                }
                else
                {
                    var res = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .Where(p => p.EQUIPMENT_NUM == input.EQUIPMENT_NUM || p.EQUIPMENT_CODE == input.EQUIPMENT_CODE)
                        .First();
                    if (res != null)
                    {
                        return new ResultDto { success = false, msg = "设备序号或代号重复" };
                    }
                    input.EQUIPMENT_STATE = "0";
                    _dbContext.Db.Insertable(input).ExecuteCommand();
                    CreatEquipmentWorkPlan(input);
                    if (input.INSTALL_AREA != null || input.INSTALL_DATE != null)
                    {
                        CreatInstallRecore(input);
                    }
                }
                result.success = true;
            }
            catch (BizException ex)
            {
                result.success = false;
                result.msg = "保存设备信息失败:" + ex.Message;
                _logger.LogError("保存设备信息失败:\n" + ex.Message);
            }
            return result;
        }


        private void CreatEquipmentWorkPlan(EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = new EMS_WORK_PLAN();
            workPlan.WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString();
            workPlan.WORK_PLAN_STATE = "1";
            workPlan.HOSPITAL_ID = equipment.HOSPITAL_ID;
            workPlan.EQUIPMENT_ID = equipment.EQUIPMENT_ID;
            workPlan.FIRST_RPERSON = equipment.FIRST_RPERSON;
            workPlan.FIRST_RTIME = DateTime.Now;
            workPlan.LAST_MPERSON = equipment.FIRST_RPERSON;
            workPlan.LAST_MTIME = DateTime.Now;
            _dbContext.Db.Insertable(workPlan).ExecuteCommand();
        }

        private void CreatInstallRecore(EMS_EQUIPMENT_INFO equipment)
        {
            var installInfo = new EMS_INSTALL_INFO();
            installInfo.INSTALL_ID = IDGenHelper.CreateGuid();
            installInfo.HOSPITAL_ID = equipment.HOSPITAL_ID;
            installInfo.EQUIPMENT_ID = equipment.EQUIPMENT_ID;
            installInfo.INSTALL_AREA = equipment.INSTALL_AREA;
            installInfo.INSTALL_DATE = equipment.INSTALL_DATE == null ? null : Convert.ToDateTime(equipment.INSTALL_DATE);
            installInfo.FIRST_RPERSON = equipment.FIRST_RPERSON;
            installInfo.FIRST_RTIME = DateTime.Now;
            installInfo.LAST_MPERSON = equipment.FIRST_RPERSON;
            installInfo.LAST_MTIME = DateTime.Now;
            installInfo.INSTALL_STATE = "1";
            _dbContext.Db.Insertable(installInfo).ExecuteCommand();
        }


        public ResultDto DeleteEquipmentInfo(string equipmentId, string userName)
        {
            var res = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .Includes(i => i.eMS_PURCHASE_INFO)
                .Includes(i => i.eMS_INSTALL_INFO)
                .Includes(i => i.eMS_COMPARISON_INFO)
                .Includes(i => i.eMS_CORRECT_INFO)
                .Includes(i => i.eMS_MAINTAIN_INFO)
                .Includes(i => i.eMS_VERIFICATION_INFO)
                .Includes(i => i.eMS_AUTHORIZE_INFO)
                .Includes(i => i.eMS_CHANGE_INFO)
                .Includes(i => i.eMS_DEBUG_INFO)
                .Includes(i => i.eMS_PARTS_INFO)
                .Includes(i => i.eMS_REPAIR_INFO)
                .Includes(i => i.eMS_SCRAP_INFO)
                .Includes(i => i.eMS_START_STOP)
                .Includes(i => i.eMS_STARTUP_INFO)
                .Includes(i => i.eMS_TRAIN_INFO)
                .Includes(i => i.eMS_UNPACK_INFO)
                .First();
            if (res == null)
            {
                return new ResultDto { success = false, msg = "设备id不存在" };
            }
            if (res.eMS_PURCHASE_INFO != null || res.eMS_INSTALL_INFO != null ||
                res.eMS_COMPARISON_INFO.Count != 0 || res.eMS_CORRECT_INFO.Count != 0 ||
                res.eMS_MAINTAIN_INFO.Count != 0 || res.eMS_VERIFICATION_INFO.Count != 0 ||
                /*res.EMS_WORK_PLAN.Count != 0 ||*/ res.eMS_AUTHORIZE_INFO.Count != 0 ||
                res.eMS_CHANGE_INFO.Count != 0 || res.eMS_DEBUG_INFO.Count != 0 ||
                /*res.EMS_EQUIPMENT_CONTACT.Count != 0 ||*/ res.eMS_PARTS_INFO.Count != 0 ||
                res.eMS_SCRAP_INFO.Count != 0 || res.eMS_STARTUP_INFO.Count != 0 ||
                res.eMS_START_STOP.Count != 0 || res.eMS_TRAIN_INFO.Count != 0 ||
                res.eMS_UNPACK_INFO.Count != 0)
            {
                return new ResultDto { success = false, msg = "设备已有关联数据，无法删除" };
            }
            // _sqlSugarUow.Db.Deleteable<EMS_EQUIPMENT_INFO>().In(equipmentId).ExecuteCommand();
            // var fileList = _sqlSugarUow.Db.Queryable<EMS_DOC_INFO>()
            //     .Where(p => p.DOC_INFO_ID == equipmentId && p.DOC_STATE == "1")
            //     .ToList();
            // fileList.ForEach(item =>
            // {
            //     _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
            // });
            return new ResultDto { success = false, msg = "当前设备不能删除，请联系管理员" };
            //return new ResultDto { success = true };
        }
        public List<SYS6_COMPANY_INFO> GetCompanyList(string type, string serviceArea, string keyword)
        {
            var res = _dbContext.Db.Queryable<SYS6_COMPANY_INFO>()
                .Where(p => p.COMPANY_STATE == "1")
                .WhereIF(type.IsNotNullOrEmpty(), p => p.COMPANY_CLASS.Contains(type))
                .WhereIF(serviceArea.IsNotNullOrEmpty(), p => p.SERVICE_SCOPE == serviceArea)
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.COMPANY_NAME.ToLower().Contains(keyword.ToLower()))
                .ToList();
            var companyClass = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "公司类型" && p.DATA_STATE == "1")
                .ToList();
            res.ForEach(item =>
            {
                
                if (item.COMPANY_CLASS == "5")
                {
                    if (Enum.TryParse<HospitalInternalServiceAreaEnum>(item.SERVICE_SCOPE , out  var enumValue))
                    {
                        item.SERVICE_SCOPE = enumValue.ToDesc();
                    }
                    
                }
                else
                {
                    if (Enum.TryParse<ServiceAreaEnum>(item.SERVICE_SCOPE , out  var enumValue))
                    {
                        item.SERVICE_SCOPE = enumValue.ToDesc();
                    }
                }
                
                item.COMPANY_CLASS = companyClass.Where(p => p.DATA_ID == item.COMPANY_CLASS).FirstOrDefault()?.DATA_CNAME;
            });
            return res;
        }
        public List<SYS6_COMPANY_CONTACT> GetCompanyContactList(string companyId, string equipmentId, string contactType, string contactState, string keyword)
        {
            //取该设备的所有联系人信息
            var contactList = _dbContext.Db.Queryable<EMS_EQUIPMENT_CONTACT>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.CONTACT_ESTATE == "1" && p.CONTACT_TYPE == contactType)
                .ToList();
            //存放设备联系人ID
            var arrContactId = new string[contactList.Count];
            for (int i = 0; i < contactList.Count; i++)
            {
                arrContactId[i] = contactList[i].CONTACT_ID;
            }
            var res = _dbContext.Db.Queryable<SYS6_COMPANY_CONTACT>()
                .Where(p => p.COMPANY_ID == companyId && p.CONTACT_STATE == "1")
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.COMPANY_TYPE.ToLower().Contains(keyword.ToLower()) || p.COMPANY_NAME.ToLower().Contains(keyword.ToLower()))
                .WhereIF(contactState.IsNotNullOrEmpty(), p => p.CONTACT_STATE == contactState)
                .ToList();
            //若当前设备联系人ID包含供应商联系人ID，则标记已选
            res.ForEach(item =>
            {
                if (arrContactId.Contains(item.CONTACT_ID))
                {
                    item.IF_SELECT = "1";
                }
            });
            return res;
        }
        public ResultDto UpdateCompanyContact(List<SYS6_COMPANY_CONTACT> record, string equipmentId, string contactType, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Deleteable<EMS_EQUIPMENT_CONTACT>()
                    .Where(p => p.EQUIPMENT_ID == equipmentId && p.CONTACT_TYPE == contactType).ExecuteCommand();
                record.ForEach(item =>
                {
                    EMS_EQUIPMENT_CONTACT equipmentContact = new EMS_EQUIPMENT_CONTACT();
                    if (item.IF_SELECT == "1")
                    {
                        equipmentContact.CONTACT_ID = item.CONTACT_ID;
                        equipmentContact.EQUIPMENT_ID = equipmentId;
                        equipmentContact.CONTACT_ESTATE = "1";
                        equipmentContact.CONTACT_TYPE = contactType;
                        equipmentContact.FIRST_RPERSON = item.LAST_MPERSON;
                        equipmentContact.FIRST_RTIME = item.LAST_MTIME;
                        equipmentContact.LAST_MPERSON = userName;
                        equipmentContact.LAST_MTIME = DateTime.Now;
                        _dbContext.Db.Insertable(equipmentContact).ExecuteCommand();
                        _dbContext.Db.Updateable<SYS6_COMPANY_CONTACT>().SetColumns(p => new SYS6_COMPANY_CONTACT
                        {
                            CONTACT_STATE = "1",
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        }).Where(p => p.CONTACT_ID == item.CONTACT_ID).ExecuteCommand();
                    }
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "修改联系人信息失败";
                _logger.LogError("修改联系人信息失败:\n" + ex.Message);
            }
            return result;
        }
        public EMS_ENVI_REQUIRE_INFO GetEnviRequireInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_ENVI_REQUIRE_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            if (res != null)
            {
                if (res.EQUIPMENT_SIZE != null)
                {
                    res.LENGTH = res.EQUIPMENT_SIZE.Split(",")[0];
                    res.WIDTH = res.EQUIPMENT_SIZE.Split(",")[1];
                    res.HEIGHT = res.EQUIPMENT_SIZE.Split(",")[2];
                }
                if (res.TEMPERATURE_REQUIRE != null)
                {
                    res.TEMP_MIN = res.TEMPERATURE_REQUIRE.Split(",")[0];
                    res.TEMP_MAX = res.TEMPERATURE_REQUIRE.Split(",")[1];
                }
                if (res.HUMIDITY_REQUIRE != null)
                {
                    res.HUMI_MIN = res.HUMIDITY_REQUIRE.Split(",")[0];
                    res.HUMI_MAX = res.HUMIDITY_REQUIRE.Split(",")[1];
                }
            }
            return res;
        }
        public EMS_ENVI_REQUIRE_INFO SaveEnviRequireInfo(EMS_ENVI_REQUIRE_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_ENVI_REQUIRE_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                .First();
            if (old != null)
            {
                old.EQUIPMENT_SIZE = record.EQUIPMENT_SIZE;
                old.AIR_PRESSURE_REQUIRE = record.AIR_PRESSURE_REQUIRE;
                old.EQUIPMENT_WEIGHT = record.EQUIPMENT_WEIGHT;
                old.BEARING_REQUIRE = record.BEARING_REQUIRE;
                old.SPACE_REQUIRE = record.SPACE_REQUIRE;
                old.AIR_REQUIRE = record.AIR_REQUIRE;
                old.WATER_REQUIRE = record.WATER_REQUIRE;
                old.TEMPERATURE_REQUIRE = record.TEMPERATURE_REQUIRE;
                old.HUMIDITY_REQUIRE = record.HUMIDITY_REQUIRE;
                old.POWER_REQUIRE = record.POWER_REQUIRE;
                old.VOLTAGE_REQUIRE = record.VOLTAGE_REQUIRE;
                old.ELECTRICITY_REQUIRE = record.ELECTRICITY_REQUIRE;
                old.OTHER_REQUIRE = record.OTHER_REQUIRE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public EMS_SUBSCRIBE_INFO GetSubscribeInfo(string equipmentId)
        {
            var list = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>().ToList();
            var res = new EMS_SUBSCRIBE_INFO();
            var res_mgroup = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(p => p.PGROUP_STATE == "1")
                .ToList();
            foreach (var item in list)
            {
                if (item.EQUIPMENT_ID.IsNullOrEmpty())
                {
                    continue;
                }
                var arrEquipment = item.EQUIPMENT_ID.TrimStart(',').TrimEnd(',').Split(',');
                if (arrEquipment.Contains(equipmentId))
                {
                    res = list.Where(p => p.SUBSCRIBE_ID == item.SUBSCRIBE_ID).FirstOrDefault();
                    if (res != null)
                    {
                        res.MGROUP_NAME = res_mgroup.Where(p => p.PGROUP_ID == res.MGROUP_ID).FirstOrDefault()?.PGROUP_NAME;
                        return res;
                    }
                }
            }
            return res;
        }
        public EMS_SUBSCRIBE_INFO SaveSubscribeInfo(EMS_SUBSCRIBE_INFO record)
        {

            var old = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                .Where(p => p.SUBSCRIBE_ID == record.SUBSCRIBE_ID)
                .First();
            if (old != null)
            {
                if (old.EQUIPMENT_ID.IsNullOrEmpty())
                {
                    old.EQUIPMENT_ID = record.EQUIPMENT_ID;
                }
                else
                {

                    var equipmentIds = old.EQUIPMENT_ID.TrimStart(',').TrimEnd(',').Split(",");
                    if (equipmentIds is null)
                    {
                        old.EQUIPMENT_ID = record.EQUIPMENT_ID;
                    }
                    if (equipmentIds.Length == 1 & !equipmentIds.Contains(record.EQUIPMENT_ID))
                    {
                        old.EQUIPMENT_ID = $"{old.EQUIPMENT_ID},{record.EQUIPMENT_ID}".Replace(",,", ",").TrimStart(',').TrimEnd(',');
                    }
                    if (equipmentIds.Length > 1 & !equipmentIds.Contains(record.EQUIPMENT_ID))
                    {
                        old.EQUIPMENT_ID = $"{old.EQUIPMENT_ID},{record.EQUIPMENT_ID}".Replace(",,", ",").TrimStart(',').TrimEnd(',');
                    }

                }


                var list = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                    .Where(x => x.EQUIPMENT_ID.Contains(record.EQUIPMENT_ID))
                    .Where(x => x.SUBSCRIBE_ID != record.SUBSCRIBE_ID)
                    .ToList();

                if (list is not null & list.Count() > 0)
                {
                    list.ForEach(item =>
                    {
                        var ids = item.EQUIPMENT_ID.Split(record.EQUIPMENT_ID);

                        if (ids.Length > 2)
                        {
                            item.EQUIPMENT_ID = $"{ids[0]}{ids[1]}".Replace(",,", ",").TrimStart(',');
                        }
                        else
                        {
                            item.EQUIPMENT_ID = $"{ids[0]}".TrimEnd(',');
                        }
                    });
                    _dbContext.Db.Updateable(list).UseParameter().ExecuteCommand();
                }
                old.REQ_TIME = record.REQ_TIME;
                old.SUBSCRIBE_NO = record.SUBSCRIBE_NO;
                old.SUBSCRIBE_NAME = record.SUBSCRIBE_NAME;
                old.SUBSCRIBE_DATE = record.SUBSCRIBE_DATE;
                old.SUBSCRIBE_PERSON = record.SUBSCRIBE_PERSON;
                old.MGROUP_ID = record.MGROUP_ID;
                old.SUBSCRIBE_STATE = record.SUBSCRIBE_STATE;
                old.APPROVE_TIME = record.APPROVE_TIME;
                old.APPROVE_PERSON = record.APPROVE_PERSON;
                old.APPROVE_OPINION = record.APPROVE_OPINION;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                old.SMBL_FLAG = record.SMBL_FLAG;
                old.SMBL_LAB_ID = record.SMBL_LAB_ID;
                _dbContext.Db.Updateable(old).IgnoreNullColumns().ExecuteCommand();
            }
            if (record.SUBSCRIBE_DATE.HasValue)
            {
                _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>()
                    .SetColumns(x => new EMS_EQUIPMENT_INFO
                        { BUY_DATE = record.SUBSCRIBE_DATE.Value.ToString("yyyy-MM-dd") })
                    .Where(x => x.EQUIPMENT_ID == record.EQUIPMENT_ID)
                    .ExecuteCommand();
            }
            return record;
        }
        public EMS_PURCHASE_INFO GetPurchaseInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_PURCHASE_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            return res;
        }
        public EMS_PURCHASE_INFO SavePurchaseInfo(EMS_PURCHASE_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_PURCHASE_INFO>()
                .Where(p => p.PURCHASE_ID == record.PURCHASE_ID)
                .First();
            if (old != null)
            {
                old.CALL_BIDS_NAME = record.CALL_BIDS_NAME;
                old.CALL_BIDS_NO = record.CALL_BIDS_NO;
                old.CALL_BIDS_DATE = record.CALL_BIDS_DATE;
                old.CONTRACT_NAME = record.CONTRACT_NAME;
                old.CONTRACT_NO = record.CONTRACT_NO;
                old.CONTRACT_DATE = record.CONTRACT_DATE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext.Db.Updateable(old).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            }
            else
            {
                var equipmentInfo = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                    .First();
                record.MGROUP_ID = equipmentInfo.UNIT_ID;
                record.LAB_ID = equipmentInfo.LAB_ID;
                _dbContext.Db.Insertable(record).ExecuteCommand();

                
            }
            return record;
        }
        public EMS_INSTALL_INFO GetInstallInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_INSTALL_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            return res;
        }
        public EMS_INSTALL_INFO SaveInstallInfo(EMS_INSTALL_INFO record)
        {
            var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .First(x => x.EQUIPMENT_ID == record.EQUIPMENT_ID);
            if (equipment is null)
            {
                throw new BizException("设备不存在");
            }

            
            var old = _dbContext.Db.Queryable<EMS_INSTALL_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                .First();
            if (old != null)
            {
                old.INSTALL_DATE = record.INSTALL_DATE;
                old.INSTALL_AREA = equipment.POSITION_ID.IsNullOrEmpty() ? record.INSTALL_AREA : equipment.POSITION_ID;
                old.INSTALL_STATE = record.INSTALL_STATE;
                old.LAB_PERSON = record.LAB_PERSON;
                old.ENGINEER = record.ENGINEER;
                old.INSTALL_CONDITION = record.INSTALL_CONDITION;
                old.HOSPITAL_PERSON = record.HOSPITAL_PERSON;
                old.RELATION_WAY = record.RELATION_WAY;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            if (record.INSTALL_DATE != null)
            {
                var installDate = Convert.ToDateTime(record.INSTALL_DATE).ToString("yyyy-MM-dd");
                var insetallArer = equipment.POSITION_ID.IsNullOrEmpty() ? record.INSTALL_AREA : equipment.POSITION_ID;
                _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>()
                    .SetColumns(p => new EMS_EQUIPMENT_INFO
                {
                    INSTALL_DATE = installDate,
                    INSTALL_AREA = insetallArer,
                    LAST_MPERSON = record.LAST_MPERSON,
                    LAST_MTIME = record.LAST_MTIME,
                }).Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID).ExecuteCommand();
            }
            return record;
        }
        public EMS_UNPACK_INFO GetUnpackInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_UNPACK_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            return res;
        }
        public EMS_UNPACK_INFO SaveUnpackInfo(EMS_UNPACK_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_UNPACK_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                .First();
            if (old != null)
            {
                old.UNPACK_DATE = record.UNPACK_DATE;
                old.UNPACK_PERSON = record.UNPACK_PERSON;
                old.ENGINEER = record.ENGINEER;
                old.LAB_PERSON = record.LAB_PERSON;
                old.APPEARANCE_INSPECT = record.APPEARANCE_INSPECT;
                old.RELATION_WAY = record.RELATION_WAY;
                old.HOSPITAL_PERSON = record.HOSPITAL_PERSON;
                old.UNPACK_CONDITION = record.UNPACK_CONDITION;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public List<EMS_PARTS_INFO> GetPartsList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_PARTS_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.PARTS_STATE == "1")
                .ToList();
            return res;
        }
        public EMS_PARTS_INFO SavePartsInfo(EMS_PARTS_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_PARTS_INFO>()
                   .Where(p => p.PARTS_ID == record.PARTS_ID)
                   .First();
            if (old != null)
            {
                old.PARTS_NAME = record.PARTS_NAME;
                old.PARTS_MODEL = record.PARTS_MODEL;
                old.PARTS_SPEC = record.PARTS_SPEC;
                old.PARTS_AMOUNT = record.PARTS_AMOUNT;
                old.PARTS_SNUM = record.PARTS_SNUM;
                old.PARTS_ORIGIN = record.PARTS_ORIGIN;
                old.PARTS_BRAND = record.PARTS_BRAND;
                old.PARTS_POSITION = record.PARTS_POSITION;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeletePartsInfo(string partsId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_PARTS_INFO>().SetColumns(predicate => new EMS_PARTS_INFO
                {
                    PARTS_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.PARTS_ID == partsId).ExecuteCommand();
                var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => p.DOC_INFO_ID == partsId && p.DOC_STATE == "1")
                    .ToList();
                fileList.ForEach(item =>
                {
                    _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除设备配件信息失败";
                _logger.LogError("删除设备配件信息失败:\n" + ex.Message);
            }
            return result;
        }
        public List<EMS_TRAIN_INFO> GetTrainList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_TRAIN_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.TRAIN_STATE == "1")
                .ToList();
            return res;
        }
        public EMS_TRAIN_INFO SaveTrainInfo(EMS_TRAIN_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_TRAIN_INFO>()
                .Where(p => p.TRAIN_ID == record.TRAIN_ID)
                .First();
            if (old != null)
            {
                old.TRAIN_TIME = record.TRAIN_TIME;
                old.TRAIN_NAME = record.TRAIN_NAME;
                old.TRAIN_ADDR = record.TRAIN_ADDR;
                old.TRAIN_HOUR = record.TRAIN_HOUR;
                old.JOIN_PERSON = record.JOIN_PERSON;
                old.TRAIN_TEACHER = record.TRAIN_TEACHER;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteTrainInfo(string trainId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_TRAIN_INFO>().SetColumns(predicate => new EMS_TRAIN_INFO
                {
                    TRAIN_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.TRAIN_ID == trainId).ExecuteCommand();
                var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => p.DOC_INFO_ID == trainId && p.DOC_STATE == "1")
                    .ToList();
                fileList.ForEach(item =>
                {
                    _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除设备培训记录信息失败";
                _logger.LogError("删除设备培训记录信息失败:\n" + ex.Message);
            }
            return result;
        }
        public EMS_DEBUG_INFO GetDebugInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_DEBUG_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            return res;
        }
        public EMS_DEBUG_INFO SaveDebugInfo(EMS_DEBUG_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_DEBUG_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                .First();
            if (old != null)
            {
                old.DEBUG_DATE = record.DEBUG_DATE;
                old.HOSPITAL_PERSON = record.HOSPITAL_PERSON;
                old.ENGINEER = record.ENGINEER;
                old.DEBUG_CONDITION = record.DEBUG_CONDITION;
                old.LAB_PERSON = record.LAB_PERSON;
                old.RELATION_WAY = record.RELATION_WAY;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public List<EMS_AUTHORIZE_INFO> GetAuthorizeList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_AUTHORIZE_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.AUTHORIZE_STATE == "1")
                .ToList();

            
            
            foreach (var item in res)
            {
                if (item.AUTHORIZE_PERSON.IsNotNullOrEmpty() && item.AUTHORIZED_PERSON.IsNotNullOrEmpty())
                {
                    item.AUTHORIZE_PERSON = "";
                    item.AUTHORIZED_PERSON = "";
                    continue;
                }

                if (item.AUTHORIZE_PERSON.IsNotNullOrEmpty())
                {
                    var authPerson = item.AUTHORIZE_PERSON.Split("_");
                    var userNo = _dbContext.Db.Queryable<SYS6_USER>()
                        .Where(x => x.LOGID == authPerson[0])
                        .Where(x => x.USERNAME == authPerson[1])
                        .Select(x => x.USER_NO)
                        .First();
                    item.AUTHORIZE_PERSON_ID = userNo;
                }
                
                if ( item.AUTHORIZED_PERSON.IsNotNullOrEmpty() )
                {
                    var beAuthPersonInfos = item.AUTHORIZED_PERSON.Split(";");
                    foreach (var beAuthPersonInfo in beAuthPersonInfos)
                    {
                        var beAuthPerson = beAuthPersonInfo.Split("_");
                        var beUserNo = _dbContext.Db.Queryable<SYS6_USER>()
                            .Where(x => x.LOGID == beAuthPerson[0])
                            .Where(x => x.USERNAME == beAuthPerson[1])
                            .Select(x => x.USER_NO)
                            .First();

                        item.AUTHORIZED_PERSON_IDS += beUserNo + ";";
                    }
                    item.AUTHORIZED_PERSON_IDS = item.AUTHORIZED_PERSON_IDS.TrimEnd(';');
                }
            }
            
            return res;
        }
        public EMS_AUTHORIZE_INFO SaveAuthorizeInfo(EMS_AUTHORIZE_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_AUTHORIZE_INFO>()
                .Where(p => p.AUTHORIZE_ID == record.AUTHORIZE_ID)
                .First();
            if (old != null)
            {
                old.AUTHORIZED_PERSON = record.AUTHORIZED_PERSON;
                old.AUTHORIZE_PERSON = record.AUTHORIZE_PERSON;
                old.AUTHORIZED_ROLE = record.AUTHORIZED_ROLE;
                old.AUTHORIZED_PERSON_POST = record.AUTHORIZED_PERSON_POST;
                old.AUTHORIZE_DATE = record.AUTHORIZE_DATE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteAuthorizeInfo(string authorizeId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_AUTHORIZE_INFO>().SetColumns(predicate => new EMS_AUTHORIZE_INFO
                {
                    AUTHORIZE_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.AUTHORIZE_ID == authorizeId).ExecuteCommand();
                var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => p.DOC_INFO_ID == authorizeId && p.DOC_STATE == "1")
                    .ToList();
                fileList.ForEach(item =>
                {
                    _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除授权记录失败";
                _logger.LogError("删除授权记录失败:\n" + ex.Message);
            }
            return result;
        }
        public EMS_STARTUP_INFO GetStartupInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_STARTUP_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            return res;
        }
        public EMS_STARTUP_INFO SaveStartupInfo(EMS_STARTUP_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_STARTUP_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID)
                .First();
            if (old != null)
            {
                old.START_DATE = record.START_DATE;
                old.END_DATE = record.END_DATE;
                old.VERIFY_PERSON = record.VERIFY_PERSON;
                old.VERIFY_RESULT = record.VERIFY_RESULT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }

        public List<EMS_START_STOP> GetStartStopList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_START_STOP>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .OrderBy(x => x.FIRST_RTIME)
                .ToList();
            return res;
        }
        public ResultDto SaveStartInfo(EMS_START_STOP record)
        {
            if (!record.START_DATE.HasValue)
            {
                return new ResultDto { success = false, msg = "请填写启用时间" };
            }
            var equipmentEnabledContext = new EquipmentEnabledContext(_dbContext);
            equipmentEnabledContext.Injection(record.EQUIPMENT_ID);
            var equipment = equipmentEnabledContext._equipment;
            var old = equipment.eMS_START_STOP.Where(p => p.START_STOP_ID == record.START_STOP_ID).FirstOrDefault();
            if (old != null)
            {
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                if (equipment.EQUIPMENT_STATE != "1")
                {

                    if (equipment.eMS_START_STOP.Count(x => x.START_CAUSE == "首次启用") > 0)
                    {
                        equipmentEnabledContext.Enabled(record.LAST_MPERSON, record.START_DATE.Value, record.START_CAUSE.IsNullOrEmpty() ? "" : record.START_CAUSE.Replace("首次启用", ""));
                    }
                    else
                    {
                        equipmentEnabledContext.Enabled(record.LAST_MPERSON, record.START_DATE.Value, "首次启用");
                    }

                }
            }
            return new ResultDto { success = true };
        }
        public EMS_SCRAP_INFO GetScrapInfo(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_SCRAP_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.APPLY_STATE == "3" && p.SCRAP_STATE == "1")
                .First();
            return res;
        }
        public ResultDto SaveScrapInfo(EMS_SCRAP_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_SCRAP_INFO>()
                .Where(p => p.EQUIPMENT_ID == record.EQUIPMENT_ID && p.APPLY_STATE == "3" && p.SCRAP_STATE == "1")
                .First();
            if (old == null)
            {
                return new ResultDto { success = false, msg = "报废信息不存在" };
            }
            old.LAST_MTIME = record.LAST_MTIME;
            old.LAST_MPERSON = record.LAST_MPERSON;
            old.REMARK = record.REMARK;
            _dbContext.Db.Updateable(old).ExecuteCommand();
            return new ResultDto { success = true };

        }
        public List<SYS6_COMPANY_CONTACT> GetContactInfoList(string equipmentId, string state, string supplier, string keyWord)
        {
            var res = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .First();
            if (res == null)
            {
                throw new BizException($"设备信息{equipmentId}不存在");
            }
            var list = _dbContext.Db.Queryable<SYS6_COMPANY_CONTACT>()
                .Where(p => (p.COMPANY_ID == res.DEALER_ID || p.COMPANY_ID == res.MANUFACTURER_ID) && p.CONTACT_STATE != "2")
                .WhereIF(state.IsNotNullOrEmpty(), p => p.CONTACT_STATE == state)
                .WhereIF(keyWord.IsNotNullOrEmpty(), p => p.CONTACT_TYPE.ToLower().Contains(keyWord.ToLower()) || p.CONTACT_NAME.ToLower().Contains(keyWord.ToLower()))
                .ToList();
            var companyInfo = _dbContext.Db.Queryable<SYS6_COMPANY_INFO>().ToList();
            //取该设备的所有联系人信息
            var contactList = _dbContext.Db.Queryable<EMS_EQUIPMENT_CONTACT>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .ToList();
            //存放设备联系人ID
            var arrContactId = new string[contactList.Count];
            for (int i = 0; i < contactList.Count; i++)
            {
                arrContactId[i] = contactList[i].CONTACT_ID;
            }
            list.ForEach(item =>
            {
                if (arrContactId.Contains(item.CONTACT_ID))
                {
                    item.IF_SELECT = "1";
                    item.CONTACT_STATE = contactList.Where(p => p.CONTACT_ID == item.CONTACT_ID
                    && p.EQUIPMENT_ID == equipmentId).FirstOrDefault()?.CONTACT_ESTATE;
                }
                item.COMPANY_NAME = companyInfo.Where(p => p.COMPANY_ID == item.COMPANY_ID).FirstOrDefault()?.COMPANY_NAME;
                if (item.COMPANY_ID == res.DEALER_ID && item.COMPANY_ID != res.MANUFACTURER_ID)
                {
                    item.COMPANY_TYPE = "经销商";
                }
                if (item.COMPANY_ID != res.DEALER_ID && item.COMPANY_ID == res.MANUFACTURER_ID)
                {
                    item.COMPANY_TYPE = "制造商";
                }
                if (item.COMPANY_ID == res.DEALER_ID && item.COMPANY_ID == res.MANUFACTURER_ID)
                {
                    item.COMPANY_TYPE = "制造商+经销商";
                }
            });
            if (supplier.IsNotNullOrEmpty())
            {
                list = list.Where(p => p.COMPANY_TYPE == supplier).ToList();
            }
            return list.Where(i => i.IF_SELECT == "1").ToList();
        }
        public SYS6_COMPANY_CONTACT SaveContactInfo(SYS6_COMPANY_CONTACT record)
        {
            var old = _dbContext.Db.Queryable<SYS6_COMPANY_CONTACT>()
                .Where(p => p.CONTACT_ID == record.CONTACT_ID)
                .First();
            if (old != null)
            {
                old.CONTACT_STATE = record.CONTACT_STATE;
                old.COMPANY_ID = record.COMPANY_ID;
                old.CONTACT_TYPE = record.CONTACT_TYPE;
                old.CONTACT_POST = record.CONTACT_POST;
                old.CONTACT_NAME = record.CONTACT_NAME;
                old.PHONE_NO = record.PHONE_NO;
                old.CONTACT_WX = record.CONTACT_WX;
                old.E_MAIL = record.E_MAIL;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var context = new DeviceSynchronizationContext(_dbContext);
                context.SetSeed(record.CONTACT_ID);
                record.CONTACT_ID = context.GetEquipmentId();
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteContactInfo(string contactId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_EQUIPMENT_CONTACT>().SetColumns(predicate => new EMS_EQUIPMENT_CONTACT
                {
                    CONTACT_ESTATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.CONTACT_ID == contactId).ExecuteCommand();
                _dbContext.Db.Updateable<SYS6_COMPANY_CONTACT>().SetColumns(predicate => new SYS6_COMPANY_CONTACT
                {
                    CONTACT_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.CONTACT_ID == contactId).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除供应商联系信息失败";
                _logger.LogError("删除供应商联系信息失败:\n" + ex.Message);
            }
            return result;
        }
        public List<CardElementDto> GetCardElement(string cardType, List<string> equipmentIds)
        {
            List<CardElementDto> result = new List<CardElementDto>();
            if (equipmentIds == null || !equipmentIds.Any())
                return result;

            result = equipmentIds.Select(id =>
            {
                var dto = new CardElementDto();
                EMS_EQUIPMENT_INFO info = GetEquipmentInfo(id);
                EMS_ENVI_REQUIRE_INFO envi = GetEnviRequireInfo(id);
                SYS6_COMPANY_CONTACT dealerContract = null;
                SYS6_COMPANY_CONTACT manufactoryContract = null;
                if (info != null)
                {
                    dto = _mapper.Map<CardElementDto>(info);
                    dto.EQUIPMENT_STATE_NAME = dto.EQUIPMENT_STATE switch
                    {
                        "0" => "未启用",
                        "1" => "启用",
                        "2" => "停用",
                        "3" => "报废",
                        _ => "启用"
                    };
                    dto.REGISTER_TIME = info.REGISTER_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.EQ_IN_TIME = info.EQ_IN_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.EQ_OUT_TIME = info.EQ_OUT_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.EQ_SCRAP_TIME = info.EQ_SCRAP_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.ENABLE_TIME = info.ENABLE_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.FIRST_START_TIME = info.FIRST_START_TIME?.ToString("yyyy-MM-dd") ?? "";
                    dto.LAST_MAINTAIN_DATE = info.LAST_MAINTAIN_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.LAST_COMPARISON_DATE = info.LAST_COMPARISON_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.LAST_VERIFICATION_DATE = info.LAST_VERIFICATION_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.LAST_CORRECT_DATE = info.LAST_CORRECT_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.VALID_CORRECT_DATE = info.LAST_CORRECT_DATE.HasValue ? info.LAST_CORRECT_DATE.Value.AddDays(-1).ToString("yyyy-MM-dd") : "";
                    dto.NEXT_MAINTAIN_DATE = info.NEXT_MAINTAIN_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.NEXT_COMPARISON_DATE = info.NEXT_COMPARISON_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.NEXT_VERIFICATION_DATE = info.NEXT_VERIFICATION_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.NEXT_CORRECT_DATE = info.NEXT_CORRECT_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.EARLIEST_ENABLE_DATE = info.EARLIEST_ENABLE_DATE?.ToString("yyyy-MM-dd") ?? "";
                    dto.DEBUG_ENGINEER = info.eMS_DEBUG_INFO?.FirstOrDefault()?.ENGINEER;
                    dto.DEBUG_ENGINEER_PHONE = info.eMS_DEBUG_INFO?.FirstOrDefault()?.RELATION_WAY;
                    dto.INSTALL_ENGINEER = info.eMS_INSTALL_INFO?.ENGINEER;
                    dto.INSTALL_ENGINEER_PHONE = info.eMS_INSTALL_INFO?.RELATION_WAY;
                    dto.LAST_CORRECT = dto.LAST_CORRECT_DATE;
                    dto.NEXT_CORRECT = dto.NEXT_CORRECT_DATE;
                    dto.E_TIME = dto.ENABLE_TIME;
                    if (envi != null)
                    {
                        dto.EQUIPMENT_WEIGHT = envi.EQUIPMENT_WEIGHT;
                        dto.BEARING_REQUIRE = envi.BEARING_REQUIRE;
                        dto.SPACE_REQUIRE = envi.SPACE_REQUIRE;
                        dto.AIR_REQUIRE = envi.AIR_REQUIRE;
                        dto.WATER_REQUIRE = envi.WATER_REQUIRE;
                        dto.TEMPERATURE_REQUIRE = envi.TEMPERATURE_REQUIRE;
                        dto.HUMIDITY_REQUIRE = envi.HUMIDITY_REQUIRE;
                        dto.AIR_PRESSURE_REQUIRE = envi.AIR_PRESSURE_REQUIRE;
                        dto.POWER_REQUIRE = envi.POWER_REQUIRE;
                        dto.VOLTAGE_REQUIRE = envi.VOLTAGE_REQUIRE;
                        dto.ELECTRICITY_REQUIRE = envi.ELECTRICITY_REQUIRE;
                        dto.OTHER_REQUIRE = envi.OTHER_REQUIRE;
                        dto.REQUIRE_STATE = envi.REQUIRE_STATE;
                        dto.LENGTH = envi.LENGTH;
                        dto.WIDTH = envi.WIDTH;
                        dto.HEIGHT = envi.HEIGHT;
                        dto.TEMP_MIN = envi.TEMP_MIN;
                        dto.TEMP_MAX = envi.TEMP_MAX;
                        dto.HUMI_MIN = envi.HUMI_MIN;
                        dto.HUMI_MAX = envi.HUMI_MAX;
                    }
                    if (info.DEALER.IsNotNullOrEmpty())
                    {
                        dealerContract = GetCompanyContactList(info.DEALER_ID, info.EQUIPMENT_ID, "经销商", "1", null).FirstOrDefault();
                        manufactoryContract = GetCompanyContactList(info.MANUFACTURER_ID, info.EQUIPMENT_ID, "制造商", "1", null).FirstOrDefault();
                    }
                    dto.DEALER_PHONE = dealerContract?.PHONE_NO;
                    dto.MANUFACTURER_PHONE = manufactoryContract?.PHONE_NO;
                    LIS6_INSPECTION_GROUP groupInfo = _dbContext.Db.Queryable<LIS6_INSPECTION_GROUP>()
                        .InnerJoin<LIS6_INSTRUMENT_INFO>((group, ins) => group.GROUP_ID == ins.GROUP_ID)
                        .InnerJoin<EMS_EQUIPMENT_INFO>((group, ins, eqt) => ins.INSTRUMENT_ID == eqt.INSTRUMENT_ID)
                        .Where((group, ins, eqt) => group.STATE_FLAG == "1")
                        .First();
                    dto.GROUP_ID = groupInfo?.GROUP_ID;
                    dto.GROUP_NAME = groupInfo?.GROUP_NAME;
                }
                return dto;
            }).Where(a => a != null).ToList();

            return result;
        }
        public ResultDto ForbidContactPerson(string equipment, string userName, SYS6_COMPANY_CONTACT record)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_EQUIPMENT_CONTACT>().SetColumns(p => new EMS_EQUIPMENT_CONTACT
                {
                    CONTACT_ESTATE = p.CONTACT_ESTATE == "1" ? "0" : "1",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "操作联系人状态失败";
                _logger.LogError("操作联系人状态失败:\n" + ex.Message);
            }

            return result;
        }
        public List<BasicDataDto> GetCompanyClass()
        {
            var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
                .ToList();
            var result = new List<BasicDataDto>();
            res.ForEach(item =>
            {
                if (item.DATA_CNAME == "常规检测设备")
                {
                    item.DATA_CNAME = "检测仪器/流水线";
                }
                result.Add(new BasicDataDto
                {
                    BasciId = item.DATA_ID,
                    BasicName = item.DATA_CNAME
                });
            });
            return result;
        }

        /// <summary>
        /// 获取标识卡生成的pdf
        /// </summary>
        /// <param name="dataXml"></param>
        /// <returns></returns>
        public string CardPrintBase64(string dataXml)
        {
            ////头部新增签名节点
            //var xmlHead = new XmlDocument();
            //xmlHead.LoadXml(dataXml);
            //var headRoot = xmlHead.SelectSingleNode("REQUEST");
            ////接口以后可能会需要鉴权,此处加入签名字段
            //var node = xmlHead.CreateElement("Sign");
            //node.InnerText = SmxUtilsHelper.SM3Utils(AppSettingsProvider.XingHePlatFormKey + dataXml);
            //headRoot.AppendChild(node);
            //dataXml = xmlHead.OuterXml;
            using (var op = LogStopwatch.Begin("生成设备标识卡"))
            {
                string requestInfo = "";
                string resultCode = "";
                string resXml = "";
                if (_clientS16 == null)
                {
                    throw new BizException($"当前服务S16地址获取失败，目前获取值为:{_configuration["S16"]},请检查S16模块是否注册或者config/appsettings.json文件的ExtraRegistrationModules是否配置S16,如 \"ExtraRegistrationModules\": \"J02,S16\" ");
                }
                try
                {

                    //Stopwatch sw = new Stopwatch();
                    //sw.Start();
                    string url = $"/ExportPdfSheet6";
                    JObject patientinfo = new JObject();
                    patientinfo["DataXml"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(dataXml));
                    string sendObj = JsonConvert.SerializeObject(patientinfo);
                    RestRequest request = new RestRequest(url);
                    request.AddJsonBody(sendObj);
                    requestInfo += $"\n DataXml:{dataXml}\n";
                    requestInfo += $"\n sendObj:{sendObj}\n";
                    requestInfo += $"\n BuildUri:{_clientS16.BuildUri(request)}\n";
                    var response = _clientS16.ExecutePost<string>(request);
                    //sw.Stop();
                    op.Lap("调用S16的ExportPdfSheet6接口");
                    resXml = response.Content;

                    resultCode = GetMiddleValue(resXml, "<ResultCode>", "</ResultCode>");
                    if (resultCode == "-1")
                    {
                        throw new BizException($"{resXml}");
                    }
                    var pdfBase64 = GetMiddleValue(resXml, "<ResultMsg>", "</ResultMsg>");
                    //var jsonString = JsonConvert.SerializeXmlNode(resXml, Newtonsoft.Json.Formatting.Indented);
                    //dynamic resJson = JsonConvert.DeserializeObject(jsonString);
                    //var resultCode = resJson["Response"][0]["ResultCode"].ToString();
                    //if(resultCode == "-1")
                    //{
                    //    throw new BizException("调用打印服务失败");
                    //}
                    //var base64 = resJson["Response"][0]["ResultMsg"].ToString();
                    var base64 = PdfToImage(pdfBase64);
                    op.Lap("PDF转换成图片Base64");
                    return base64;
                }
                catch (Exception ex)
                {
                    Log.Error("(CardPrintBase64)获取标识卡生成的pdf失败:" + ex + requestInfo);
                    Log.Error($"resultCode:{resultCode}。\n resXml:{resXml}\n");
                    throw new BizException("获取标识卡生成的pdf失败" + ex.Message);
                }
                finally
                {
                    op.Complete();
                }
            }

        }
        public string PdfToImage(string pdfBase64)
        {
            Spire.Pdf.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            PdfDocument doc = new PdfDocument();
            byte[] bytes = Convert.FromBase64String(pdfBase64);
            doc.LoadFromBytes(bytes);
            var ms = doc.SaveAsImage(0);
            var bitmap = SKBitmap.Decode(ms);
            var image = SKImage.FromBitmap(bitmap);
            var data = image.Encode(SKEncodedImageFormat.Png, 100);
            var memoryStream = new MemoryStream();
            data.SaveTo(memoryStream);
            var res = Convert.ToBase64String(memoryStream.ToArray());
            return res;
        }
        public List<PgroupPipelineDto> GetPipelineList(string pgroupId, string labId, string areaId, string userNo)
        {
            var authContext = new AuthorityContext(_dbContext, _authorityService);
            authContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),labId,areaId);
            var pgroupList = authContext.GetAccessibleProfessionalGroups(labId, areaId)
                .Select((pgroup) => new
                {
                    pgroup.PGROUP_ID,
                    pgroup.PGROUP_NAME,
                    pgroup.PGROUP_SORT,
                }).ToList();
            var pipelineList = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .InnerJoin<SYS6_BASE_DATA>((equipment, baseData) => equipment.EQUIPMENT_TYPE == baseData.DATA_ID)
                .Where((equipment, baseData) => baseData.CLASS_ID == "设备类型" && baseData.DATA_CNAME == "流水线" && baseData.DATA_STATE == "1")
                .Where((equipment, baseData) => equipment.EQUIPMENT_STATE == "1" || equipment.EQUIPMENT_STATE == "0")
                .ToList();
            var result = new List<PgroupPipelineDto>();
            pgroupList.ForEach(item =>
            {
                var pipelineDto = pipelineList.Where(p => p.UNIT_ID == item.PGROUP_ID).Select(i => new PipelineDto
                {
                    PIPELINE_ID = i.EQUIPMENT_ID,
                    PIPELINE_NAME = i.EQUIPMENT_CODE
                }).ToList();
                result.Add(new PgroupPipelineDto
                {
                    PGROUP_ID = item.PGROUP_ID,
                    PGROUP_NAME = item.PGROUP_NAME,
                    PipelineDto = pipelineDto
                });
            });
            result.RemoveAll(x => !x.PipelineDto.Any());
            return result;
        }
        public string GetMiddleValue(string str, string start, string end)
        {
            string pattern = "(?<=" + start + ")[.\\s\\S]*?(?=" + end + ")";
            Match match = Regex.Match(str, pattern);
            return match.Value;
        }
        public ResultDto ExportDeviceList(List<EMS_EQUIPMENT_INFO> record, string exportType)
        {
            Spire.Doc.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            //导出清单模板
            Document docTemp = new Document();
            var docTemp_path = Path.Combine(AppContext.BaseDirectory, "ExampleFile", "EquipmentTemplate.docx");
            docTemp.LoadFromFile(docTemp_path);
            Section docTemp_section = docTemp.Sections[0];
            HeaderFooter docTemp_header = docTemp_section.HeadersFooters.Header;
            Table docTemp_table = (Table)docTemp.Sections[0].Tables[0];
            var tableTemp_height = docTemp_table.Rows[0].Height;

            Document document = new Document();
            Section section = document.AddSection();
            PageSetup pageSetup = section.PageSetup;
            //设置页面方向为横向
            pageSetup.Orientation = PageOrientation.Landscape;
            //页边距设置
            pageSetup.Margins.Top = 56.7f; // 上边距
            pageSetup.Margins.Bottom = 56.7f; // 下边距
            pageSetup.Margins.Left = 51.03f; // 左边距
            pageSetup.Margins.Right = 56.7f; // 右边距
            //页眉设置
            HeaderFooter header = section.HeadersFooters.Header;
            Paragraph headerParagraph = header.AddParagraph();
            TextRange textRange = headerParagraph.AppendText($"{docTemp_header.Paragraphs[0].Text}");
            textRange.CharacterFormat.FontName = "宋体";
            textRange.CharacterFormat.FontSize = 9;
            header.Paragraphs[0].Format.Borders.Bottom.BorderType = BorderStyle.Single;

            //标题设置
            Paragraph title = section.AddParagraph();
            TextRange titleRange = title.AppendText("设备清单");
            titleRange.CharacterFormat.FontName = "宋体";
            titleRange.CharacterFormat.FontSize = 15;
            title.Format.HorizontalAlignment = HorizontalAlignment.Center;

            //表格设置
            Table table = section.AddTable();
            table.ResetCells(record.Count + 1, 10);
            TableRow headerRow = table.Rows[0];
            headerRow.IsHeader = true;
            int i = 0;
            foreach (TableCell cell in docTemp_table.Rows[0].Cells)
            {
                var paragraph = headerRow.Cells[i].AddParagraph();
                TextRange textRange1 = paragraph.AppendText(cell.Paragraphs[0].Text);
                textRange1.CharacterFormat.FontName = "宋体";
                textRange1.CharacterFormat.FontSize = 10.5f;
                paragraph.Format.HorizontalAlignment = HorizontalAlignment.Center;
                headerRow.Cells[i].SetCellWidth(cell.GetCellWidth(), cell.GetCellWidthType());
                i += 1;
            }
            var num = 1;
            record.ForEach(item =>
            {
                var tableRow = table.Rows[num];
                var itemCell = docTemp_table.Rows[0]; ;
                tableRow.Cells[0].AddParagraph().AppendText($"{num}");
                tableRow.Cells[0].SetCellWidth(itemCell.Cells[0].GetCellWidth(), itemCell.Cells[0].GetCellWidthType());
                var a = itemCell.Cells[0].GetCellWidth();
                var b = itemCell.Cells[0].GetCellWidthType();
                tableRow.Cells[1].AddParagraph().AppendText(item.EQUIPMENT_NAME);
                tableRow.Cells[1].SetCellWidth(itemCell.Cells[1].GetCellWidth(), itemCell.Cells[1].GetCellWidthType());
                tableRow.Cells[2].AddParagraph().AppendText(item.EQUIPMENT_MODEL);
                tableRow.Cells[2].SetCellWidth(itemCell.Cells[2].GetCellWidth(), itemCell.Cells[2].GetCellWidthType());
                tableRow.Cells[3].AddParagraph().AppendText(item.SERIAL_NUMBER);
                tableRow.Cells[3].SetCellWidth(itemCell.Cells[3].GetCellWidth(), itemCell.Cells[3].GetCellWidthType());
                tableRow.Cells[4].AddParagraph().AppendText(item.DEPT_SECTION_NO);
                tableRow.Cells[4].SetCellWidth(itemCell.Cells[4].GetCellWidth(), itemCell.Cells[4].GetCellWidthType());
                tableRow.Cells[5].AddParagraph().AppendText(item.MANUFACTURER);
                tableRow.Cells[5].SetCellWidth(itemCell.Cells[5].GetCellWidth(), itemCell.Cells[5].GetCellWidthType());
                tableRow.Cells[6].AddParagraph().AppendText($"{Convert.ToDateTime(item.ENABLE_TIME).ToString("yyyy-MM-dd")}");
                tableRow.Cells[6].SetCellWidth(itemCell.Cells[6].GetCellWidth(), itemCell.Cells[6].GetCellWidthType());
                tableRow.Cells[7].AddParagraph().AppendText(item.INSTALL_AREA);
                tableRow.Cells[7].SetCellWidth(itemCell.Cells[7].GetCellWidth(), itemCell.Cells[7].GetCellWidthType());
                tableRow.Cells[8].AddParagraph().AppendText(item.MGROUP_NAME);
                tableRow.Cells[8].SetCellWidth(itemCell.Cells[8].GetCellWidth(), itemCell.Cells[8].GetCellWidthType());
                tableRow.Cells[9].AddParagraph().AppendText(item.KEEP_PERSON);
                tableRow.Cells[9].SetCellWidth(itemCell.Cells[9].GetCellWidth(), itemCell.Cells[9].GetCellWidthType());
                num += 1;
            });
            foreach (TableRow row in table.Rows)
            {
                row.Height = tableTemp_height;
                foreach (TableCell cell in row.Cells)
                {
                    cell.CellFormat.Borders.BorderType = Spire.Doc.Documents.BorderStyle.Single;
                    cell.CellFormat.VerticalAlignment = VerticalAlignment.Middle;
                    foreach (Paragraph paragraph in cell.Paragraphs)
                    {
                        foreach (DocumentObject obj in paragraph.ChildObjects)
                        {
                            if (obj is TextRange)
                            {
                                TextRange textRange2 = obj as TextRange;
                                textRange2.CharacterFormat.FontName = "宋体";
                                textRange2.CharacterFormat.FontSize = 10.5f;
                            }
                        }
                    }
                }
            }

            //页脚设置
            foreach (Section section1 in document.Sections)
            {
                HeaderFooter footer = section.HeadersFooters.Footer;
                Paragraph footerParagraph = footer.AddParagraph();
                TextRange foot1 = footerParagraph.AppendText("第 ");
                Field pageField = footerParagraph.AppendField("page number", FieldType.FieldPage);
                pageField.CharacterFormat.FontName = "宋体";
                pageField.CharacterFormat.FontSize = 12;
                TextRange foot2 = footerParagraph.AppendText(" 页  共 ");
                Field pageCountField = footerParagraph.AppendField("number of pages", FieldType.FieldSectionPages);
                pageCountField.CharacterFormat.FontName = "宋体";
                pageCountField.CharacterFormat.FontSize = 12;
                TextRange foot3 = footerParagraph.AppendText(" 页");
                footerParagraph.Format.HorizontalAlignment = HorizontalAlignment.Center;
                foot1.CharacterFormat.FontName = "宋体";
                foot1.CharacterFormat.FontSize = 9;
                foot2.CharacterFormat.FontName = "宋体";
                foot2.CharacterFormat.FontSize = 9;
                foot3.CharacterFormat.FontName = "宋体";
                foot3.CharacterFormat.FontSize = 9;
                footer.Paragraphs[0].Format.Borders.Top.BorderType = BorderStyle.Single;
            }

            //保存清单
            var docName = exportType == "1" ? "EquipmentList.docx" : "EquipmentList.pdf";
            var fileUrl = Path.Combine(AppContext.BaseDirectory, "ExampleFile", docName);
            if (exportType == "1")
            {
                document.SaveToFile(fileUrl, Spire.Doc.FileFormat.Docx);
            }
            else
            {
                document.SaveToFile(fileUrl, Spire.Doc.FileFormat.PDF);
            }
            return new ResultDto { success = true, data = fileUrl };
        }
    }
}
