﻿using H.Utility.SqlSugarInfra;
using Newtonsoft.Json;
using SqlSugar;

namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备档案记录字典表
/// </summary>
[DBOwner("XH_OA")]
[SugarTable("EMS_EQP_ARCHIVES_DICT", TableDescription = "设备档案记录字典表")]
public class EMS_EQP_ARCHIVES_DICT
{
    /// <summary>
    /// 档案记录ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true,ColumnName = "EQP_ARCHIVES_ID")]
    public string EqpArchivesId{ get; set; }
    
    /// <summary>
    /// 医疗机构ID
    /// </summary>
    [SugarColumn(ColumnName = "HOSPITAL_ID")]
    public string HospitalId{ get; set; }
    
    /// <summary>
    /// 档案记录父级ID;细分类的父级id 父级id可为空
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_PID")]
    public string? EqpArchivesPid{ get; set; }

    /// <summary>
    /// 档案记录父级名称;细分类的父级名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string EqpArchivesPName { get; set; }
    
    /// <summary>
    /// 档案记录类型;0:固定 1:扩展 默认0
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_TYPE")]
    public string EqpArchivesType{ get; set; }
    
    /// <summary>
    /// 档案名称
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_NAME")]
    public string EqpArchivesName{ get; set; }
    
    /// <summary>
    /// 档案顺序
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_SORT")]
    public double? EqpArchivesSort{ get; set; }
    
    /// <summary>
    /// 档案记录状态;0 禁用  1在用  2删除
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_STATE")]
    public string EqpArchivesState{ get; set; }
    
    /// <summary>
    /// 表单配置记录id
    /// </summary>
    [SugarColumn(ColumnName = "FORM_SETUP_ID")]
    public string? FormSetupId{ get; set; }
    
    /// <summary>
    /// 表格配置记录id
    /// </summary>
    [SugarColumn(ColumnName = "TABLE_SETUP_ID")]
    public string? TableSetupId{ get; set; }
    
    /// <summary>
    /// 用于存储不同档案记录的拓展功能：如是否上传附件
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_JSON")]
    public string? EqpArchivesJson{ get; set; }
    
    /// <summary>
    /// 首次登记人
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RPERSON")]
    public string? FIRST_RPERSON{ get; set; }
    
    /// <summary>
    /// 首次登记时间
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RTIME")]
    public DateTime? FIRST_RTIME{ get; set; }
    
    /// <summary>
    /// 最后修改人员
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MPERSON")]
    public string? LAST_MPERSON{ get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MTIME")]
    public DateTime? LAST_MTIME{ get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string? Remark{ get; set; }
    
    /// <summary>
    /// 关联关系
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToMany, nameof(EMS_EQP_CLASS_ARCHIVES.EqpArchivesId))]
    public List<EMS_EQP_CLASS_ARCHIVES> ClassArchives { get; set; }
     /// <summary>
    /// 创建固定数据
    /// </summary>
    /// <param name="hospitalId"></param>
    /// <returns></returns>
    public static List<EMS_EQP_ARCHIVES_DICT> CreatInit(string hospitalId)
    {
        var result =  new List<EMS_EQP_ARCHIVES_DICT>
        {
            new()
            {
                EqpArchivesId = "H82JBXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "基本信息",
                EqpArchivesSort = 1,
                EqpArchivesState = "1",
                TableSetupId = "H8205612", 
                FormSetupId = "H825301",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82EQPARCHIVESHJXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "环境信息",
                EqpArchivesSort = 2,
                EqpArchivesState = "1",
                TableSetupId = "", 
                FormSetupId = "H8205614",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82GYSXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "供应商信息",
                EqpArchivesSort = 3,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82SWXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "商务信息",
                EqpArchivesSort = 4,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82AZXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "安装信息",
                EqpArchivesSort = 5,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82QTXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "启停信息",
                EqpArchivesSort = 6,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BFXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "报废信息",
                EqpArchivesSort = 7,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82ZZZSXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "资质证书",
                EqpArchivesSort = 8,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82SBSMSXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "设备说明书",
                EqpArchivesSort = 9,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82SOPDAXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "基本信息",
                EqpArchivesSort = 10,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BSKXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "设备标识卡",
                EqpArchivesSort = 11,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82YXJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "0",
                EqpArchivesType = "0",
                EqpArchivesName = "运行记录",
                EqpArchivesSort = 12,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82SYJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "使用记录",
                EqpArchivesSort = 13,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BYJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "保养记录",
                EqpArchivesSort = 14,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82WXJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "基本信息",
                EqpArchivesSort = 15,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82JZLLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "校准记录",
                EqpArchivesSort = 16,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BDJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "比对记录",
                EqpArchivesSort = 17,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82XNYZJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "性能验证记录",
                EqpArchivesSort = 18,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82QWRJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "去污染记录",
                EqpArchivesSort = 19,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BGJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "变更记录",
                EqpArchivesSort = 20,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            },
            new()
            {
                EqpArchivesId = "H82BLSJJLXX",
                HospitalId = hospitalId,
                EqpArchivesPid = "H82YXJLXX",
                EqpArchivesType = "0",
                EqpArchivesName = "不良时间",
                EqpArchivesSort = 21,
                EqpArchivesState = "1",
                Remark = "初始化数据"
            }
        };

        var num = 0 ;
        foreach (var item in result)
        {
            var exObject = new  EqpArchivesExt{ IsUpload = true };
            item.EqpArchivesJson = JsonConvert.SerializeObject(exObject);
            item.EqpArchivesSort = ++num;
            item.FIRST_RTIME = DateTime.Now;
            item.LAST_MTIME = DateTime.Now;
            item.FIRST_RPERSON = "H82初始化";
            item.LAST_MPERSON = "H82初始化";
        }
        return result;
    }
}