﻿using H.BASE.SqlSugarInfra.Uow;
using H.U<PERSON>;
using Newtonsoft.Json;
using Serilog;
using SqlSugar;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Card;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.Entities.WarnRecord;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.InkScreenTemplate;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;
using SYS6_HOSPITAL_INFO = XH.LAB.UTILS.Models.SYS6_HOSPITAL_INFO;

namespace XH.H82.Services.DeviceDataRefresh
{
    /// <summary>
    /// 设备数据上下文
    /// </summary>
    public class EquipmentContext
    {
        const string KEY = "H820101";
        public List<EMS_DOC_INFO> _docs = new();

        public List<CertificatDto> _certificats = new();

        public List<EquipmentDisplay> equipmentDisplays = new();

        public List<EMS_EQUIPMENT_INFO> equipments = new();
        
        public List<EMS_EQUIPMENT_CLASS_DICT> _Classes = new();

        private bool? _NeedReviewProcess { get; set; } = null;

        #region 缓存数据
        private List<EMS_OPER_LOG> _logRecords = new();

        private List<SYS6_BASE_DATA> _baseData = new();

        private List<SYS6_INSPECTION_PGROUP> _inspectionPgroups = new();

        private List<EMS_SUBSCRIBE_INFO> _subscribes = new();

        private List<SYS6_COMPANY_CONTACT> _companyContacts = new();

        private List<SYS6_INSPECTION_MGROUP> _inspectionMgroups = new();

        private List<SYS6_HOSPITAL_INFO> _hospitalInfo = new();

        private List<SYS6_INSPECTION_LAB> _inspectionLab = new();
        
        private List<SYS6_INSPECTION_AREA> _areas = new();
        
        private List<SMBL_LAB> _smblLabs = new();
        #endregion

        private EquipmentWarnInfoDto equipmentWarnInfo = new();

        private string? _hosptalId = null;
        private string? _labId = null;
        
        private bool NeedReviewProcess
        {
            get
            {
                if (!_NeedReviewProcess.HasValue)
                {
                    var setUpValue = _dbContext.Db.Queryable<SYS6_SETUP>()
                        .Where(x => x.SETUP_NO == KEY)
                        .First();
                    if (setUpValue is not null)
                    {
                        _NeedReviewProcess = setUpValue.SETUP_VALUE == "1";
                        return setUpValue.SETUP_VALUE == "1";
                    }
                    else
                    {
                        var setUpValue2 = _dbContext.Db.Queryable<SYS6_SETUP_DICT>()
                            .Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1")
                            .First();
                        if (setUpValue2 is not null)
                        {
                            _NeedReviewProcess = setUpValue2.DEFAULT_VALUE == "1";
                            return  setUpValue2.DEFAULT_VALUE == "1";
                        }
                    }
                    return true; 
                }
                else
                {
                    return _NeedReviewProcess.Value;
                }
            }
        }

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public EquipmentContext(ISqlSugarUow<SugarDbContext_Master> dbContext)
        {
            _dbContext = dbContext;
        }
        


        #region 初始化设置

        /// <summary>
        /// 设定设备范围
        /// </summary>
        /// <param name="hosptalId"></param>
        /// <param name="labId"></param>
        public void SetHosptalIdAndLabId(string? hosptalId, string? labId)
        {
            _hosptalId = hosptalId;
            _labId = labId;
        }
        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="equipments"></param>
        public void Injection(List<EMS_EQUIPMENT_INFO> equipments = null)
        {
            if (equipments is null)
            {
                equipments = new List<EMS_EQUIPMENT_INFO>();
                _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                          .WhereIF(!_hosptalId.IsNullOrEmpty(), x => x.HOSPITAL_ID == _hosptalId)
                          .WhereIF(!_labId.IsNullOrEmpty(), x => x.LAB_ID == _labId)
                          .Includes(i => i.eMS_INSTALL_INFO)
                          .Includes(i => i.eMS_PURCHASE_INFO)
                          .Includes(i => i.eMS_WORK_PLAN)
                          .Includes(i => i.eMS_MAINTAIN_INFO)
                          .Includes(i => i.eMS_CORRECT_INFO)
                          .Includes(i => i.eMS_COMPARISON_INFO)
                          .Includes(i => i.eMS_VERIFICATION_INFO)
                          .Includes(i => i.eMS_AUTHORIZE_INFO)
                          .Includes(i => i.eMS_CHANGE_INFO)
                          .Includes(i => i.eMS_DEBUG_INFO)
                          .Includes(i => i.eMS_PARTS_INFO)
                          .Includes(i => i.eMS_REPAIR_INFO)
                          .Includes(i => i.eMS_SCRAP_INFO)
                          .Includes(i => i.eMS_START_STOP)
                          .Includes(i => i.eMS_STARTUP_INFO)
                          .Includes(i => i.eMS_TRAIN_INFO)
                          .Includes(i => i.eMS_UNPACK_INFO)
                          .Includes(i => i.WarnRecords)
                          .Includes(i => i.Inkscreen)
                          .Includes(i => i.Contacts)
                          .Includes(i => i.eMS_ENVI_REQUIRE_INFO)
                          .ForEach(it => equipments.Add(it)); //每次查询100条
            }
            this.equipments.Clear();
            this.equipments.AddRange(equipments);
            foreach (var equipment in equipments)
            {
                CalculateEquipmentWorkPlanNextTime(equipment);
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="equipments"></param>
        public void Injection(string equipmentId)
        {
            var equipments = new List<EMS_EQUIPMENT_INFO>();
            if (equipmentId is not null)
            {
                _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                          .WhereIF(!equipmentId.IsNullOrEmpty(), x => x.EQUIPMENT_ID == equipmentId)
                          .WhereIF(!_hosptalId.IsNullOrEmpty(), x => x.HOSPITAL_ID == _hosptalId)
                          .WhereIF(!_labId.IsNullOrEmpty(), x => x.LAB_ID == _labId)
                          .Includes(i => i.eMS_INSTALL_INFO)
                          .Includes(i => i.eMS_PURCHASE_INFO)
                          .Includes(i => i.eMS_WORK_PLAN)
                          .Includes(i => i.eMS_MAINTAIN_INFO)
                          .Includes(i => i.eMS_CORRECT_INFO)
                          .Includes(i => i.eMS_COMPARISON_INFO)
                          .Includes(i => i.eMS_VERIFICATION_INFO)
                          .Includes(i => i.eMS_AUTHORIZE_INFO)
                          .Includes(i => i.eMS_CHANGE_INFO)
                          .Includes(i => i.eMS_DEBUG_INFO)
                          .Includes(i => i.eMS_PARTS_INFO)
                          .Includes(i => i.eMS_REPAIR_INFO)
                          .Includes(i => i.eMS_SCRAP_INFO)
                          .Includes(i => i.eMS_START_STOP)
                          .Includes(i => i.eMS_STARTUP_INFO)
                          .Includes(i => i.eMS_TRAIN_INFO)
                          .Includes(i => i.eMS_UNPACK_INFO)
                          .Includes(i => i.WarnRecords)
                          .Includes(i => i.Contacts)
                          .Includes(i => i.eMS_ENVI_REQUIRE_INFO)
                          .ForEach(it => equipments.Add(it), 200); //每次查询200条
            }
            this.equipments.Clear();
            this.equipments.AddRange(equipments);
            foreach (var equipment in equipments)
            {
                CalculateEquipmentWorkPlanNextTime(equipment);
            }

        }
        #endregion

        #region 获取设备数据
        public EMS_EQUIPMENT_INFO? GetEquipment(string equipmentId)
        {
            return equipments.FirstOrDefault(x => x.EQUIPMENT_ID == equipmentId);
        }


        /// <summary>
        /// 获取设备不同报警类型分类下的设备信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public EquipmentWarnInfoDto GetEquipmentWarnInfo(string? hospitalId, string? labId)
        {

            SetHosptalIdAndLabId(hospitalId, null);
            Injection();

            foreach (var equipment in equipments)
            {
                AddEquipmentWarnInfo(equipment);
            }

            return equipmentWarnInfo;
        }

        /// <summary>
        ///  按报警信息分类设备
        /// </summary>
        /// <param name="equipment"></param>
        private void AddEquipmentWarnInfo(EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment is null)
            {
                return;
            }
            if (equipment.EQUIPMENT_STATE == "2" || equipment.EQUIPMENT_STATE == "3")
            {
                equipmentWarnInfo.FaultRecords.Add(new()
                {
                    Summary = EquipmentSummaryEnum.Fault,
                    EquipmentName = equipment.EQUIPMENT_NAME,
                    EquipmentCode = equipment.EQUIPMENT_CODE,
                    EquipmentId = equipment.EQUIPMENT_ID,
                    EquipmentModel = equipment.EQUIPMENT_MODEL,
                    GroupId = equipment.UNIT_ID
                });
                return;
            }
            var earlyWarningInfo = ConutEarlyWarningDate(equipment);
            if (earlyWarningInfo.MaintenanceState is not TaskStatusEnum.NoReminder)
            {
                equipmentWarnInfo.MaintenanceRecords.Add(new()
                {
                    Summary = EquipmentSummaryEnum.Maintenance,
                    EquipmentName = equipment.EQUIPMENT_NAME,
                    EquipmentCode = equipment.EQUIPMENT_CODE,
                    EquipmentId = equipment.EQUIPMENT_ID,
                    EquipmentModel = equipment.EQUIPMENT_MODEL,
                    GroupId = equipment.UNIT_ID
                });
            }
            if (earlyWarningInfo.ComparisonState is not TaskStatusEnum.NoReminder)
            {
                equipmentWarnInfo.ComparisonRecords.Add(new()
                {
                    Summary = EquipmentSummaryEnum.Comparison,
                    EquipmentName = equipment.EQUIPMENT_NAME,
                    EquipmentCode = equipment.EQUIPMENT_CODE,
                    EquipmentId = equipment.EQUIPMENT_ID,
                    EquipmentModel = equipment.EQUIPMENT_MODEL,
                    GroupId = equipment.UNIT_ID
                });
            }
            if (earlyWarningInfo.CorrectState is not TaskStatusEnum.NoReminder)
            {
                equipmentWarnInfo.CorrectRecords.Add(new()
                {
                    Summary = EquipmentSummaryEnum.Correct,
                    EquipmentName = equipment.EQUIPMENT_NAME,
                    EquipmentCode = equipment.EQUIPMENT_CODE,
                    EquipmentId = equipment.EQUIPMENT_ID,
                    EquipmentModel = equipment.EQUIPMENT_MODEL,
                    GroupId = equipment.UNIT_ID
                });
            }
        }

        #endregion

        /// <summary>
        /// 计算处理工作计划各种提醒状态
        /// </summary>
        /// <param name="equipment"></param>
        /// <returns></returns>
        public EquipmentDisplay ConutEarlyWarningDate(EMS_EQUIPMENT_INFO equipment)
        {
            var date = DateTime.Now;

            var equipmentState = ExchangeEquipmentStateEnum(equipment.EQUIPMENT_STATE);

            var result = new EquipmentDisplay()
            {
                Id = IDGenHelper.CreateGuid(),
                EquipmentId = equipment.EQUIPMENT_ID,
                State = equipmentState.ToDesc(),
                EquipmentState = equipmentState,
            };
            var startOrstopRecord = GetEquipmentStartOrStopRecord(equipment);
            if (startOrstopRecord is not null)
            {
                result.ChangeStateTime = startOrstopRecord.START_DATE;
            }
            else
            {
                result.ChangeStateTime = equipment.LAST_MTIME;
            }

            if (equipment.eMS_WORK_PLAN is not null)
            {
                if (equipment.NEXT_MAINTAIN_DATE.HasValue)
                {
                    var NextMaintain = CompareNext(equipment);
                    if (NextMaintain is null)
                    {
                        NextMaintain = new EMS_MAINTAIN_INFO() { EQUIPMENT_ID = equipment.EQUIPMENT_ID, MAINTAIN_DATE = DateTime.Now, MAINTAIN_CYCLE = "" };
                    }
                    var maintenanceDate = (equipment.NEXT_MAINTAIN_DATE.Value - date).Days;
                    var warnData = NextMaintain.MAINTAIN_CYCLE switch
                    {
                        "月" => equipment.eMS_WORK_PLAN.MONTHLY_MAINTAIN_WARN,
                        "季度" => equipment.eMS_WORK_PLAN.QUARTERLY_MAINTAIN_WARN,
                        "年" => equipment.eMS_WORK_PLAN.YEARLY_MAINTAIN_WARN,
                        "半年" => equipment.eMS_WORK_PLAN.YEARLY_MAINTAIN_WARN,
                        "周" => equipment.eMS_WORK_PLAN.MAINTAIN_WARN_INTERVALS,
                        _ => "0"
                    };
                    if (warnData.IsNullOrEmpty())
                    {
                        warnData = "0";
                    }
                    var warn = maintenanceDate - int.Parse(warnData);


                    if (maintenanceDate < 0)
                    {
                        result.MaintenanceState = TaskStatusEnum.Alarm;
                        result.MaintenanceDate = Math.Abs(maintenanceDate);
                    }
                    else
                    {

                        if (warn >= 0)
                        {
                            result.MaintenanceState = TaskStatusEnum.NoReminder;
                            result.MaintenanceDate = Math.Abs(maintenanceDate);
                        }
                        else
                        {
                            result.MaintenanceState = TaskStatusEnum.Warning;
                            result.MaintenanceDate = Math.Abs(maintenanceDate) + 1;
                        }

                    }
                    result.MaintenanceType = NextMaintain.MAINTAIN_CYCLE;
                }
                if (equipment.NEXT_CORRECT_DATE.HasValue)
                {

                    var warnData = equipment.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS;
                    if (warnData.IsNullOrEmpty())
                    {
                        warnData = "0";
                    }
                    var corrctDate = (equipment.NEXT_CORRECT_DATE.Value - date).Days;
                    var warn = corrctDate - int.Parse(warnData);

                    if (corrctDate < 0)
                    {
                        result.CorrectState = TaskStatusEnum.Alarm;
                        result.CorrectDate = Math.Abs(corrctDate);
                    }
                    else
                    {

                        if (warn >= 0)
                        {
                            result.CorrectState = TaskStatusEnum.NoReminder;
                            result.CorrectDate = Math.Abs(corrctDate);
                        }
                        else
                        {
                            result.CorrectState = TaskStatusEnum.Warning;
                            result.CorrectDate = Math.Abs(corrctDate) + 1;
                        }

                    }
                }
                if (equipment.NEXT_COMPARISON_DATE.HasValue)
                {
                    var warnData = equipment.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS;
                    if (warnData.IsNullOrEmpty())
                    {
                        warnData = "0";
                    }
                    var comparisonDate = (equipment.NEXT_COMPARISON_DATE.Value - date).Days;
                    var warn = comparisonDate - int.Parse(warnData);
                    if (comparisonDate < 0)
                    {
                        result.ComparisonState = TaskStatusEnum.Alarm;
                        result.ComparisonDate = Math.Abs(comparisonDate);
                    }
                    else
                    {

                        if (warn >= 0)
                        {
                            result.ComparisonState = TaskStatusEnum.NoReminder;
                            result.ComparisonDate = Math.Abs(comparisonDate);
                        }
                        else
                        {
                            result.ComparisonState = TaskStatusEnum.Warning;
                            result.ComparisonDate = Math.Abs(comparisonDate) + 1;
                        }

                    }

                }
            }
            if (equipment.MANUFACTURER_ID is not null)
            {
                var certificats = _certificats.Where(x => x.CompanyId == equipment.MANUFACTURER_ID).ToList();
                foreach (var certificat in certificats)
                {
                    if (equipment.MANUFACTURER.IsNotNullOrEmpty())
                    {
                        var data = certificat.GetCertificatStateInfo($"制造商{equipment.MANUFACTURER}");
                        result.Certificates.Add(certificat.GetCertificatStateInfo($"制造商{equipment.MANUFACTURER}"));
                    }
                }

            }
            if (equipment.DEALER_ID is not null)
            {
                var certificats = _certificats.Where(x => x.CompanyId == equipment.DEALER_ID).ToList();
                foreach (var certificat in certificats)
                {
                    if (equipment.DEALER.IsNotNullOrEmpty())
                    {
                        var data = certificat.GetCertificatStateInfo($"经销商{equipment.DEALER}");
                        result.Certificates.Add(data);
                    }
                }
            }

            equipmentDisplays.Add(result);
            return result;
        }

        /// <summary>
        /// 处理报警信息合并
        /// </summary>
        /// <param name="warnRecords"></param>
        /// <returns></returns>
        public (int warnDuration, DateTime? warnTime, string warnMsgs) CombinedWarnInfo(List<EMS_EQUIPMENT_WARN> warnRecords)
        {
            int warnDuration = 0;
            DateTime? warnTime = null;
            var warnMsgs = "";

            if (warnRecords is null)
            {
                return (warnDuration, warnTime, warnMsgs);
            }
            var records = warnRecords.Where(x => x.DISPOSE_STATE is DealWithStateEnum.Untreated).OrderByDescending(x => x.WARN_TIME).ToList();
            foreach (var warnRecord in records)
            {
                if (warnRecord.DISPOSE_STATE is DealWithStateEnum.Untreated)
                {
                    if (warnRecord.WARN_MSG.Contains("超期") || warnRecord.WARN_MSG.Contains("过期"))
                    {
                        if (warnRecord.WARN_MSG.Contains("超期"))
                        {
                            var operationName = warnRecord.WARN_MSG.Split("超期");
                            warnMsgs += $"需进行{operationName[0]};";
                        }
                        else
                        {
                            var operationName = warnRecord.WARN_MSG.Split("过期");
                            warnMsgs += $"{operationName[0]}需要更新;";
                        }
                    }
                    else
                    {
                        warnMsgs += $"{warnRecord.WARN_MSG};";
                    }

                }
            }
            if (records.Count() > 0)
            {
                warnTime = records[0].WARN_TIME;
                warnDuration = (int)(DateTime.Now - warnTime).Value.TotalHours;
            }
            return (warnDuration, warnTime, warnMsgs);
        }

        public void AutoAllEarlyWarning()
        {
            equipmentDisplays.Clear();
            foreach (var equipment in equipments)
            {
                ConutEarlyWarningDate(equipment);
            }
            CreateWarnRecords();
        }

        public void CreateWarnRecords()
        {
            foreach (var equipmentDisplay in equipmentDisplays)
            {
                CreateWarnRecord(equipmentDisplay);
            }
            foreach (var equipmentDisplay in equipmentDisplays)
            {
                WarnRecordDealWith(equipmentDisplay);
            }
        }

        /// <summary>
        /// 刷新水墨屏信息
        /// </summary>
        /// <param name="h115Client"></param>
        public void RefreshCradInfo(H115Client h115Client)
        {
            try
            {
                var hasInkscreenEquipments = equipments.Where(x => x.Inkscreen is not null);
                if (hasInkscreenEquipments.Count() > 0)
                {
                    foreach (var equipment in hasInkscreenEquipments)
                    {
                        var cardInfo = ExchangeIdentificationCard(equipment.EQUIPMENT_ID);

                        if (equipment.Inkscreen.REMARK == cardInfo.meassage)
                        {
                            continue;
                        }
                        if (cardInfo.meassage.IsNullOrEmpty())
                        {
                            continue;
                        }
                        if (cardInfo.equipmentName.IsNullOrEmpty())
                        {
                            Log.Error(JsonConvert.SerializeObject(cardInfo));
                            continue;
                        }

                        var rsp = h115Client.ChangeDisplay(cardInfo.ToEinkChangeDisplay(equipment.Inkscreen.MAC, isuploadFile: true));
                        if (rsp.success)
                        {
                            Log.Information($"墨水屏应用图片:{rsp.data}|||{rsp.data1}|||{rsp.data2}");
                            equipment.Inkscreen.REMARK = $"{cardInfo.meassage}";
                            _dbContext.Db.Updateable(equipment.Inkscreen).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
                        }
                    }
                }
            }
            catch
            {
                return;
            }
        }

        /// <summary>
        /// 查询设备分类
        /// </summary>
        /// <returns></returns>
        public List<SYS6_BASE_DATA> GetEquipmentClasses()
        {
            if (_baseData.Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1").Count() == 0)
            {
                var basedatas = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                    .Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1")
                    .ToList();
                _baseData.AddRange(basedatas);
            }
            return _baseData.Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1").ToList();
        }

        /// <summary>
        /// 获取供应商证书
        /// </summary>
        /// <param name="h92Client"></param>
        /// <returns></returns>
        public List<CertificatDto> LoadCertificates(H92Client h92Client)
        {
            _certificats.Clear();
            if (_certificats.Count() == 0)
            {
                var certificates = h92Client.GetCompanyCerlist(_dbContext);

                var list = certificates.GroupBy(x => new { x.CompanyId, x.FileTypeName }).ToList();
                foreach (var item in list)
                {
                    if (item.FirstOrDefault() is not null)
                    {
                        _certificats.Add(item.FirstOrDefault());
                    }
                }
            }
            return _certificats;
        }

        /// <summary>
        /// 根据设备反馈的信息生成报警信息
        /// </summary>
        /// <param name="equipmentDisplay"></param>
        private void CreateWarnRecord(EquipmentDisplay equipmentDisplay)
        {
            var dateTime = DateTime.Now;
            var equipment = GetEquipment(equipmentDisplay.EquipmentId);
            var warnRecords = equipment.WarnRecords.OrderByDescending(x => x.WARN_TIME).Where(x => x.DISPOSE_STATE == DealWithStateEnum.Untreated);

            if (equipment.EQUIPMENT_STATE == "4" || equipment.EQUIPMENT_STATE == "5")
            {
                return;
            }

            if (equipment.EQUIPMENT_STATE == "2")
            {
                var preScrapp = equipment.eMS_SCRAP_INFO
                    .OrderByDescending(x => x.OPER_TIME)
                    .Where(x => x.SCRAP_DATE != null)
                    .Where(x => x.APPLY_TYPE == "停用")
                    .Where(x => x.APPLY_STATE == "3")
                    .FirstOrDefault(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                if (preScrapp is null)
                {
                    return;
                }
                var faultRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Fault).FirstOrDefault();
                var msg = $"设备已于{preScrapp.SCRAP_DATE!.Value.ToString("yyyy-MM-dd")}由{preScrapp.FIRST_RPERSON}提交停用申请";

                BuilderWarnRecord(equipment, faultRecord, EquipmentSummaryEnum.Fault, msg);
                return;
            }
            if (equipment.EQUIPMENT_STATE == "3")
            {
                var preScrap = equipment.eMS_SCRAP_INFO
                    .OrderByDescending(x => x.OPER_TIME)
                    .Where(x => x.SCRAP_DATE != null)
                    .Where(x => x.APPLY_TYPE == "报废")
                    .Where(x => x.APPLY_STATE == "3")
                    .FirstOrDefault(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                if (preScrap is null)
                {
                    return;
                }
                var faultRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Fault).FirstOrDefault();
                var msg = $"设备已于{preScrap.SCRAP_DATE!.Value.ToString("yyyy-MM-dd")}由{preScrap.FIRST_RPERSON}提交报废申请";

                BuilderWarnRecord(equipment, faultRecord, EquipmentSummaryEnum.Fault, msg);
                return;
            }


            if (equipmentDisplay.MaintenanceState is TaskStatusEnum.Warning)
            {
                var MaintenanceWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Maintenance).FirstOrDefault();
                var msg = $"设备下次保养剩余{equipmentDisplay.MaintenanceDate}天";
                BuilderWarnRecord(equipment, MaintenanceWarnRecord, EquipmentSummaryEnum.Maintenance, msg);
            }
            if (equipmentDisplay.MaintenanceState is TaskStatusEnum.Alarm)
            {
                var MaintenanceWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Maintenance).FirstOrDefault();
                var msg = $"设备保养超期{equipmentDisplay.MaintenanceDate}天";
                BuilderWarnRecord(equipment, MaintenanceWarnRecord, EquipmentSummaryEnum.Maintenance, msg);
            }
            if (equipmentDisplay.ComparisonState is TaskStatusEnum.Alarm)
            {
                var ComparisonWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Comparison).FirstOrDefault();
                var msg = $"设备比对超期{equipmentDisplay.ComparisonDate}天";
                BuilderWarnRecord(equipment, ComparisonWarnRecord, EquipmentSummaryEnum.Comparison, msg);

            }
            if (equipmentDisplay.ComparisonState is TaskStatusEnum.Warning)
            {
                var ComparisonWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Comparison).FirstOrDefault();
                var msg = $"设备比对剩余{equipmentDisplay.ComparisonDate}天";
                BuilderWarnRecord(equipment, ComparisonWarnRecord, EquipmentSummaryEnum.Comparison, msg);
            }
            if (equipmentDisplay.CorrectState is TaskStatusEnum.Alarm)
            {
                var CorrectWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Correct).FirstOrDefault();
                var msg = $"设备校准超期{equipmentDisplay.CorrectDate}天";
                BuilderWarnRecord(equipment, CorrectWarnRecord, EquipmentSummaryEnum.Correct, msg);
            }
            if (equipmentDisplay.CorrectState is TaskStatusEnum.Warning)
            {
                var CorrectWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Correct).FirstOrDefault();
                var msg = $"设备校准剩余{equipmentDisplay.CorrectDate}天";
                BuilderWarnRecord(equipment, CorrectWarnRecord, EquipmentSummaryEnum.Correct, msg);
            }
            if (equipmentDisplay.Certificates.Count() > 0)
            {
                foreach (var certificate in equipmentDisplay.Certificates)
                {
                    if (certificate.CertificateState is not TaskStatusEnum.NoReminder)
                    {
                        var certificateWarnRecord = warnRecords
                            .Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Certificate)
                            .Where(x => x.WARN_MSG.Contains(certificate.CertificateType))
                            .Where(x => x.REMARK == certificate.CompanyId)
                            .FirstOrDefault();
                        BuilderWarnRecord(equipment, certificateWarnRecord, EquipmentSummaryEnum.Certificate, certificate.ToWarnMsg(), certificate.CompanyId);
                    }
                }
            }
        }

        private void WarnRecordDealWith(EquipmentDisplay equipmentDisplay)
        {

            try
            {
                var equipment = GetEquipment(equipmentDisplay.EquipmentId);
                var warnRecords = equipment.WarnRecords
                    .OrderByDescending(x => x.WARN_TIME)
                    .Where(x => x.DISPOSE_STATE == DealWithStateEnum.Untreated);

                if (equipmentDisplay.EquipmentState is EquipmentStateEnum.Deactivated)
                {
                    var deactivatedRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Fault).FirstOrDefault();
                    BuilderWarnRecordDealWithDeactivatedRecord(equipment, deactivatedRecord);
                }

                if (equipmentDisplay.EquipmentState is EquipmentStateEnum.Scrapped)
                {
                    var deactivatedRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Fault).FirstOrDefault();
                    BuilderWarnRecordDealWithScrappedRecord(equipment, deactivatedRecord);
                }

                if (equipmentDisplay.MaintenanceState is TaskStatusEnum.NoReminder)
                {
                    if (equipment.LAST_MAINTAIN_DATE.HasValue)
                    {
                        var MaintenanceWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Maintenance).FirstOrDefault();

                        if (MaintenanceWarnRecord is not null)
                        {
                            BuilderWarnRecordDealWithMaintain(equipment, MaintenanceWarnRecord);
                        }
                    }
                }

                if (equipmentDisplay.ComparisonState is TaskStatusEnum.NoReminder)
                {
                    if (equipment.LAST_COMPARISON_DATE.HasValue)
                    {
                        var comparisonWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Comparison).FirstOrDefault();
                        if (comparisonWarnRecord is not null)
                        {
                            BuilderWarnRecordDealWithComparison(equipment, comparisonWarnRecord);
                        }
                    }
                }

                if (equipmentDisplay.CorrectState is TaskStatusEnum.NoReminder)
                {
                    if (equipment.LAST_CORRECT_DATE.HasValue)
                    {
                        var CorrectWarnRecord = warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Correct).FirstOrDefault();
                        if (CorrectWarnRecord is not null)
                        {
                            BuilderWarnRecordDealWithCorrect(equipment, CorrectWarnRecord);
                        }
                    }
                }

                foreach (var warnRecord in warnRecords.Where(x => x.WARN_TYPE == EquipmentSummaryEnum.Certificate))
                {
                    BuilderWarnRecordDealWithCertificateRecord(equipmentDisplay.Certificates, warnRecord);
                }

            }
            catch (Exception e)
            {
                Log.Error(e.ToString());
                return;
            }

        }

        private void BuilderWarnRecordDealWithCertificateRecord(List<CertificateWarn> certificateWarns, EMS_EQUIPMENT_WARN record)
        {
            var certificateWarn = certificateWarns.Where(x => x.CompanyId == record.REMARK).Where(x => record.WARN_MSG.Contains(x.CertificateType)).FirstOrDefault();
            if (certificateWarn is null)
            {
                _dbContext.Db.Deleteable(record).ExecuteCommand();
            }
            else
            {
                if (certificateWarn.CertificateState is TaskStatusEnum.NoReminder)
                {
                    if (certificateWarn.CompanyId == record.REMARK)
                    {
                        record.DISPOSE_PERSON = certificateWarn.UpdatePerson;
                        record.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                        record.DISPOSE_TIME = DateTime.Now;
                        _dbContext.Db.Updateable(record).ExecuteCommand();
                    }

                }
            }
        }

        private void BuilderWarnRecord(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN? record, EquipmentSummaryEnum equipmentSummary, string msg, string reramk = "")
        {
            var dateTime = DateTime.Now;
            if (record is null)
            {
                var warnRecord = new EMS_EQUIPMENT_WARN()
                {
                    WARN_RID = IDGenHelper.CreateGuid(),
                    EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                    WARN_TYPE = equipmentSummary,
                    WARN_MSG = msg,
                    WARN_TIME = dateTime,
                    DISPOSE_PERSON = "",
                    DISPOSE_TIME = null,
                    DISPOSE_STATE = DealWithStateEnum.Untreated,
                    FIRST_RPERSON = "系统检测",
                    FIRST_RTIME = dateTime,
                    LAST_MPERSON = "系统检测",
                    LAST_MTIME = dateTime,
                    REMARK = reramk
                };
                _dbContext.Db.Insertable(warnRecord).ExecuteCommand();
            }
            else
            {
                if ((record.WARN_TIME! - dateTime).Value.Days > 3)
                {
                    record.WARN_TIME = dateTime;
                }
                if (record.WARN_MSG != msg)
                {
                    record.WARN_MSG = msg;
                    _dbContext.Db.Updateable(record).ExecuteCommand();
                }
            }
        }


        private void BuilderWarnRecordDealWithDeactivatedRecord(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN record)
        {

            if (record is null)
            {
                return;
            }
            if (equipment.eMS_SCRAP_INFO is not null)
            {
                var scrap = equipment.eMS_SCRAP_INFO.OrderByDescending(x => x.FIRST_RTIME)
                    .Where(x => x.APPLY_TYPE == "停用")
                    .Where(x => x.APPLY_STATE == "3")
                    .FirstOrDefault(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                if (scrap is not null)
                {
                    record.DISPOSE_PERSON = scrap.FIRST_RPERSON;
                    record.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                    record.DISPOSE_TIME = DateTime.Now;
                    _dbContext.Db.Updateable(record).ExecuteCommand();
                }
            }

        }


        private void BuilderWarnRecordDealWithScrappedRecord(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN record)
        {
            if (record is null)
            {
                return;
            }
            if (equipment.eMS_SCRAP_INFO is not null)
            {
                var scrap = equipment.eMS_SCRAP_INFO.OrderByDescending(x => x.FIRST_RTIME)
                    .Where(x => x.APPLY_TYPE == "报废")
                    .Where(x => x.APPLY_STATE == "3")
                    .FirstOrDefault(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                if (scrap is not null)
                {
                    record.DISPOSE_PERSON = scrap.FIRST_RPERSON;
                    record.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                    record.DISPOSE_TIME = DateTime.Now;
                    _dbContext.Db.Updateable(record).ExecuteCommand();
                }
            }

        }

        private void BuilderWarnRecordDealWithCorrect(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN record)
        {
            if (record is null)
            {
                return;
            }
            if (record.WARN_TIME <= equipment.LAST_CORRECT_DATE)
            {
                var correct = equipment.eMS_CORRECT_INFO.OrderByDescending(x => x.LAST_MTIME).FirstOrDefault();

                var recordData = _dbContext.Db.Queryable<EMS_EQUIPMENT_WARN>().First(x => x.WARN_RID == record.WARN_RID);
                recordData.DISPOSE_PERSON = correct.CORRECT_PERSON;
                recordData.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                recordData.DISPOSE_TIME = DateTime.Now;
                _dbContext.Db.Updateable(recordData).ExecuteCommand();
            }
        }


        private void BuilderWarnRecordDealWithComparison(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN record)
        {
            if (record is null)
            {
                return;
            }
            if (record.WARN_TIME <= equipment.LAST_COMPARISON_DATE)
            {
                var comparison = equipment.eMS_COMPARISON_INFO.OrderByDescending(x => x.LAST_MTIME).FirstOrDefault();
                var recordData = _dbContext.Db.Queryable<EMS_EQUIPMENT_WARN>().First(x => x.WARN_RID == record.WARN_RID);
                recordData.DISPOSE_PERSON = comparison.COMPARISON_PERSON ?? "";
                recordData.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                recordData.DISPOSE_TIME = DateTime.Now;
                _dbContext.Db.Updateable(recordData).ExecuteCommand();
            }
        }


        private void BuilderWarnRecordDealWithMaintain(EMS_EQUIPMENT_INFO equipment, EMS_EQUIPMENT_WARN record)
        {
            if (record is null)
            {
                return;
            }
            if (record.WARN_TIME <= equipment.LAST_MAINTAIN_DATE)
            {
                var maintain = equipment.eMS_MAINTAIN_INFO.OrderByDescending(x => x.LAST_MTIME).FirstOrDefault();
                var recordData = _dbContext.Db.Queryable<EMS_EQUIPMENT_WARN>().First(x => x.WARN_RID == record.WARN_RID);
                recordData.DISPOSE_PERSON = maintain.MAINTAIN_PERSON ?? "";
                recordData.DISPOSE_STATE = DealWithStateEnum.pProcessed;
                recordData.DISPOSE_TIME = DateTime.Now;
                _dbContext.Db.Updateable(recordData).ExecuteCommand();
            }

        }


        #region 状态、类型、枚举等数据处理


        public string ExChangeMGroupByPGroupId( string pgroupId)
        {
            var result = "";
            if (pgroupId.IsNullOrEmpty())
            {
                return result;
            }
            if (_inspectionPgroups.Count() == 0)
            {
                var pGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
                _inspectionPgroups.AddRange(pGroups);
            }
            var pGroup = _inspectionPgroups.FirstOrDefault(x => x.PGROUP_ID == pgroupId);
            if (pGroup is not null)
            {
                if (pGroup.MGROUP_ID.IsNotNullOrEmpty())
                {
                    if (_inspectionMgroups.Count() == 0)
                    {
                        var mGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>().ToList();
                        _inspectionMgroups.AddRange(mGroups);
                    }
                    var mGroup = _inspectionMgroups.FirstOrDefault(x => x.MGROUP_ID == pGroup.MGROUP_ID);
                    if (mGroup is not null)
                    {
                        result = mGroup.MGROUP_NAME;
                    }
                }
            }
            return result;
        }

        public List<SYS6_INSPECTION_PGROUP> GetPgoups(string labid)
        {
            if (_inspectionPgroups.Count() == 0)
            {
                var pGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
                _inspectionPgroups.AddRange(pGroups);
            }
            return _inspectionPgroups.Where(x => x.LAB_ID == labid).ToList();
        }
        public EMS_START_STOP? GetEquipmentStartOrStopRecord(EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_START_STOP is null)
            {
                return null;
            }

            if (equipment.eMS_START_STOP.Count > 0)
            {
                var theLastRecord = equipment.eMS_START_STOP
                   .Where(x => x.START_CAUSE == "首次启用")
                    .FirstOrDefault();
                if (theLastRecord is not null)
                {
                    return theLastRecord;
                }
                else { return null; }
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 刷新墨水屏所需数据
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public IdentificationCard ExchangeIdentificationCard(string equipmentId)
        {

            var equipment = equipments.Where(x => x.EQUIPMENT_ID == equipmentId).FirstOrDefault();
            if (equipment is null)
            {
                return new();
            }

            var equipmentEnableTime = equipment.LAST_MTIME;
            var startOrstopRecord = GetEquipmentStartOrStopRecord(equipment);
            if (startOrstopRecord is not null)
            {
                equipmentEnableTime = startOrstopRecord.START_DATE;
            }

            string? installTime = null;
            string localtion = "";

            var installInfo = equipment.eMS_INSTALL_INFO;
            if (installInfo is not null)
            {
                installTime = installInfo.INSTALL_DATE.HasValue ? installInfo.INSTALL_DATE.Value.ToString("yyyy-MM-dd") : DateTime.Now.AddDays(7).ToString("yyyy-MM-dd");
                localtion = installInfo.INSTALL_AREA;
            }

            var correctDept = "";
            if (equipment.eMS_CORRECT_INFO is not null)
            {
                var correct = equipment.eMS_CORRECT_INFO.OrderByDescending(x => x.CORRECT_DATE).FirstOrDefault();
                if (correct is not null)
                {
                    correctDept = correct.CORRECT_DEPT;
                }
            }

            var (_, _, msg) = CombinedWarnInfo(equipment.WarnRecords);
            var result = new IdentificationCard()
            {
                equipmentName = equipment.EQUIPMENT_NAME,
                dealer = equipment.DEALER,
                equipmentCode = equipment.EQUIPMENT_CODE,
                deptSectionNo = equipment.DEPT_SECTION_NO,
                enableTime = equipmentEnableTime.HasValue ? equipmentEnableTime.Value.ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd"),
                installDate = $"{installTime}",
                maintainType = equipment.MAINTAIN_TYPE,
                nextMaintainDate = equipment.NEXT_MAINTAIN_DATE.HasValue ? equipment.NEXT_MAINTAIN_DATE.Value.ToString("yyyy-MM-dd") : "",
                nextMaintainDateStatus = msg.Contains("需进行设备保养") ? "true" : "false",
                nextCorrectDate = equipment.NEXT_CORRECT_DATE.HasValue ? equipment.NEXT_CORRECT_DATE.Value.ToString("yyyy-MM-dd") : "",
                nextCorrectDateStatus = msg.Contains("需进行设备校准") ? "true" : "false",
                correctDept = correctDept,
                eqInPerson = equipment.KEEP_PERSON,
                installArea = $"{localtion}",
                circumstance = ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                meassage = msg,
                registrationNum = equipment.REGISTRATION_NUM,
                eqOutTime = equipment.EQ_OUT_TIME.HasValue ? equipment.EQ_OUT_TIME.Value.ToString("yyyy-MM-dd") : "",
                professionalClass = ExchangeProfessionalClass(equipment.PROFESSIONAL_CLASS),
                manufacturer = equipment.MANUFACTURER,
                serialNumber = equipment.SERIAL_NUMBER,
                dealerContact = GetTheFirstContactUserName(equipment, "经销商", equipment.DEALER_ID),
                lastComparisonDate = equipment.LAST_COMPARISON_DATE.HasValue ? equipment.LAST_COMPARISON_DATE.Value.ToString("yyyy-MM-dd") : "",
                depreciationTime = equipment.DEPRECIATION_TIME,
                correctIntervals = equipment.CORRECT_INTERVALS,
                manufacturerContact = GetTheFirstContactUserName(equipment, "供应商", equipment.DEALER_ID),
                hospitalName = ExchangeHosptailName(equipment.HOSPITAL_ID),
                labName = ExchangeLabName(equipment.LAB_ID),
                sellPrice = equipment.SELL_PRICE.IsNullOrEmpty() ? $"{equipment.SELL_PRICE}元" : "",
                equipmentSize = equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.IsNullOrEmpty() ? "" :
                $"{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[0]}*{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[1]}*{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[2]}",
                correctDate = equipment.LAST_CORRECT_DATE.HasValue ? equipment.LAST_CORRECT_DATE.Value.ToString("yyyy-MM-dd") : "",
                model = equipment.EQUIPMENT_MODEL ?? "",
                maintainDate = equipment.LAST_MAINTAIN_DATE.HasValue? equipment.LAST_MAINTAIN_DATE.Value.ToString("yyyy-MM-dd") : "",
            };
            var colStyles = TemplateAContentInit.Default();
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.PGROUP_SID.Contains(equipment.UNIT_ID));
            if (template is not null)
            {
                colStyles = JsonConvert.DeserializeObject<List<Template>>(template.TEMPLATE_CONTENT);
            }
            result.SetTemplates(colStyles);

            return result;
        }


        public string GetTheFirstContactUserName(EMS_EQUIPMENT_INFO equipment, string type, string id)
        {

            var dealerContacts = new List<SYS6_COMPANY_CONTACT>();
            var contacts = GetCompanyContact(equipment.DEALER_ID);
            if (contacts.Count() == 0)
            {
                return "";
            }

            var manu = equipment.Contacts.Where(x => x.CONTACT_TYPE == type && x.CONTACT_ESTATE == "1").Select(x => x.CONTACT_ID).ToList();
            foreach (var contact in contacts)
            {
                contact.IF_SELECT = "0";
                if (manu is not null)
                {
                    if (manu.Contains(contact.CONTACT_ID))
                    {
                        contact.IF_SELECT = "1";
                    }
                }
                dealerContacts.Add(contact);
            }

            foreach (var contact in dealerContacts)
            {
                if (contact.IF_SELECT == "1")
                {
                    return contact.CONTACT_NAME;
                }
            }
            return "";

        }



        public string ExchangeEquipmentStartOrStop(string type)
        {
            return type switch
            {
                "0" => "停用",
                "1" => "启用",
                _ => ""
            };
        }


        public string ExchangeManageGroupName(string? unitId)
        {
            var result = "";
            if (unitId.IsNullOrEmpty())
            {
                return result;
            }
            if (_inspectionMgroups.Count() == 0)
            {
                var mGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>().ToList();
                _inspectionMgroups.AddRange(mGroups);
            }
            var mGroup = _inspectionMgroups.FirstOrDefault(x => x.MGROUP_ID == unitId);
            if (mGroup is not null)
            {
                result = mGroup.MGROUP_NAME;
            }

            return result!;
        }

        /// <summary>
        /// 替换单元组名
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public string ExchangeProfessionalGroupName(string? unitId)
        {
            var result = "";
            if (unitId.IsNullOrEmpty())
            {
                return result;
            }
            if (_inspectionPgroups.Count() == 0)
            {
                var pGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
                _inspectionPgroups.AddRange(pGroups);
            }
            var pGroup = _inspectionPgroups.FirstOrDefault(x => x.PGROUP_ID == unitId);
            if (pGroup is not null)
            {
                result = pGroup.PGROUP_NAME;
            }

            return result!;
        }
        

        /// <summary>
        /// 替换设备分类名
        /// </summary>
        /// <param name="classCode"></param>
        /// <returns></returns>
        public string ExchangeEquipmentClass(string? classCode, string? type)
        {
            var result = "";
            if (classCode.IsNullOrEmpty())
            {
                return result;
            }

            if (_baseData.Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1").Count() == 0)
            {
                var basedatas = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1")
                .ToList();
                _baseData.AddRange(basedatas);
            }
            var eqClasses = _baseData
                .Where(x => (x.CLASS_ID == "设备分类" || x.CLASS_ID == "设备类型") && x.DATA_STATE == "1")
                .ToList();
            var baseData = eqClasses.FirstOrDefault(x => x.CLASS_ID == "设备分类" && x.DATA_ID == classCode);
            if (baseData is not null)
            {
                result = baseData.DATA_CNAME;
            }
            if (type.IsNullOrEmpty() && classCode == "1")
            {
                result = "检测仪器";
            }

            if (type.IsNotNullOrEmpty() && classCode == "1")
            {
                var equimentType = eqClasses.FirstOrDefault(x => x.CLASS_ID == "设备类型" && x.DATA_ID == type);
                result = equimentType is null ? "检测仪器" : equimentType.DATA_CNAME;
            }
            
            return result;
        }

        
        /// <summary>
        /// 替换设备分类名
        /// </summary>
        /// <param name="classId">设备分类id</param>
        /// <returns></returns>
        public string ExchangeEquipmentNewClass(string? classId)
        {
            var result = "";
            if (classId.IsNullOrEmpty())
            {
                return result;
            }
            if (_Classes.Count == 0)
            {
                var classes  = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
                    .ToList();
                _Classes.AddRange(classes);
            }
            var eqClasses = _Classes.FirstOrDefault(x => x.ClassId == classId);
            if (eqClasses is not null)
            {
                result = eqClasses.ClassName;
            }
            return result;
        }
        
        
        /// <summary>
        /// 生安类型
        /// </summary>
        /// <param name="classCode"></param>
        /// <returns></returns>
        public string ExchangeEquipmentSmblClass(string? classCode)
        {
            const string smblClass = "生安设备类型";
            
            var result = "-";
            if (classCode.IsNullOrEmpty())
            {
                return result;
            }
            if (_baseData.Where(x => x.CLASS_ID == smblClass && x.DATA_STATE == "1").Count() == 0)
            {
                var basedatas = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                    .Where(x => x.CLASS_ID == smblClass && x.DATA_STATE == "1")
                    .ToList();
                if (!result.Any())
                {
                    basedatas.Add(new SYS6_BASE_DATA()
                {
                DATA_ID = "1",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "001",
                DATA_CNAME = "生物安全柜",
                DATA_ENAME = "生物安全柜",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            
            basedatas.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "2",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "002",
                    DATA_CNAME="高压灭菌器",
                    DATA_ENAME="高压灭菌器",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            basedatas.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "3",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "003",
                    DATA_CNAME="离心机",
                    DATA_ENAME="离心机",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            basedatas.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "4",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "004",
                DATA_CNAME = "洗眼装置",
                DATA_ENAME = "洗眼装置",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            basedatas.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "5",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "005",
                    DATA_CNAME="消毒装置",
                    DATA_ENAME="消毒装置",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            basedatas.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "6",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID="生安设备类型",
                DATA_SORT = "099",
                DATA_CNAME="其他",
                DATA_ENAME="其他",
                HIS_ID = null,
                CUSTOM_CODE="smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE= "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON="初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE=null,
                ONE_CLASS = "设备管理",
                DATA_UNAME=null,
                IF_REPEAT= null,
                SYSTEM_ID="LIS",
            });
        }
                _baseData.AddRange(basedatas);
            }
            var smblClasses = _baseData
                .Where(x => x.CLASS_ID == smblClass  && x.DATA_STATE == "1")
                .ToList();
            var baseData = smblClasses.FirstOrDefault(x => x.CLASS_ID == smblClass && x.DATA_ID == classCode);
            if (baseData is not null)
            {
                result = baseData.DATA_CNAME;
                return result ?? "-";
            }
            else
            {
                return result;
            }
        }

        
        /// <summary>
        /// 返回设备状态中文
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        public string ExchangeEquipmentState(string? state)
        {
            var result = "";
            if (state.IsNullOrEmpty())
            {
                return result;
            }
            result = state switch
            {

                "0" => "未启用",
                "1" => "启用",
                "2" => "待停用",
                "3" => "待报废",
                "4" => "停用",
                "5" => "报废",
                _ => "",
            };
            return result;

        }

        public EquipmentStateEnum ExchangeEquipmentStateEnum(string? state)
        {
            var result = EquipmentStateEnum.NotEnabled;
            if (state.IsNullOrEmpty())
            {
                return result;
            }
            result = state switch
            {
                "0" => EquipmentStateEnum.NotEnabled,
                "1" => EquipmentStateEnum.Normal,
                "2" => EquipmentStateEnum.PreDeactivat,
                "3" => EquipmentStateEnum.PreScrapped,
                "4" => EquipmentStateEnum.Deactivated,
                "5" => EquipmentStateEnum.Deactivated,
                _ => EquipmentStateEnum.Normal,
            };
            return result;

        }
        

        /// <summary>
        /// 替换专业分类名
        /// </summary>
        /// <param name="professionalClassCode"></param>
        /// <returns></returns>
        public string ExchangeProfessionalClass(string? professionalClassCode)
        {
            var result = "";
            if (professionalClassCode.IsNullOrEmpty())
            {
                return result;
            }

            if (_baseData.Where(p => p.CLASS_ID == "专业分类" && p.DATA_STATE == "1").Count() == 0)
            {
                var basedatas = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "专业分类" && p.DATA_STATE == "1")
                .ToList();
                _baseData.AddRange(basedatas);
            }
            var professionalClass = _baseData
                .FirstOrDefault(p => p.CLASS_ID == "专业分类" && p.DATA_STATE == "1");
            if (professionalClass is not null)
            {
                result = professionalClass.DATA_CNAME;
            }
            return result;
        }

        public string ExchangeHosptailName(string hospitalId)
        {
            var result = "";

            if (hospitalId is null)
            {
                return result;
            }

            if (_hospitalInfo.Where(x => x.HOSPITAL_ID == hospitalId).Count() == 0)
            {
                var hospitals = _dbContext.Db.Queryable<SYS6_HOSPITAL_INFO>()
                .ToList();
                _hospitalInfo.Clear();
                _hospitalInfo.AddRange(hospitals);
            }

            var hospitalInfo = _hospitalInfo
                   .Where(p => p.HOSPITAL_ID == hospitalId)
                   .FirstOrDefault();
            if (hospitalInfo is not null)
            {
                result = hospitalInfo.HOSPITAL_CNAME;
            }
            return result;

        }

        public string ExchangeAssemblyLineName(string assemblylineCode)
        {
            var result = "";
            if (assemblylineCode.IsNullOrEmpty())
            {
                return result;
            }
            if (_baseData.Where(baseData => baseData.CLASS_ID == "设备类型" && baseData.DATA_CNAME == "流水线").Count() == 0)
            {
                var basedatas = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(baseData => baseData.CLASS_ID == "设备类型" && baseData.DATA_CNAME == "流水线")
                .ToList();
                _baseData.AddRange(basedatas);
            }
            var eqClasses = _baseData
                .Where(baseData => baseData.CLASS_ID == "设备类型" && baseData.DATA_CNAME == "流水线")
                .ToList();
            var baseData = eqClasses.FirstOrDefault(x => x.CLASS_ID == assemblylineCode);
            if (baseData is not null)
            {
                result = baseData.DATA_SNAME;
            }
            return result;
        }

        public string? ExchangeAreaNameByPGroupId(string pgroupId)
        {
            var result = "";
            if (pgroupId.IsNullOrEmpty())
            {
                return result;
            }
            if (_inspectionPgroups.Count() == 0)
            {
                var pGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
                _inspectionPgroups.AddRange(pGroups);
            }
            var pGroup = _inspectionPgroups.FirstOrDefault(x => x.PGROUP_ID == pgroupId);
            if (pGroup is not null)
            {
                if (pGroup.AREA_ID.IsNotNullOrEmpty())
                {
                    if (_areas.Count() == 0)
                    {
                        var areas = _dbContext.Db.Queryable<SYS6_INSPECTION_AREA>().ToList();
                        _areas.AddRange(areas);
                    }
                    var area = _areas.FirstOrDefault(x => x.AREA_ID == pGroup.AREA_ID);
                    if (area is not null)
                    {
                        result = area.AREA_NAME;
                    }
                }
            }
            return result;
        }
        
        public string ExchangeLabName(string labId)
        {
            var result = "";

            if (labId is null)
            {
                return result;
            }

            if (_inspectionLab.Count() == 0)
            {
                var Labs = _dbContext.Db.Queryable<SYS6_INSPECTION_LAB>()
                .ToList();
                _inspectionLab.Clear();
                _inspectionLab.AddRange(Labs);
            }

            var LabInfo = _inspectionLab
                   .Where(p => p.LAB_ID == labId)
                   .FirstOrDefault();
            if (LabInfo is not null)
            {
                result = $"{LabInfo.LAB_NAME}";
            }
            return result;
        }

        
        public string ExchangeSmblLabName(string? smbLlabId)
        {
            var result = "";
            if (smbLlabId.IsNullOrEmpty())
            {
                return result;
            }

            if (!_smblLabs.Any())
            {
                var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                    .ToList();
                _smblLabs.Clear();
                _smblLabs.AddRange(smblLabs);
            }
            var smblLab = _smblLabs
                .FirstOrDefault(p => p.SMBL_LAB_ID == smbLlabId);
            if (smblLab is not null)
            {
                result = $"{smblLab.SMBL_LAB_CNAME}";
            }
            return result;
        }

        
        public string VerifyresultName(string VerifyresultCode)
        {

            return VerifyresultCode switch
            {
                "1" => "通过",
                "2" => "不通过",
                "3" => "部分通过",
                _ => ""
            };

        }

        public string DebugConditionName(string debugConditionCode)
        {

            return debugConditionCode switch
            {
                "1" => "正常",
                "2" => "异常",
                _ => ""
            };

        }


        /// <summary>
        /// 根据设备的工作计划中的“保养、验证、比对、校准”的下次的任务时间
        /// </summary>
        /// <param name="equipment"></param>
        public void CalculateEquipmentWorkPlanNextTime(EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_WORK_PLAN != null && 
                ( !NeedReviewProcess || equipment.eMS_WORK_PLAN.CURRENT_STATE == ((int)OperationStateEnum.Audited).ToString()))
            {
                CalculateEquipmentWorkPlanMaintain(equipment);
                CalculateEquipmentWorkPlanCorrect(equipment);
                CalculateEquipmentWorkPlanComparison(equipment);
                CalculateEquipmentWorkPlanVerification(equipment);
            }

            CalculateEquipmentLastRecord(equipment);

        }

        private void CalculateEquipmentLastRecord(EMS_EQUIPMENT_INFO equipment)
        {
            CalculateLastMaintain(equipment);
            CalculateLastCorrect(equipment);
            CalculateLastComparison(equipment);
            CalculateLastVerification(equipment);
        }

        /// <summary>
        /// 计算上次保养时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateLastMaintain(EMS_EQUIPMENT_INFO equipment)
        {
            var maintenanceRecode = equipment.eMS_MAINTAIN_INFO
                .OrderByDescending(x => x.MAINTAIN_DATE)
                .Where(x => x.MAINTAIN_STATE == "1")
                .Where(x => x.MAINTAIN_DATE != null)
                .FirstOrDefault();
            if (maintenanceRecode is null)
            {
                return;
            }
            var lastMaintainDate = maintenanceRecode.MAINTAIN_DATE;
            if (maintenanceRecode is null)
            {
                return;
            }
            equipment.LAST_MAINTAIN_DATE = lastMaintainDate;
        }

        /// <summary>
        /// 计算上次校准时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateLastCorrect(EMS_EQUIPMENT_INFO equipment)
        {
            var correctRecode = equipment.eMS_CORRECT_INFO
                .OrderByDescending(x => x.CORRECT_DATE)
                .Where(x => x.CORRECT_DATE != null)
                .Where(x => x.CORRECT_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (correctRecode is null)
            {
                return;
            }
            equipment.LAST_CORRECT_DATE = correctRecode.CORRECT_DATE;
            equipment.LAST_CORRECT_RESULT = correctRecode.CORRECT_RESULT;
            equipment.LAST_CORRECT_DEPT = correctRecode.CORRECT_DEPT;
        }

        /// <summary>
        /// 计算上次比对时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateLastComparison(EMS_EQUIPMENT_INFO equipment)
        {
            var comparisonRecode = equipment.eMS_COMPARISON_INFO
                .OrderByDescending(x => x.COMPARISON_DATE)
                .Where(x => x.COMPARISON_DATE != null)
                .Where(x => x.COMPARISON_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (comparisonRecode is null)
            {
                return;
            }
            equipment.LAST_COMPARISON_DATE = comparisonRecode.COMPARISON_DATE;
        }

        /// <summary>
        /// 计算性能验证时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateLastVerification(EMS_EQUIPMENT_INFO equipment)
        {
            var verificationRecord = equipment.eMS_VERIFICATION_INFO
                .OrderByDescending(x => x.VERIFICATION_DATE)
                .Where(x => x.VERIFICATION_DATE != null)
                .Where(x => x.VERIFICATION_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (verificationRecord is null)
            {
                return;
            }
            equipment.LAST_VERIFICATION_DATE = verificationRecord.VERIFICATION_DATE;
        }


        /// <summary>
        /// 计算保养时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateEquipmentWorkPlanMaintain(EMS_EQUIPMENT_INFO equipment)
        {
            CompareWorkPlanCycle(equipment);
            var workPlan = equipment.eMS_WORK_PLAN;
            var maintenanceRecode = equipment.eMS_MAINTAIN_INFO
                .OrderByDescending(x => x.MAINTAIN_DATE)
                .Where(x => x.MAINTAIN_STATE != null)
                .Where(x => x.MAINTAIN_CYCLE != null)
                .Where(x => x.MAINTAIN_STATE == "1")
                .FirstOrDefault();
            if (maintenanceRecode is null)
            {
                return;
            }
            if (maintenanceRecode.MAINTAIN_CYCLE.Contains("周"))
            {
                equipment.MAINTAIN_INTERVALS = workPlan.MAINTAIN_INTERVALS;
                equipment.MAINTAIN_WARN_INTERVALS = workPlan.MAINTAIN_WARN_INTERVALS;
            }
            if (maintenanceRecode.MAINTAIN_CYCLE.Contains("月"))
            {
                equipment.MAINTAIN_INTERVALS = workPlan.MONTHLY_MAINTAIN;
                equipment.MAINTAIN_WARN_INTERVALS = workPlan.MONTHLY_MAINTAIN_WARN;
            }
            if (maintenanceRecode.MAINTAIN_CYCLE.Contains("季度"))
            {
                equipment.MAINTAIN_INTERVALS = workPlan.QUARTERLY_MAINTAIN;
                equipment.MAINTAIN_WARN_INTERVALS = workPlan.QUARTERLY_MAINTAIN_WARN;
            }
            if (maintenanceRecode.MAINTAIN_CYCLE.Contains("年"))
            {
                equipment.MAINTAIN_INTERVALS = workPlan.YEARLY_MAINTAIN;
                equipment.MAINTAIN_WARN_INTERVALS = workPlan.YEARLY_MAINTAIN_WARN;
            }

        }

        /// <summary>
        /// 计算校准时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateEquipmentWorkPlanCorrect(EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = equipment.eMS_WORK_PLAN;

            var correctRecode = equipment.eMS_CORRECT_INFO
                .OrderByDescending(x => x.CORRECT_DATE)
                .Where(x => x.CORRECT_DATE != null)
                .Where(x => x.CORRECT_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (correctRecode is null)
            {
                return;
            }
            equipment.CORRECT_INTERVALS = workPlan.CORRECT_INTERVALS;
            equipment.CORRECT_WARN_INTERVALS = workPlan.CORRECT_WARN_INTERVALS;
            equipment.LAST_CORRECT_DATE = correctRecode.CORRECT_DATE.HasValue ? correctRecode.CORRECT_DATE : correctRecode.LAST_MTIME;
            equipment.NEXT_CORRECT_DATE = workPlan.CORRECT_INTERVALS != null ? Convert.ToDateTime(correctRecode.CORRECT_DATE)
                        .AddDays((int)double.Parse(workPlan.CORRECT_INTERVALS)) : null;
        }

        /// <summary>
        /// 计算比对时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateEquipmentWorkPlanComparison(EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = equipment.eMS_WORK_PLAN;
            var comparisonRecode = equipment.eMS_COMPARISON_INFO
                .OrderByDescending(x => x.COMPARISON_DATE)
                .Where(x => x.COMPARISON_DATE != null)
                .Where(x => x.COMPARISON_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (comparisonRecode is null)
            {
                return;
            }
            equipment.COMPARISON_INTERVALS = workPlan.COMPARISON_INTERVALS;
            equipment.COMPARISON_WARN_INTERVALS = workPlan.COMPARISON_WARN_INTERVALS;
            equipment.LAST_COMPARISON_DATE = comparisonRecode.COMPARISON_DATE.HasValue ? comparisonRecode.COMPARISON_DATE : comparisonRecode.LAST_MTIME;
            equipment.NEXT_COMPARISON_DATE = workPlan.COMPARISON_INTERVALS != null ? Convert.ToDateTime(comparisonRecode.COMPARISON_DATE)
                        .AddDays(int.Parse(workPlan.COMPARISON_INTERVALS)) : null;
        }

        /// <summary>
        /// 计算性能验证时间
        /// </summary>
        /// <param name="equipment"></param>
        private void CalculateEquipmentWorkPlanVerification(EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = equipment.eMS_WORK_PLAN;
            var verificationRecord = equipment.eMS_VERIFICATION_INFO
                .OrderByDescending(x => x.VERIFICATION_DATE)
                .Where(x => x.VERIFICATION_DATE != null)
                .Where(x => x.VERIFICATION_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .FirstOrDefault();
            if (verificationRecord is null)
            {
                return;
            }
            equipment.VERIFICATION_INTERVALS = workPlan.VERIFICATION_INTERVALS;
            equipment.VERIFICATION_WARN_INTERVALS = workPlan.VERIFICATION_WARN_INTERVALS;
            equipment.LAST_VERIFICATION_DATE = verificationRecord.VERIFICATION_DATE.HasValue ? verificationRecord.VERIFICATION_DATE : verificationRecord.LAST_MTIME;
            equipment.NEXT_VERIFICATION_DATE = workPlan.VERIFICATION_INTERVALS != null ? Convert.ToDateTime(verificationRecord.VERIFICATION_DATE)
                        .AddDays(int.Parse(workPlan.VERIFICATION_INTERVALS)) : null;

        }

        private void CompareWorkPlanCycle(EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = equipment.eMS_WORK_PLAN;
            var maintenanceRecodes = equipment.eMS_MAINTAIN_INFO
                .OrderByDescending(x => x.MAINTAIN_DATE)
                .Where(x => x.MAINTAIN_CYCLE != null)
                .Where(x => x.MAINTAIN_DATE != null)
                .Where(x => x.MAINTAIN_STATE == "1")
                .ToList();

            var week = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("周"))
                  .OrderByDescending(x => x.MAINTAIN_DATE)
                  .FirstOrDefault();
            var month = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("月"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();

            var quarter = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("季度"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();

            var year = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("年"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();
            var lastMaintainDate = maintenanceRecodes.Count() > 0 ? maintenanceRecodes[0].MAINTAIN_DATE : null;

            var nextMaintain = CompareNext(workPlan, week, month, quarter, year);

            if (nextMaintain is null)
            {
                return;
            }

            var dateTime = lastMaintainDate.HasValue ? lastMaintainDate : DateTime.Now;

            equipment.MAINTAIN_TYPE = nextMaintain.MAINTAIN_CYCLE switch
            {
                "月" => "月保养",
                "季度" => "季保养",
                "年" => "年保养",
                "半年" => "年保养",
                "周" => "周保养",
                _ => ""
            };

            var weekAddDay = workPlan.MAINTAIN_INTERVALS;
            var monthAddDay = workPlan.MONTHLY_MAINTAIN;
            var quarterAddDay = workPlan.QUARTERLY_MAINTAIN;
            var yearAddDay = workPlan.YEARLY_MAINTAIN;

            if (equipment.MAINTAIN_TYPE == "月保养")
            {
                dateTime = dateTime.Value.AddDays(Convert.ToDouble(monthAddDay));
            }
            if (equipment.MAINTAIN_TYPE == "年保养")
            {
                dateTime = dateTime.Value.AddDays(Convert.ToDouble(yearAddDay));
            }
            if (equipment.MAINTAIN_TYPE == "季保养")
            {
                dateTime = dateTime.Value.AddDays(Convert.ToDouble(quarterAddDay));
            }
            if (equipment.MAINTAIN_TYPE == "周保养")
            {
                dateTime = dateTime.Value.AddDays(Convert.ToDouble(weekAddDay));
            }
            equipment.LAST_MAINTAIN_DATE = lastMaintainDate;
            equipment.NEXT_MAINTAIN_DATE = dateTime;
        }

        private EMS_MAINTAIN_INFO CompareNext(EMS_EQUIPMENT_INFO equipment)
        {

            var workPlan = equipment.eMS_WORK_PLAN;

            var maintenanceRecodes = equipment.eMS_MAINTAIN_INFO
                .OrderByDescending(x => x.MAINTAIN_DATE)
                .Where(x => x.MAINTAIN_CYCLE != null)
                .Where(x => x.MAINTAIN_DATE != null)
                .ToList();

            var week = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("周"))
                  .OrderByDescending(x => x.MAINTAIN_DATE)
                  .FirstOrDefault();
            var month = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("月"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();
            var quarter = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("季度"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();
            var year = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("年"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();
            return CompareNext(workPlan, week, month, quarter, year);
        }
        private EMS_MAINTAIN_INFO? CompareNext(EMS_WORK_PLAN workPlan, EMS_MAINTAIN_INFO week, EMS_MAINTAIN_INFO month, EMS_MAINTAIN_INFO quarter, EMS_MAINTAIN_INFO year)
        {
            
            var dataTimeNow = DateTime.Now;
            var weekAddDay = workPlan.MAINTAIN_INTERVALS;
            var monthAddDay = workPlan.MONTHLY_MAINTAIN;
            var quarterAddDay = workPlan.QUARTERLY_MAINTAIN;
            var yearAddDay = workPlan.YEARLY_MAINTAIN;

            var weekDate = week is not null ? week.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(weekAddDay)) : DateTime.MinValue.AddDays(Convert.ToDouble(weekAddDay));
            var monthDate = month is not null ? month.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(monthAddDay)) : DateTime.MinValue.AddDays(Convert.ToDouble(monthAddDay));
            var quarterDate = quarter is not null ? quarter.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(quarterAddDay)) : DateTime.MinValue.AddDays(Convert.ToDouble(quarterAddDay));
            var yearDate = year is not null ? year.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(yearAddDay)) : DateTime.MinValue.AddDays(Convert.ToDouble(yearAddDay));

            var minWeekDate = weekDate - dataTimeNow;
            var minMonthDate = monthDate - dataTimeNow;
            var minQuarterDate = quarterDate - dataTimeNow;
            var minYearDate = yearDate - dataTimeNow;

            var list = new List<(EMS_MAINTAIN_INFO?, TimeSpan)>();
            if (weekAddDay is not null)
            {
                list.Add((week, minWeekDate));
            }
            if (monthAddDay is not null)
            {
                list.Add((month, minMonthDate));
            }
            if (quarterAddDay is not null)
            {
                list.Add((quarter, minQuarterDate));
            }
            if (yearAddDay is not null)
            {
                list.Add((year, minYearDate));
            }

            return ComparisonTime(list);
        }

        private EMS_MAINTAIN_INFO ComparisonTime(List<(EMS_MAINTAIN_INFO?, TimeSpan)> datas)
        {
            var returnNull = 0;
            var times = new List<TimeSpan>();
            foreach (var data in datas)
            {
                if (data.Item1 is null)
                {
                    returnNull++;
                }
                times.Add(data.Item2);
            }
            if (returnNull == datas.Count())
            {
                return null;
            }
            int minIndex = 0;
            double minDays = double.MaxValue;
            for (int i = 0; i < times.Count(); i++)
            {
                if (Math.Abs(times[i].TotalDays) < minDays)
                {
                    minDays = Math.Abs(times[i].TotalDays);
                    minIndex = i;
                }
            }
            return datas[minIndex].Item1;
        }

        public List<SYS6_COMPANY_CONTACT> GetCompanyContact(string companyId)
        {
            if (_companyContacts.Count() == 0)
            {
                _companyContacts.Clear();
                var res = _dbContext.Db.Queryable<SYS6_COMPANY_CONTACT>()
                .Where(p => p.CONTACT_STATE == "1")
                .ToList();
                _companyContacts.AddRange(res);
            }
            var result = _companyContacts.Where(x => x.COMPANY_ID == companyId).ToList();
            return result;
        }


        public EMS_SUBSCRIBE_INFO? GetSubscribe(string equipmentId)
        {
            if (_subscribes.Count() == 0)
            {
                var subs = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                   .Where(x => x.EQUIPMENT_ID != null)
                   .ToList();
                _subscribes.Clear();
                _subscribes.AddRange(subs);
            }

            EMS_SUBSCRIBE_INFO? result = null;
            foreach (var subscribe in _subscribes)
            {
                if (subscribe.EQUIPMENT_ID!.Contains(equipmentId))
                {
                    result = subscribe;
                }
            }

            return result;

        }


        public List<EMS_OPER_LOG> GetPlanWorkOperLogs(EMS_EQUIPMENT_INFO equipment)
        {
            if (_logRecords.Count() == 0)
            {
                _logRecords.Clear();
                var openRecords = _dbContext.Db.Queryable<EMS_OPER_LOG>().ToList();
                _logRecords.AddRange(openRecords);
            }
            var result = _logRecords.Where(x => x.OPER_MAIN_ID == equipment.eMS_WORK_PLAN.WORK_PLAN_ID).ToList();

            return result;
        }


        public List<string> GetEmsDocPaths(string docInfoId, string? docClass)
        {

            var reuslt = new List<string>();

            if (_docs.Count() == 0)
            {
                _docs.Clear();
                var docs = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(x=>x.DOC_STATE == "1")
                  .ToList();
                _docs.AddRange(docs);
            }

            return _docs.WhereIF(docClass.IsNotNullOrEmpty(), x => x.DOC_CLASS == docClass)
                   .Where(x => x.DOC_INFO_ID == docInfoId)
                   .Where(x=>x.DOC_STATE == "1")
                   .Select(x => x.DOC_PATH)
                   .ToList();
        }

        #endregion


        #region 取值运算

        /// <summary>
        /// 计算设备即将展示的模板数据
        /// </summary>
        /// <param name="equipment"></param>
        /// <returns></returns>
        private IdentificationCard ContIdCradInfo(EMS_EQUIPMENT_INFO equipment)
        {

            var equipmentEnableTime = equipment.LAST_MTIME;
            var startOrstopRecord = GetEquipmentStartOrStopRecord(equipment);
            if (startOrstopRecord is not null)
            {
                equipmentEnableTime = startOrstopRecord.START_DATE;
            }
            string? installTime = null;
            string localtion = "";
            var installInfo = equipment.eMS_INSTALL_INFO;
            if (installInfo is not null)
            {
                installTime = installInfo.INSTALL_DATE.HasValue ? installInfo.INSTALL_DATE.Value.ToString("yyyy-MM-dd") : DateTime.Now.AddDays(7).ToString("yyyy-MM-dd");
                localtion = installInfo.INSTALL_AREA;
            }
            var correctDept = "";
            if (equipment.eMS_CORRECT_INFO is not null)
            {
                var correct = equipment.eMS_CORRECT_INFO.OrderByDescending(x => x.CORRECT_DATE).FirstOrDefault();
                if (correct is not null)
                {
                    correctDept = correct.CORRECT_DEPT;
                }
            }

            var size = "";
            if (equipment.eMS_ENVI_REQUIRE_INFO is not null)
            {
                if (equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE is not null)
                {
                    size = $"{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[0]}*{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[1]}*{equipment.eMS_ENVI_REQUIRE_INFO.EQUIPMENT_SIZE.Split(",")[2]}";
                }
            }

            var (_, _, msg) = CombinedWarnInfo(equipment.WarnRecords);
            var result = new IdentificationCard()
            {
                equipmentName = equipment.EQUIPMENT_NAME ?? "",
                dealer = equipment.DEALER ?? "",
                equipmentCode = equipment.EQUIPMENT_CODE ?? "",
                deptSectionNo = equipment.DEPT_SECTION_NO ?? "",
                enableTime = equipmentEnableTime.HasValue ? equipmentEnableTime.Value.ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd"),
                installDate = $"{installTime}",
                maintainType = equipment.MAINTAIN_TYPE ?? "",
                nextMaintainDate = equipment.NEXT_MAINTAIN_DATE.HasValue ? equipment.NEXT_MAINTAIN_DATE.Value.ToString("yyyy-MM-dd") : "",
                nextMaintainDateStatus = msg.Contains("保养超期") ? "true" : "false",
                nextCorrectDate = equipment.NEXT_CORRECT_DATE.HasValue ? equipment.NEXT_CORRECT_DATE.Value.ToString("yyyy-MM-dd") : "",
                nextCorrectDateStatus = msg.Contains("校准超期") ? "true" : "false",
                correctDept = correctDept,
                eqInPerson = equipment.KEEP_PERSON,
                installArea = $"{localtion}",
                circumstance = ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                meassage = msg,
                registrationNum = equipment.REGISTRATION_NUM,
                eqOutTime = equipment.EQ_OUT_TIME.HasValue ? equipment.EQ_OUT_TIME.Value.ToString("yyyy-MM-dd") : "",
                professionalClass = ExchangeProfessionalClass(equipment.PROFESSIONAL_CLASS),
                manufacturer = equipment.MANUFACTURER,
                serialNumber = equipment.SERIAL_NUMBER,
                dealerContact = GetTheFirstContactUserName(equipment, "经销商", equipment.DEALER_ID),
                lastComparisonDate = equipment.LAST_COMPARISON_DATE.HasValue ? equipment.LAST_COMPARISON_DATE.Value.ToString("yyyy-MM-dd") : "",
                depreciationTime = equipment.DEPRECIATION_TIME,
                correctIntervals = equipment.CORRECT_INTERVALS,
                manufacturerContact = GetTheFirstContactUserName(equipment, "供应商", equipment.DEALER_ID),
                hospitalName = ExchangeHosptailName(equipment.HOSPITAL_ID),
                labName = ExchangeLabName(equipment.LAB_ID),
                sellPrice = $"{equipment.SELL_PRICE}",
                equipmentSize = size,
                correctDate = equipment.LAST_CORRECT_DATE.HasValue? equipment.LAST_CORRECT_DATE.Value.ToString("yyyy-MM-dd") : "",
                model = equipment.EQUIPMENT_MODEL ?? "",
                maintainDate = equipment.LAST_MAINTAIN_DATE.HasValue? equipment.LAST_MAINTAIN_DATE.Value.ToString("yyyy-MM-dd") : "",
            };
            return result;
        }

        public string GetEquimentMaxLongValue(string attribute)
        {

            var idCards = new List<IdentificationCard>();
            Injection();
            foreach (var equipment in equipments)
            {
                idCards.Add(ContIdCradInfo(equipment));
            }

            if (idCards.Count == 0)
            {
                return "";
            }


            return attribute switch
            {
                "equipmentName" => idCards.OrderByDescending(x => SqlFunc.Length(x.equipmentName)).Take(1).SingleOrDefault()!.equipmentName,
                "dealer" => idCards.OrderByDescending(x => SqlFunc.Length(x.dealer)).Take(1).SingleOrDefault()!.dealer,
                "equipmentCode" => idCards.OrderByDescending(x => SqlFunc.Length(x.equipmentCode)).Take(1).SingleOrDefault()!.equipmentCode,
                "deptSectionNo" => idCards.OrderByDescending(x => SqlFunc.Length(x.deptSectionNo)).Take(1).SingleOrDefault()!.deptSectionNo,
                "enableTime" => idCards.OrderByDescending(x => SqlFunc.Length(x.enableTime)).Take(1).SingleOrDefault()!.enableTime,
                "installDate" => idCards.OrderByDescending(x => SqlFunc.Length(x.installDate)).Take(1).SingleOrDefault()!.installDate,
                "nextMaintainDate" => idCards.OrderByDescending(x => SqlFunc.Length(x.nextMaintainDate)).Take(1).SingleOrDefault()!.nextMaintainDate,
                "maintainType" => idCards.OrderByDescending(x => SqlFunc.Length(x.maintainType)).Take(1).SingleOrDefault()!.maintainType,
                "nextCorrectDate" => idCards.OrderByDescending(x => SqlFunc.Length(x.nextCorrectDate)).Take(1).SingleOrDefault()!.nextCorrectDate,
                "correctDept" => idCards.OrderByDescending(x => SqlFunc.Length(x.correctDept)).Take(1).SingleOrDefault()!.correctDept,
                "eqInPerson" => idCards.OrderByDescending(x => SqlFunc.Length(x.eqInPerson)).Take(1).SingleOrDefault()!.eqInPerson,
                "installArea" => idCards.OrderByDescending(x => SqlFunc.Length(x.installArea)).Take(1).SingleOrDefault()!.installArea,
                "registrationNum" => idCards.OrderByDescending(x => SqlFunc.Length(x.registrationNum)).Take(1).SingleOrDefault()!.registrationNum,
                "eqOutTime" => idCards.OrderByDescending(x => SqlFunc.Length(x.eqOutTime)).Take(1).SingleOrDefault()!.eqOutTime,
                "professionalClass" => idCards.OrderByDescending(x => SqlFunc.Length(x.professionalClass)).Take(1).SingleOrDefault()!.professionalClass,
                "manufacturer" => idCards.OrderByDescending(x => SqlFunc.Length(x.manufacturer)).Take(1).SingleOrDefault()!.manufacturer,
                "serialNumber" => idCards.OrderByDescending(x => SqlFunc.Length(x.serialNumber)).Take(1).SingleOrDefault()!.serialNumber,
                "dealerContact" => idCards.OrderByDescending(x => SqlFunc.Length(x.dealer)).Take(1).SingleOrDefault()!.dealer,
                "lastComparisonDate" => idCards.OrderByDescending(x => SqlFunc.Length(x.lastComparisonDate)).Take(1).SingleOrDefault()!.lastComparisonDate,
                "depreciationTime" => idCards.OrderByDescending(x => SqlFunc.Length(x.depreciationTime)).Take(1).SingleOrDefault()!.depreciationTime,
                "correctIntervals" => idCards.OrderByDescending(x => SqlFunc.Length(x.correctIntervals)).Take(1).SingleOrDefault()!.correctIntervals,
                "manufacturerContact" => idCards.OrderByDescending(x => SqlFunc.Length(x.manufacturerContact)).Take(1).SingleOrDefault()!.manufacturerContact,
                "hospitalName" => idCards.OrderByDescending(x => SqlFunc.Length(x.hospitalName)).Take(1).SingleOrDefault()!.hospitalName,
                "labName" => idCards.OrderByDescending(x => SqlFunc.Length(x.labName)).Take(1).SingleOrDefault()!.labName,
                "sellPrice" => idCards.OrderByDescending(x => SqlFunc.Length(x.sellPrice)).Take(1).SingleOrDefault()!.sellPrice,
                "equipmentSize" => idCards.OrderByDescending(x => SqlFunc.Length(x.equipmentSize)).Take(1).SingleOrDefault()!.equipmentSize,
                "correctDate" => idCards.OrderByDescending(x => SqlFunc.Length(x.correctDate)).Take(1).SingleOrDefault()!.correctDate,
                "model"=> idCards.OrderByDescending(x => SqlFunc.Length(x.model)).Take(1).SingleOrDefault()!.model,
                "maintainDate"=> idCards.OrderByDescending(x => SqlFunc.Length(x.maintainDate)).Take(1).SingleOrDefault()!.maintainDate,

                _ => ""
            };
        }




        #endregion


    }
}
