﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.EquipmengtClassNew;

    /// <summary>
    /// 设备附加记录表
    /// </summary>
    [DBOwner("XH_OA")]
    [SugarTable("EMS_EQP_ADDN_RECORD", TableDescription = "设备附加记录表")]
    public class EMS_EQP_ADDN_RECORD
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true,ColumnName = "EQP_RECORD_ID")]
        public string EqpRecordId{ get; set; }
        
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [SugarColumn(ColumnName = "HOSPITAL_ID")]
        public string HospitalId{ get; set; }
        
        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId{ get; set; }
        
        /// <summary>
        /// 档案记录字典ID
        /// </summary>
        [SugarColumn(ColumnName = "EQP_ARCHIVES_ID")]
        public string? EqpArchivesId{ get; set; }
        
        /// <summary>
        /// 附加信息
        /// </summary>
        [SugarColumn(ColumnName = "EQUITMENT_JSON")]
        public string? EquitmentJson{ get; set; }
        
        /// <summary>
        /// 附件
        /// </summary>
        [SugarColumn(ColumnName = "EQP_RECORD_AFFIX")]
        public string? EqpRecordAffix{ get; set; }
        
        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnName = "EQP_RECORD_SORT")]
        public string? EqpRecordSort{ get; set; }
        
        /// <summary>
        /// 状态;0禁用 1在用 2删除
        /// </summary>
        [SugarColumn(ColumnName = "EQP_RECORD_STATE")]
        public string? EqpRecordState{ get; set; }
        
        /// <summary>
        /// 首次登记人
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RPERSON")]
        public string? FIRST_RPERSON{ get; set; }
        
        /// <summary>
        /// 首次登记时间
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RTIME")]
        public DateTime? FIRST_RTIME{ get; set; }
        
        /// <summary>
        /// 最后修改人员
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MPERSON")]
        public string? LAST_MPERSON{ get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MTIME")]
        public DateTime? LAST_MTIME{ get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string? Remark{ get; set; }
        
    }