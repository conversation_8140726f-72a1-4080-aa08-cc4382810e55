{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"XH.H82.Services/1.0.0": {"dependencies": {"EPPlus": "6.2.6", "NuGet.Protocol": "6.6.1", "SkiaSharp.NativeAssets.Linux": "2.88.6", "System.ServiceModel.Duplex": "4.8.1", "System.ServiceModel.Federation": "4.8.1", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.NetTcp": "4.8.1", "System.ServiceModel.Security": "4.8.1", "System.Xml.XmlSerializer": "4.3.0", "XH.H82.Base": "1.0.0", "XH.H82.IServices": "1.0.0"}, "runtime": {"XH.H82.Services.dll": {}}}, "AspNetCoreRateLimit/4.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/AspNetCoreRateLimit.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Autofac/6.4.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net6.0/Autofac.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "Autofac.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Autofac": "6.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extras.DynamicProxy/6.0.1": {"dependencies": {"Autofac": "6.4.0", "Castle.Core": "5.1.0"}, "runtime": {"lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "6.0.1.0", "fileVersion": "6.0.1.0"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "5.0.0"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "0.4.0.0", "fileVersion": "0.4.0.2"}}}, "BouncyCastle/1.8.9": {}, "Castle.Core/5.1.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dapper/2.0.123": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.123.33578"}}}, "EasyCaching.Core/1.7.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net6.0/EasyCaching.Core.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}}, "EasyCaching.Serialization.Protobuf/1.7.0": {"dependencies": {"EasyCaching.Core": "1.7.0", "protobuf-net": "3.1.0"}, "runtime": {"lib/net6.0/EasyCaching.Serialization.Protobuf.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}}, "Elastic.Channels/0.7.0": {"dependencies": {"System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netstandard2.1/Elastic.Channels.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.7.0.0"}}}, "Elastic.Clients.Elasticsearch/8.15.0": {"dependencies": {"Elastic.Transport": "0.4.22"}, "runtime": {"lib/net6.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "8.15.0.0"}}}, "Elastic.CommonSchema/8.11.1": {"dependencies": {"Ben.Demystifier": "0.4.1"}, "runtime": {"lib/net6.0/Elastic.CommonSchema.dll": {"assemblyVersion": "*******", "fileVersion": "8.11.1.0"}}}, "Elastic.CommonSchema.Serilog/8.11.1": {"dependencies": {"Elastic.CommonSchema": "8.11.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "8.11.1.0"}}}, "Elastic.Ingest.Elasticsearch/0.7.0": {"dependencies": {"Elastic.Ingest.Transport": "0.7.0"}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.7.0.0"}}}, "Elastic.Ingest.Elasticsearch.CommonSchema/8.11.1": {"dependencies": {"Elastic.CommonSchema": "8.11.1", "Elastic.Ingest.Elasticsearch": "0.7.0"}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.dll": {"assemblyVersion": "*******", "fileVersion": "8.11.1.0"}}}, "Elastic.Ingest.Transport/0.7.0": {"dependencies": {"Elastic.Channels": "0.7.0", "Elastic.Transport": "0.4.22", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.7.0.0"}}}, "Elastic.Serilog.Sinks/8.11.1": {"dependencies": {"Elastic.CommonSchema.Serilog": "8.11.1", "Elastic.Ingest.Elasticsearch.CommonSchema": "8.11.1"}, "runtime": {"lib/netstandard2.1/Elastic.Serilog.Sinks.dll": {"assemblyVersion": "*******", "fileVersion": "8.11.1.0"}}}, "Elastic.Transport/0.4.22": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.4.22.0"}}}, "Enums.NET/4.0.0": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "EPPlus/6.2.6": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.1", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net6.0/EPPlus.dll": {"assemblyVersion": "6.2.6.0", "fileVersion": "6.2.6.0"}}}, "EPPlus.Interfaces/6.1.1": {"runtime": {"lib/net6.0/EPPlus.Interfaces.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "EPPlus.System.Drawing/6.1.1": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "FileHelpers/3.5.2": {"runtime": {"lib/netstandard2.0/FileHelpers.dll": {"assemblyVersion": "3.5.2.0", "fileVersion": "3.5.2.0"}}}, "FireflySoft.RateLimit.AspNetCore/3.0.0": {"dependencies": {"FireflySoft.RateLimit.Core": "3.0.0"}, "runtime": {"lib/net6.0/FireflySoft.RateLimit.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FireflySoft.RateLimit.Core/3.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3", "Nito.AsyncEx": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "StackExchange.Redis": "2.8.31", "System.Linq.Async": "5.1.0"}, "runtime": {"lib/netstandard2.0/FireflySoft.RateLimit.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FSharp.Core/6.0.5": {"runtime": {"lib/netstandard2.1/FSharp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.27404"}}, "resources": {"lib/netstandard2.1/cs/FSharp.Core.resources.dll": {"locale": "cs"}, "lib/netstandard2.1/de/FSharp.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.1/es/FSharp.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.1/fr/FSharp.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.1/it/FSharp.Core.resources.dll": {"locale": "it"}, "lib/netstandard2.1/ja/FSharp.Core.resources.dll": {"locale": "ja"}, "lib/netstandard2.1/ko/FSharp.Core.resources.dll": {"locale": "ko"}, "lib/netstandard2.1/pl/FSharp.Core.resources.dll": {"locale": "pl"}, "lib/netstandard2.1/pt-BR/FSharp.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.1/ru/FSharp.Core.resources.dll": {"locale": "ru"}, "lib/netstandard2.1/tr/FSharp.Core.resources.dll": {"locale": "tr"}, "lib/netstandard2.1/zh-Hans/FSharp.Core.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.1/zh-Hant/FSharp.Core.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "HashDepot/2.0.3": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime": "4.3.1"}, "runtime": {"lib/netstandard2.0/HashDepot.dll": {"assemblyVersion": "2.0.3.0", "fileVersion": "2.0.3.0"}}}, "iTextSharp/5.5.13.3": {"dependencies": {"BouncyCastle": "1.8.9"}, "runtime": {"lib/itextsharp.dll": {"assemblyVersion": "5.5.13.3", "fileVersion": "5.5.13.3"}}}, "JWT/10.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/JWT.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}}, "KYSharp.SM.Core/1.0.1": {"dependencies": {"Portable.BouncyCastle": "1.9.0"}, "runtime": {"lib/netstandard2.0/KYSharp.SM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net6.0/Mapster.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.0"}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net6.0/Mapster.Core.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}}, "Mapster.DependencyInjection/1.0.1": {"dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Mapster.DependencyInjection.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}}}, "MathNet.Numerics.Signed/4.15.0": {"runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"assemblyVersion": "4.15.0.0", "fileVersion": "4.15.0.0"}}}, "MessagePack/2.1.90": {"dependencies": {"MessagePack.Annotations": "2.1.90", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.3"}, "runtime": {"lib/netcoreapp2.1/MessagePack.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.90.20873"}}}, "MessagePack.Annotations/2.1.90": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.90.20873"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.15": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "6.0.15.0", "fileVersion": "6.0.1523.12404"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/6.0.35": {"dependencies": {"MessagePack": "2.1.90", "Microsoft.Extensions.Options": "6.0.0", "StackExchange.Redis": "2.8.31"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {"assemblyVersion": "6.0.35.0", "fileVersion": "6.0.3524.46214"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.TimeProvider/8.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/2.1.7": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.10.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/8.0.1": {"dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.Data.Sqlite.Core/8.0.1": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.123.58002"}}}, "Microsoft.EntityFrameworkCore/6.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.16", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.16.0", "fileVersion": "6.0.1623.17009"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.16": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.16.0", "fileVersion": "6.0.1623.17009"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.16": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.12": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.16", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1222.56411"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.11": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.EntityFrameworkCore.Relational": "6.0.12"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1122.51302"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {}, "Microsoft.Extensions.Hosting/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Logging.Console": "6.0.0", "Microsoft.Extensions.Logging.Debug": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "Microsoft.Extensions.Logging.EventSource": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.1": {"dependencies": {"Microsoft.Extensions.Hosting": "6.0.1", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "System.ServiceProcess.ServiceController": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}}, "Microsoft.Extensions.Logging.Console/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1"}}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.EventLog": "6.0.0"}}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.ObjectPool/5.0.10": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Logging/6.10.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.10.0", "System.IdentityModel.Tokens.Jwt": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens.Saml": "6.8.0", "Microsoft.IdentityModel.Xml": "6.8.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.WsTrust.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11018"}}}, "Microsoft.IdentityModel.Tokens/6.10.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.10.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0", "Microsoft.IdentityModel.Xml": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.Saml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Xml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Xml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.3.1": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MsgPack.Cli/1.0.1": {"dependencies": {"System.CodeDom": "4.4.0", "System.Numerics.Vectors": "4.5.0", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0"}, "runtime": {"lib/netstandard2.0/MsgPack.dll": {"assemblyVersion": "*******", "fileVersion": "*********"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Nito.AsyncEx/5.1.0": {"dependencies": {"Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "Nito.AsyncEx.Interop.WaitHandles": "5.1.0", "Nito.AsyncEx.Oop": "5.1.0", "Nito.AsyncEx.Tasks": "5.1.0", "Nito.Cancellation": "1.1.0"}}, "Nito.AsyncEx.Context/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Coordination/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Interop.WaitHandles/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Interop.WaitHandles.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Oop/5.1.0": {"dependencies": {"Nito.AsyncEx.Coordination": "5.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Oop.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Tasks/5.1.0": {"dependencies": {"Nito.Disposables": "2.2.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Cancellation/1.1.0": {"dependencies": {"Nito.Disposables": "2.2.0"}, "runtime": {"lib/netstandard2.0/Nito.Cancellation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Collections.Deque/1.1.0": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Disposables/2.2.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "NodaTime/3.1.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/NodaTime.dll": {"assemblyVersion": "3.1.10.0", "fileVersion": "3.1.10.0"}}}, "Npgsql/6.0.8": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.16", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.16", "Microsoft.EntityFrameworkCore.Relational": "6.0.12", "Npgsql": "6.0.8"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI/2.6.0": {"dependencies": {"Enums.NET": "4.0.0", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.1", "Portable.BouncyCastle": "1.9.0", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0-beta18", "SixLabors.ImageSharp": "2.1.3", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npoi.Mapper/6.0.0": {"dependencies": {"NPOI": "2.6.0"}, "runtime": {"lib/net6.0/Npoi.Mapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Common/6.6.1": {"dependencies": {"NuGet.Frameworks": "6.6.1"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Configuration/6.6.1": {"dependencies": {"NuGet.Common": "6.6.1", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Frameworks/6.6.1": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Packaging/6.6.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.6.1", "NuGet.Versioning": "6.6.1", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Pkcs": "6.0.4"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Protocol/6.6.1": {"dependencies": {"NuGet.Packaging": "6.6.1"}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Versioning/6.6.1": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Oracle.EntityFrameworkCore/6.21.61": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.12", "Oracle.ManagedDataAccess.Core": "3.21.140"}, "runtime": {"lib/net6.0/Oracle.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.21.1", "fileVersion": "6.0.21.1"}}}, "Oracle.ManagedDataAccess.Core/3.21.140": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.2", "System.Security.Cryptography.Pkcs": "6.0.4"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.12", "Microsoft.Extensions.DependencyInjection": "6.0.1", "MySqlConnector": "2.2.5"}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/1.9.0": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "protobuf-net/3.1.0": {"dependencies": {"protobuf-net.Core": "3.1.22"}, "runtime": {"lib/net5.0/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.0.42307"}}}, "protobuf-net.Core/3.1.22": {"runtime": {"lib/net6.0/protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.22.19916"}}}, "RestSharp/112.1.0": {"runtime": {"lib/net6.0/RestSharp.dll": {"assemblyVersion": "112.1.0.0", "fileVersion": "112.1.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Scrutor/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "runtime": {"lib/netcoreapp3.1/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/3.1.1": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/6.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "7.0.1", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/2.3.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Elasticsearch/10.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}}, "Serilog.Settings.Configuration/7.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net6.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.1.0"}}}, "Serilog.Sinks.Async/1.5.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "Serilog.Sinks.Console/4.1.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.1.0.0"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.FastConsole/2.2.0": {"dependencies": {"Serilog": "3.1.1", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.FastConsole.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.SpectreConsole/0.3.3": {"dependencies": {"FSharp.Core": "6.0.5", "Serilog": "3.1.1", "Spectre.Console": "0.45.0"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.SpectreConsole.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.0-beta18": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.9": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux/2.88.6": {"dependencies": {"SkiaSharp": "2.88.9"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"dependencies": {"SkiaSharp": "2.88.9"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Snowflake.Core/2.0.0": {"runtime": {"lib/netstandard2.0/Snowflake.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Spectre.Console/0.45.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/Spectre.Console.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.45.0.0"}}}, "Spire.Officefor.NETStandard/9.2.1": {"dependencies": {"SkiaSharp": "2.88.9", "System.Buffers": "4.5.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/Spire.Barcode.dll": {"assemblyVersion": "7.2.9.0", "fileVersion": "7.2.9.9420"}, "lib/netstandard2.0/Spire.Doc.dll": {"assemblyVersion": "1*******", "fileVersion": "12.2.1.3420"}, "lib/netstandard2.0/Spire.Email.dll": {"assemblyVersion": "6.5.10.0", "fileVersion": "6.5.10.10420"}, "lib/netstandard2.0/Spire.Pdf.dll": {"assemblyVersion": "10.2.2.0", "fileVersion": "10.2.2.1420"}, "lib/netstandard2.0/Spire.Presentation.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.6420"}, "lib/netstandard2.0/Spire.XLS.dll": {"assemblyVersion": "14.2.1.0", "fileVersion": "14.2.1.5420"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.core/2.1.6": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.Data.Sqlite": "8.0.1", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "6.0.8", "Oracle.ManagedDataAccess.Core": "3.21.140", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.6.0", "SqlSugarCore.Kdbndp": "9.3.6.801", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/8.6.0": {"dependencies": {"System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.27409", "fileVersion": "8.3.1.27409"}}}, "SqlSugarCore.Kdbndp/9.3.6.801": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.6.618", "fileVersion": "9.3.6.801"}}}, "StackExchange.Redis/2.8.31": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.31.52602"}}}, "StackExchange.Redis.Extensions.AspNetCore/10.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis.Extensions.Core/10.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.1", "StackExchange.Redis": "2.8.31"}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis.Extensions.MsgPack/10.2.0": {"dependencies": {"MsgPack.Cli": "1.0.1", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.MsgPack.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis.Extensions.Newtonsoft/10.2.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.Newtonsoft.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/6.2.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.2.3", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3", "Swashbuckle.AspNetCore.SwaggerUI": "6.2.3"}}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.OpenApi": "1.3.1", "Scrutor": "3.3.0", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3"}, "runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"dependencies": {"Microsoft.OpenApi": "1.3.1"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.0": {}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.25519.3"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Immutable/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.2": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Hashing/7.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Pipelines/5.0.1": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Async/5.1.0": {"runtime": {"lib/netcoreapp3.1/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Dynamic.Core/1.3.3": {"runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Memory/4.5.5": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.6.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.6.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "6.0.0"}}, "System.Security.Cryptography.Pkcs/6.0.4": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1923.31806"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1923.31806"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.4"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.822.36306"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.8.1": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.ServiceModel.Federation/4.8.1": {"dependencies": {"Microsoft.IdentityModel.Protocols.WsTrust": "6.8.0", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.Primitives": "4.10.0", "System.ServiceModel.Security": "4.8.1"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Federation.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.ServiceModel.Http/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.NetTcp/4.8.1": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.ServiceModel.Primitives/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.Security/4.8.1": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.ServiceProcess.ServiceController/6.0.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.3": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.3"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "Unchase.Swashbuckle.AspNetCore.Extensions/2.6.12": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "6.2.3"}, "runtime": {"lib/netstandard2.0/Unchase.Swashbuckle.AspNetCore.Extensions.dll": {"assemblyVersion": "2.6.12.0", "fileVersion": "2.6.12.0"}}}, "XH.LAB.UTILS/6.25.301.13": {"dependencies": {"Autofac.Extras.DynamicProxy": "6.0.1", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Spire.Officefor.NETStandard": "9.2.1", "SqlSugarCore": "*********", "Xinghe.Utility": "6.25.206", "Yarp.ReverseProxy": "2.1.0", "iTextSharp": "5.5.13.3"}, "runtime": {"lib/net6.0/XH.LAB.UTILS.dll": {"assemblyVersion": "6.25.301.13", "fileVersion": "6.25.301.13"}}}, "Xinghe.Utility/6.25.206": {"dependencies": {"AspNetCoreRateLimit": "4.0.2", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.0", "Autofac": "6.4.0", "Autofac.Extensions.DependencyInjection": "8.0.0", "Castle.Core": "5.1.0", "Dapper": "2.0.123", "Elastic.Clients.Elasticsearch": "8.15.0", "Elastic.Serilog.Sinks": "8.11.1", "FileHelpers": "3.5.2", "HashDepot": "2.0.3", "JWT": "10.1.1", "KYSharp.SM.Core": "1.0.1", "Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.15", "Microsoft.AspNetCore.SignalR.StackExchangeRedis": "6.0.35", "Microsoft.EntityFrameworkCore": "6.0.16", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.11", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Hosting.WindowsServices": "6.0.1", "NPOI": "2.6.0", "Newtonsoft.Json": "13.0.3", "NodaTime": "3.1.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.8", "Npoi.Mapper": "6.0.0", "Oracle.EntityFrameworkCore": "6.21.61", "Oracle.ManagedDataAccess.Core": "3.21.140", "Polly": "7.2.3", "Pomelo.EntityFrameworkCore.MySql": "6.0.2", "RestSharp": "112.1.0", "Serilog.AspNetCore": "6.1.0", "Serilog.Enrichers.Environment": "2.3.0", "Serilog.Formatting.Elasticsearch": "10.0.0", "Serilog.Settings.Configuration": "7.0.1", "Serilog.Sinks.Async": "1.5.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.SpectreConsole": "0.3.3", "Snowflake.Core": "2.0.0", "SqlSugarCore": "*********", "SqlSugarCore.Dm": "8.6.0", "StackExchange.Redis": "2.8.31", "StackExchange.Redis.Extensions.AspNetCore": "10.2.0", "StackExchange.Redis.Extensions.Core": "10.2.0", "StackExchange.Redis.Extensions.MsgPack": "10.2.0", "StackExchange.Redis.Extensions.Newtonsoft": "10.2.0", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Filters": "7.0.5", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "System.Data.SqlClient": "4.8.6", "System.Linq.Dynamic.Core": "1.3.3", "System.ServiceModel.Duplex": "4.8.1", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.NetTcp": "4.8.1", "System.ServiceModel.Security": "4.8.1", "Unchase.Swashbuckle.AspNetCore.Extensions": "2.6.12", "Serilog": "3.1.1"}, "runtime": {"lib/net6.0/Xinghe.Utility.dll": {"assemblyVersion": "6.25.206.0", "fileVersion": "6.25.206.0"}, "lib/net6/ClickHouse.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Yarp.ReverseProxy/2.1.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.0", "System.Collections.Immutable": "8.0.0", "System.IO.Hashing": "7.0.0"}, "runtime": {"lib/net6.0/Yarp.ReverseProxy.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.100.23.56701"}}}, "XH.H82.Base/1.0.0": {"dependencies": {"EPPlus": "6.2.6", "EasyCaching.Serialization.Protobuf": "1.7.0", "FireflySoft.RateLimit.AspNetCore": "3.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.15", "RestSharp": "112.1.0", "Serilog.Sinks.FastConsole": "2.2.0", "Serilog.Sinks.SpectreConsole": "0.3.3", "SkiaSharp": "2.88.9", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Filters": "7.0.5", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "Unchase.Swashbuckle.AspNetCore.Extensions": "2.6.12", "XH.H82.Models": "1.0.0", "iTextSharp": "5.5.13.3"}, "runtime": {"XH.H82.Base.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "XH.H82.IServices/1.0.0": {"dependencies": {"EPPlus": "6.2.6", "XH.H82.Base": "1.0.0", "XH.H82.Models": "1.0.0"}, "runtime": {"XH.H82.IServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "XH.H82.Models/1.0.0": {"dependencies": {"EPPlus": "6.2.6", "XH.LAB.UTILS": "6.25.301.13", "Xinghe.Utility": "6.25.206", "protobuf-net.Core": "3.1.22"}, "runtime": {"XH.H82.Models.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"XH.H82.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AspNetCoreRateLimit/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-FzXAJFgaRjKfnKAVwjEEC7OAGQM5v/I3sQw2tpzmR0yHTCGhUAxZzDuwZiXTk8XLrI6vovzkqKkfKmiDl3nYMg==", "path": "aspnetcoreratelimit/4.0.2", "hashPath": "aspnetcoreratelimit.4.0.2.nupkg.sha512"}, "Autofac/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tkFxl6wAPuwVhrlN8wuNADnd+k2tv4ReP7ZZSL0vjfcN0RcfC9v25ogxK6b03HC7D4NwWjSLf1G/zTG8Bw43wQ==", "path": "autofac/6.4.0", "hashPath": "autofac.6.4.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nGrXNpQX2FiZpIBydK9cxZnnoqP/cUd3k/53uRERYEqLtWzKtE15R6L+j5q5ax5Rv/+3wAIkOaPePkahfqrwjg==", "path": "autofac.extensions.dependencyinjection/8.0.0", "hashPath": "autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+ulCOXUjbJ5dljBPZf3gRxQNPRGqg/h1cNTZ4SpXK3qiamTfRsW3gXs2w/IJ+CIDmHrFGYXLqzNd5etvzwB8kA==", "path": "autofac.extras.dynamicproxy/6.0.1", "hashPath": "autofac.extras.dynamicproxy.6.0.1.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XCJ4E3oKrbRl1qY9Mr+7uyC0xZj1+bqQjmQRWTiTKiVuuXTny+7YFWHi20tPjwkMukLbicN6yGlDy5PZ4wyi1w==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.0.nupkg.sha512"}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "BouncyCastle/1.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-axnBgvdD5n+FnEG6efk/tfKuMFru7R/EoISH9zjh319yb3HD24TEHSAbNN/lTRT2ulOGRxDgOsCjkuk08iwWPg==", "path": "bouncycastle/1.8.9", "hashPath": "bouncycastle.1.8.9.nupkg.sha512"}, "Castle.Core/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-31UJpTHOiWq95CDOHazE3Ub/hE/PydNWsJMwnEVTqFFP4WhAugwpaVGxzOxKgNeSUUeqS2W6lxV+q7u1pAOfXg==", "path": "castle.core/5.1.0", "hashPath": "castle.core.5.1.0.nupkg.sha512"}, "Dapper/2.0.123": {"type": "package", "serviceable": true, "sha512": "sha512-RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "path": "dapper/2.0.123", "hashPath": "dapper.2.0.123.nupkg.sha512"}, "EasyCaching.Core/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RU4mL51t7a1rXSK12LX4WaxpHap8cD3HDqJQkI1Y+BHGwv29nA4nuh4Rw29YlFz+jME6AdgnkHlP5sjJTLWOsg==", "path": "easycaching.core/1.7.0", "hashPath": "easycaching.core.1.7.0.nupkg.sha512"}, "EasyCaching.Serialization.Protobuf/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AnoLv6GXEBFjxVZrdPw0H0ro+n4MHUeUzAEwKX8/WSjWTtcMLkqBpVWr28RHk+Hy7ItpcSvusnJcLL6FB4MjBA==", "path": "easycaching.serialization.protobuf/1.7.0", "hashPath": "easycaching.serialization.protobuf.1.7.0.nupkg.sha512"}, "Elastic.Channels/0.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-IcL3NteG4QU6FlqMbUw05UGtZcHG7RgTGqiFotV/4kIua79l0WTXpSWTaN0WkRs2C7nyjW2QdITIEJRDHFYgsA==", "path": "elastic.channels/0.7.0", "hashPath": "elastic.channels.0.7.0.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/8.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-cj3mYmQyXtxelaOhPrSfuwjGYAaN49/1BwN7x9oFVaQlerE00FoFH00ID9LstXSsq7gtrq70Vcakl92V7rjT5w==", "path": "elastic.clients.elasticsearch/8.15.0", "hashPath": "elastic.clients.elasticsearch.8.15.0.nupkg.sha512"}, "Elastic.CommonSchema/8.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z4vl/VUbH7A3UcyL0xe2s1fdATt2Hro3NBhshnZ/yRNbDwEwuQnbj2IHcv9+L/FfT+jgZricXgD769NTqOedhA==", "path": "elastic.commonschema/8.11.1", "hashPath": "elastic.commonschema.8.11.1.nupkg.sha512"}, "Elastic.CommonSchema.Serilog/8.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-QPRqMOvlRx5rEU9KGtnhHmgcy7yOm/b4v8/GEzIwRcRW50B7AukCuFeNzewW5guuwPxGsSgveIQvHHo5dlsoLg==", "path": "elastic.commonschema.serilog/8.11.1", "hashPath": "elastic.commonschema.serilog.8.11.1.nupkg.sha512"}, "Elastic.Ingest.Elasticsearch/0.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-7MmRkwVYphGhXAVXyrQxhnNpiyrkwVIzNuOJy/MXjPZPc0Tc6bwXLThqyCxR1JFE/hugHeoMFEAdb/TABHaJ6g==", "path": "elastic.ingest.elasticsearch/0.7.0", "hashPath": "elastic.ingest.elasticsearch.0.7.0.nupkg.sha512"}, "Elastic.Ingest.Elasticsearch.CommonSchema/8.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-qkeBbOt7ZtcK7PiOhUH5l6FOSHnqzLuDCfmxU0xRgN8X5WC95IaGK7YD5FEwFGPiOA7206GBVT0e/9TFXdrZGw==", "path": "elastic.ingest.elasticsearch.commonschema/8.11.1", "hashPath": "elastic.ingest.elasticsearch.commonschema.8.11.1.nupkg.sha512"}, "Elastic.Ingest.Transport/0.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ury3crl3KPMe8TCluusrq0+hzloYp0/kslhEdtXdnQ0qhHmI1CCJCYfDKK/tE7VSjnuEA9tFkdoUp2M+PqrmFg==", "path": "elastic.ingest.transport/0.7.0", "hashPath": "elastic.ingest.transport.0.7.0.nupkg.sha512"}, "Elastic.Serilog.Sinks/8.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-cbKyJr0e8Ix/MiEkjVMbCxGUT4BVLd5AsBXrHkhvyQnrzWOfS19m12YazFXZBVmsDEnjiYM16Svd/62JiUJSSA==", "path": "elastic.serilog.sinks/8.11.1", "hashPath": "elastic.serilog.sinks.8.11.1.nupkg.sha512"}, "Elastic.Transport/0.4.22": {"type": "package", "serviceable": true, "sha512": "sha512-9J5GPHJcT8sewn2zVfWTrsCQvfQYgUiY/jx+IRjWUk7XNPd837qfEL42I5baH69cyW/e4RoDy8v76yxqz95tDw==", "path": "elastic.transport/0.4.22", "hashPath": "elastic.transport.0.4.22.nupkg.sha512"}, "Enums.NET/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d47SgeuGxKpalKhYoHqFkDPmO9SoE3amSwVNDoUdy4d675/tX7bLyZFHdjfo3Tobth9Y80VnjfasQ/PD4LqUuA==", "path": "enums.net/4.0.0", "hashPath": "enums.net.4.0.0.nupkg.sha512"}, "EPPlus/6.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-eAPQbEHPCsEh8pAICPP4YYhhBuUHq2HH6oLqHzfgUP+GynB7MFHL+Jjx1X7rZPD+iA3c12ilz/WygRylc+xriw==", "path": "epplus/6.2.6", "hashPath": "epplus.6.2.6.nupkg.sha512"}, "EPPlus.Interfaces/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "path": "epplus.interfaces/6.1.1", "hashPath": "epplus.interfaces.6.1.1.nupkg.sha512"}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "path": "epplus.system.drawing/6.1.1", "hashPath": "epplus.system.drawing.6.1.1.nupkg.sha512"}, "FileHelpers/3.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-Z0jwjF4AmDBzRWfHFMdrcuBgmoQ2ChLaTmhRjnmReoUPUkT/+5StzWCB0N0RpslYeXiSyvcntcZNLtY58todFw==", "path": "filehelpers/3.5.2", "hashPath": "filehelpers.3.5.2.nupkg.sha512"}, "FireflySoft.RateLimit.AspNetCore/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-n1yEOIE7wlHJiDr0pL/I37I+35B1TO76xyyY+Wl0pvU4K7RwhRgH99f53bkxYeXwHwInxTb6DGPJsKDWDyK09w==", "path": "fireflysoft.ratelimit.aspnetcore/3.0.0", "hashPath": "fireflysoft.ratelimit.aspnetcore.3.0.0.nupkg.sha512"}, "FireflySoft.RateLimit.Core/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VO2wzTkgIZbTfUOEr+8ohQ1aB8gyXTK/KCrA9E2LKfa4r7iAfyuZOF3JNmuOtbHUODk5PfwCEOcOBodJuNzOKg==", "path": "fireflysoft.ratelimit.core/3.0.0", "hashPath": "fireflysoft.ratelimit.core.3.0.0.nupkg.sha512"}, "FSharp.Core/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-FwdQVtpj34xt8vKyFUUeNIS+obWlEnSrSW7y1ivRVts/ZsrUsKyOd0bZehgFhWdnB/NBsa9DCWvNFMTO0XDFcg==", "path": "fsharp.core/6.0.5", "hashPath": "fsharp.core.6.0.5.nupkg.sha512"}, "HashDepot/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-K033sUok3i8MhGM/10tX6Bq/7MTWZJxS3eAU1z+zFv6k2+1FV9+gEAuvZYHYYJeB4v8rbkaL9fHzLoHBZOTXDA==", "path": "hashdepot/2.0.3", "hashPath": "hashdepot.2.0.3.nupkg.sha512"}, "iTextSharp/5.5.13.3": {"type": "package", "serviceable": true, "sha512": "sha512-vtnMhTEJdZFCkLqaQLjD8aqTBIVKHPrSFuSXnxbLEJlvE/j/l88fvG9wtsejXTmhtErMo0lA9T2LdfdfbwplYw==", "path": "itextsharp/5.5.13.3", "hashPath": "itextsharp.5.5.13.3.nupkg.sha512"}, "JWT/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OJ7Vr6Rf539Ty8iw1FDcfM2Adp7BVT0tOEld5NZnxcVguzczFfv2dsdUKGWf9FVc4VCcVRXv0KZ7Q3/zs+b0Iw==", "path": "jwt/10.1.1", "hashPath": "jwt.10.1.1.nupkg.sha512"}, "KYSharp.SM.Core/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ns0yhMLNhYVR6PJKiZ6LGi2MB4KnBl/L4lntvpiGZr5TOKEc9KOWoVED4XKP9By1G9TE9EOkZcLbcpa0xdBLMA==", "path": "kysharp.sm.core/1.0.1", "hashPath": "kysharp.sm.core.1.0.1.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "path": "mapster.dependencyinjection/1.0.1", "hashPath": "mapster.dependencyinjection.1.0.1.nupkg.sha512"}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "path": "mathnet.numerics.signed/4.15.0", "hashPath": "mathnet.numerics.signed.4.15.0.nupkg.sha512"}, "MessagePack/2.1.90": {"type": "package", "serviceable": true, "sha512": "sha512-frdAkIQyILYjAhH8Q107N77eccLjnkYr8/L+3vHeCezI0rvfCB2xiZjin936QFtskLBmgW1D9y6bKs45zddVLQ==", "path": "messagepack/2.1.90", "hashPath": "messagepack.2.1.90.nupkg.sha512"}, "MessagePack.Annotations/2.1.90": {"type": "package", "serviceable": true, "sha512": "sha512-YnS4qdOMFJuC8TDvXK8ECjWn+hI/B6apwMSgQsiynVXxsvFiiSyun7OXgVT7KxkR8qLinFuASzoG/5zv3kemwQ==", "path": "messagepack.annotations/2.1.90", "hashPath": "messagepack.annotations.2.1.90.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-FvMT6e6EzBVeKzEh5vT25LrWtc3HNcnlanbFUli+PIoL8WKuCUOUSpwN7XFJJBLUW3S+QT/xG+Qa6jo+FlM1zg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/6.0.15", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.6.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vbFDyKsSYBnxl3+RABtN79b0vsTcG66fDY8vD6Nqvu9uLtSej70Q5NcbGlnN6bJpZci5orSdgFTHMhBywivDPg==", "path": "microsoft.aspnetcore.http.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-M8Gk5qrUu5nFV7yE3SZgATt/5B1a5Qs8ZnXXeO/Pqu68CEiBHJWc10sdGdO5guc3zOFdm7H966mVnpZtEX4vSA==", "path": "microsoft.aspnetcore.http.extensions/2.1.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UmkUePxRjsQW0j5euFFscBwjvTu25b8+qIK/2fI3GvcqQ+mkwgbWNAT8b/Gkoei1m2bTWC07lSdutuRDPPLcJA==", "path": "microsoft.aspnetcore.http.features/2.1.0", "hashPath": "microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/6.0.35": {"type": "package", "serviceable": true, "sha512": "sha512-NC4bU6lQNQ8G9fkY+ArQpLNH6E0zki6lgXLxbBXaU+TPhTM0g+wbHCx8PsDxw0J097XpRO0HBnov9xSFFC0tSw==", "path": "microsoft.aspnetcore.signalr.stackexchangeredis/6.0.35", "hashPath": "microsoft.aspnetcore.signalr.stackexchangeredis.6.0.35.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f5Kr5JepAbiGo7uDmhgvMqhntwxqXNn6/IpTBSSI4cuHhgnJGrLxFRhMjVpRkLPp6zJXO0/G0l3j9p9zSJxa+w==", "path": "microsoft.bcl.timeprovider/8.0.0", "hashPath": "microsoft.bcl.timeprovider.8.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-dSdlcXPszeOjjDX9a0buMFgYqKrI5bTxdSgX3JyCa+OL80NUstJSxOJr0j9oOn8mpP5PgWeRC2bVf/Zf2Cjv+g==", "path": "microsoft.data.sqlclient/2.1.7", "hashPath": "microsoft.data.sqlclient.2.1.7.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+7uDWNYZmLrVq9eABAKwy1phGbpoFVohKCUoh/nGg9WiBwi856EkAJYFiQhTJWoXxzpInkLFj/6KACoSB7ODYg==", "path": "microsoft.data.sqlite/8.0.1", "hashPath": "microsoft.data.sqlite.8.0.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s8C8xbwMb79EqzTaIhwiBrYtbv6ATnUW19pJed4fKVgN5K4VPQ7JUGqBLztknvD6EJIMKrfRnINGTjnZghrDGw==", "path": "microsoft.data.sqlite.core/8.0.1", "hashPath": "microsoft.data.sqlite.core.8.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-Phfd0D0ew5VHGtM/j7o5HYapbt4R7d9Tkm4z+L4nmkhcEzWZjeuBCUBTOgJI7U4gI916QHliDCcp53FF7NBHIA==", "path": "microsoft.entityframeworkcore/6.0.16", "hashPath": "microsoft.entityframeworkcore.6.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-ubRazZ/5BbOI4B11HcNAr0v4qE9MDcAAZtEFPqkGrnz4ClciA8qsM1Rd8IB25EoxfLnOv/Lnz7aW+/3G+D1MCg==", "path": "microsoft.entityframeworkcore.abstractions/6.0.16", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-gN2yqqz2R6jj80SUTc036cPPM6xv3e/T1qpQI8ZfWJG3hXjcISI93uZtnyLVpn1712SklQR1WMIZ/bGYUurjcA==", "path": "microsoft.entityframeworkcore.analyzers/6.0.16", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-HBtRGHtF0Vf+BIQTkRGiopmE5rLYhj59xPpd17S1tLgYpiHDVbepCuHwh5H63fzjO99Z4tW5wmmEGF7KnD91WQ==", "path": "microsoft.entityframeworkcore.relational/6.0.12", "hashPath": "microsoft.entityframeworkcore.relational.6.0.12.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ua2+N4y/t5LZ78sO4ZBeP4d7mp/NFFWN2+wktMXCCBl3tR1s06r0I0W6lQz36Z4F1J45Lwb4O8djocqnEEYC2Q==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.11", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.11.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "path": "microsoft.extensions.configuration/6.0.0", "hashPath": "microsoft.extensions.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tgU4u7bZsoS9MKVRiotVMAwHtbREHr5/5zSEV+JPhg46+ox47Au84E3D2IacAaB0bk5ePNaNieTlPrfjbbRJkg==", "path": "microsoft.extensions.configuration.binder/7.0.0", "hashPath": "microsoft.extensions.configuration.binder.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nL1qCkZ1Oxx14ZTzgo4MmlO7tso7F+TtMZAY2jUAtTLyAcDp+EDjk3RqafoKiNaePyPvvlleEcBxh3b2Hzl1g==", "path": "microsoft.extensions.configuration.commandline/6.0.0", "hashPath": "microsoft.extensions.configuration.commandline.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pnyXV1LFOsYjGveuC07xp0YHIyGq7jRq5Ncb5zrrIieMLWVwgMyYxcOH0jTnBedDT4Gh1QinSqsjqzcieHk1og==", "path": "microsoft.extensions.configuration.environmentvariables/6.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "path": "microsoft.extensions.configuration.json/6.0.0", "hashPath": "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Fy8yr4V6obi7ZxvKYI1i85jqtwMq8tqyxQVZpRSkgeA8enqy/KvBIMdcuNdznlxQMZa72mvbHqb7vbg4Pyx95w==", "path": "microsoft.extensions.configuration.usersecrets/6.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hbmizc9KPWOacLU8Z8YMaBG6KWdZFppczYV/KwnPGU/8xebWxQxdDeJmLOgg968prb7g2oQgnp6JVLX6lgby8g==", "path": "microsoft.extensions.hosting/6.0.1", "hashPath": "microsoft.extensions.hosting.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0vaOeMeDpVUqvOJ0pHugoOqTZULdq7c0kZLzzlrQwUZ7ScUat27d8hrK3IxJ7FWF3OBZXSjA2nWYRcdA5WG9wg==", "path": "microsoft.extensions.hosting.windowsservices/6.0.1", "hashPath": "microsoft.extensions.hosting.windowsservices.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "path": "microsoft.extensions.logging.abstractions/7.0.1", "hashPath": "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "path": "microsoft.extensions.logging.configuration/6.0.0", "hashPath": "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gsqKzOEdsvq28QiXFxagmn1oRB9GeI5GgYCkoybZtQA0IUb7QPwf1WmN3AwJeNIsadTvIFQCiVK0OVIgKfOBGg==", "path": "microsoft.extensions.logging.console/6.0.0", "hashPath": "microsoft.extensions.logging.console.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M9g/JixseSZATJE9tcMn9uzoD4+DbSglivFqVx8YkRJ7VVPmnvCEbOZ0AAaxsL1EKyI4cz07DXOOJExxNsUOHw==", "path": "microsoft.extensions.logging.debug/6.0.0", "hashPath": "microsoft.extensions.logging.debug.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rlo0RxlMd0WtLG3CHI0qOTp6fFn7MvQjlrCjucA31RqmiMFCZkF8CHNbe8O7tbBIyyoLGWB1he9CbaA5iyHthg==", "path": "microsoft.extensions.logging.eventlog/6.0.0", "hashPath": "microsoft.extensions.logging.eventlog.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BeDyyqt7nkm/nr+Gdk+L8n1tUT/u33VkbXAOesgYSNsxDM9hJ1NOBGoZfj9rCbeD2+9myElI6JOVVFmnzgeWQA==", "path": "microsoft.extensions.logging.eventsource/6.0.0", "hashPath": "microsoft.extensions.logging.eventsource.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "path": "microsoft.extensions.objectpool/5.0.10", "hashPath": "microsoft.extensions.objectpool.5.0.10.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-0qjS31rN1MQTc46tAYbzmMTSRfdV5ndZxSjYxIGqKSidd4wpNJfNII/pdhU5Fx8olarQoKL9lqqYw4yNOIwT0Q==", "path": "microsoft.identitymodel.jsonwebtokens/6.10.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbcwV6esnNzhZZ/VP87dji6VrUBLB5rxnZBkDMqNYpyG+nrBnBsbm4PUYLCBMUflHCM9EMLDG0rLnqqT+l0ldA==", "path": "microsoft.identitymodel.logging/6.10.0", "hashPath": "microsoft.identitymodel.logging.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-DFyXD0xylP+DknCT3hzJ7q/Q5qRNu0hO/gCU90O0ATdR0twZmlcuY9RNYaaDofXKVbzcShYNCFCGle2G/o8mkg==", "path": "microsoft.identitymodel.protocols/6.10.0", "hashPath": "microsoft.identitymodel.protocols.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-LVvMXAWPbPeEWTylDrxunlHH2wFyE4Mv0L4gZrJHC4HTESbWHquKZb/y/S8jgiQEDycOP0PDQvbG4RR/tr2TVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.10.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-KiRRp/pAtbJtyK7wPIiUW1FMna1T0IEizDWFL9R0C60jk6t66U95fo0SPl3ztOkmnS4v7uF1zWjfAEgZ/i+Zhg==", "path": "microsoft.identitymodel.protocols.wstrust/6.8.0", "hashPath": "microsoft.identitymodel.protocols.wstrust.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qbf1NslutDB4oLrriYTJpy7oB1pbh2ej2lEHd2IPDQH9C74ysOdhU5wAC7KoXblldbo7YsNR2QYFOqQM/b0Rsg==", "path": "microsoft.identitymodel.tokens/6.10.0", "hashPath": "microsoft.identitymodel.tokens.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRbtJ7Kvr2RMcXi4g4ta3og/wX0GZpLGfb/h7aohwNAaUtCooRpx7Gl8Cv7tn4FDAp6MZwBQL/w0jMeyVTkjPQ==", "path": "microsoft.identitymodel.tokens.saml/6.8.0", "hashPath": "microsoft.identitymodel.tokens.saml.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-p2DOCNVVOQpxfx9c3FW0kJve2jAAZB/zecWsi9S5fpznbJ/VcH0zxKdz6wIXjDQgwf2xg/u/k58uHiS/o+0qiA==", "path": "microsoft.identitymodel.xml/6.8.0", "hashPath": "microsoft.identitymodel.xml.6.8.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-T5ahjOqWFMTSb9wFHKFNAcGXm35BxbUbwARtAPLSSPPFehcLz5mwDsKO1RR9R2aZ2Lk1BNQC7Ja63onOBE6rpA==", "path": "microsoft.io.recyclablememorystream/2.2.1", "hashPath": "microsoft.io.recyclablememorystream.2.2.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "path": "microsoft.net.http.headers/2.1.0", "hashPath": "microsoft.net.http.headers.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-2X5CCFJCnx6v86fnpOg4TKlU1ba7MSf1yakeT7VI4846s7i6fOkERwStX94Rcq8wJsLyQYsUitd6vR38viePeA==", "path": "microsoft.openapi/1.3.1", "hashPath": "microsoft.openapi.1.3.1.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MsgPack.Cli/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-od0WujAAuPuRLoGfGNfE2iRPP7Kj+o174Oed2uirT03/Dp4rkccnEIqQl/QRHjly79sMwh6xKaIVsLqYwWtiHA==", "path": "msgpack.cli/1.0.1", "hashPath": "msgpack.cli.1.0.1.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Nito.AsyncEx/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>ahZdW0fRLnoym5lGtyWpFUZ94I5s0wLgYF5RHhK8RUhXpCiEX+185qjNvkse3H9WJV2/pFfPlKZHv8Eej7Hw==", "path": "nito.asyncex/5.1.0", "hashPath": "nito.asyncex.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "path": "nito.asyncex.context/5.1.0", "hashPath": "nito.asyncex.context.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "path": "nito.asyncex.coordination/5.1.0", "hashPath": "nito.asyncex.coordination.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Interop.WaitHandles/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-wFm3lrXXNPBtZHjLI21xhcADoh5CzO5KKNO38ybLO/CcL9zMUWWfsNiAFbw8JGp/wHoxhfdEUlThBnY3XaLR/w==", "path": "nito.asyncex.interop.waithandles/5.1.0", "hashPath": "nito.asyncex.interop.waithandles.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Oop/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J5DVgQrmE9qMNhK2eEMsuEw7V7cw7MIPrv3jqqQWolzDXkOxJFFYKUK+4dnC6UAEmum3xRVD2oBAoXg0vdYDDQ==", "path": "nito.asyncex.oop/5.1.0", "hashPath": "nito.asyncex.oop.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "path": "nito.asyncex.tasks/5.1.0", "hashPath": "nito.asyncex.tasks.5.1.0.nupkg.sha512"}, "Nito.Cancellation/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BSezc6jsEEAoa8UtVjQ6Qr/D5xX+FozlDKFHAvDeTv24I7ZZmmfbFxEmdjaSLnrboz1WMRjUKCQwZw7Gf4+WcA==", "path": "nito.cancellation/1.1.0", "hashPath": "nito.cancellation.1.1.0.nupkg.sha512"}, "Nito.Collections.Deque/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "path": "nito.collections.deque/1.1.0", "hashPath": "nito.collections.deque.1.1.0.nupkg.sha512"}, "Nito.Disposables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "path": "nito.disposables/2.2.0", "hashPath": "nito.disposables.2.2.0.nupkg.sha512"}, "NodaTime/3.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-56V8ELg9Az1S0Nij44xILAtfUQs3WTLIwbTQTuWZxL8EagsfUkkPAoWxv/HyZccmfaUEqFHfHiAuqfj/NfbfrA==", "path": "nodatime/3.1.10", "hashPath": "nodatime.3.1.10.nupkg.sha512"}, "Npgsql/6.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-wKa8MJEJaj0xQXUQZGv7q/KfPID23jSSvFFtljMworrv7dNajr0GN8PCU1SpywqHjMWdYEfK29DY1aYbiISbQg==", "path": "npgsql/6.0.8", "hashPath": "npgsql.6.0.8.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-YJRpO+3wXQyWuwRUCVJj/Rsn46sY0bZ6uCGOEFApiRe0ZYJ6N6TxZUWKbTNJYjesickcLGzynOerpSbDJX1AYg==", "path": "npgsql.entityframeworkcore.postgresql/6.0.8", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.8.nupkg.sha512"}, "NPOI/2.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pwjo65CUH3MiRnBEbVo8ff31ZrDGdGyyFJyAEncmbTQ0/gYgDkBUnGKm20aLpdwCpPNLzvapZm8v5tx4S6qAWg==", "path": "npoi/2.6.0", "hashPath": "npoi.2.6.0.nupkg.sha512"}, "Npoi.Mapper/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-czueOx+9qMSi8zBogXpKufSVNUeDZ/vGcMJbXgSEe01EGX+iE64fvXeIR7waCcTgh5ecI2/Lwbbek3DcvgahNA==", "path": "npoi.mapper/6.0.0", "hashPath": "npoi.mapper.6.0.0.nupkg.sha512"}, "NuGet.Common/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-hW5NtShErO3qbdkyv7doCRsFNK9Rlcc7mVjYM+hg1sOAWheTh/oo95DzNbsZthiqyHZfaioopfWtzmoxNw9h4g==", "path": "nuget.common/6.6.1", "hashPath": "nuget.common.6.6.1.nupkg.sha512"}, "NuGet.Configuration/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-9WbK8wgwPfRpSwuG+ZhMshE48qUYvPIw7VNLCncrq/in4vE6SGsuawPSxPJkkLBtcKTGbPMez5JDvUf6vEBgKg==", "path": "nuget.configuration/6.6.1", "hashPath": "nuget.configuration.6.6.1.nupkg.sha512"}, "NuGet.Frameworks/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-iRtDhL0zPqVw037fHEK9bQljTKPuOHhfIkz86/IH2P8eetr910HTTe5G8lJTuzZHh592Ze/sYhh173HIFjPSfg==", "path": "nuget.frameworks/6.6.1", "hashPath": "nuget.frameworks.6.6.1.nupkg.sha512"}, "NuGet.Packaging/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-GwhFi2Ep4YzAGQFYz1OsMVNfiJ1M46nyCgHQ7xjJSMvxDYFgodR1RqVugWFMbIUUq6I8iYASwp5lpHXvITeuIQ==", "path": "nuget.packaging/6.6.1", "hashPath": "nuget.packaging.6.6.1.nupkg.sha512"}, "NuGet.Protocol/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-HhKLsK6Q0NNp6qb0T26GLR5gCTRZu+gzqDVK4xqXHZmsolaDVIdIYpn44b2etaVYLzNJCvgRkw+I422u2bIvMw==", "path": "nuget.protocol/6.6.1", "hashPath": "nuget.protocol.6.6.1.nupkg.sha512"}, "NuGet.Versioning/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Wm/AOFICTIrCgbVxv9dNWusraTzcggbo5W4ao7hD8NNVq911an9TGwW+uNuYc8I5PkpTeMuSXneV2u6hbi1P4w==", "path": "nuget.versioning/6.6.1", "hashPath": "nuget.versioning.6.6.1.nupkg.sha512"}, "Oracle.EntityFrameworkCore/6.21.61": {"type": "package", "serviceable": true, "sha512": "sha512-X8vCHqyexPekPyoiUkoQMVxXzsB8QBKM8C1noFrTEev5/AgfnyGDgzfoF9Y8MDUS4jSiVzJafqc/olk7TlBWoA==", "path": "oracle.entityframeworkcore/6.21.61", "hashPath": "oracle.entityframeworkcore.6.21.61.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.140": {"type": "package", "serviceable": true, "sha512": "sha512-F1c4adtCGe6C+h04fryfAeRC3XsTG73Xd6vYhpD/6ABfdWo4IOTLy8EZs4vhya2+OR0BME/751Y3BCZcWdNYiQ==", "path": "oracle.manageddataaccess.core/3.21.140", "hashPath": "oracle.manageddataaccess.core.3.21.140.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "hashPath": "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512"}, "Portable.BouncyCastle/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "path": "portable.bouncycastle/1.9.0", "hashPath": "portable.bouncycastle.1.9.0.nupkg.sha512"}, "protobuf-net/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-dZjLX2TzcEkPy/oZfCB/GE32+oJlX58w/zNMkJ1JPdMeO1oTSfiXVxgFagya8xblBhHpr1v4PBbt3Q13Yvvtwg==", "path": "protobuf-net/3.1.0", "hashPath": "protobuf-net.3.1.0.nupkg.sha512"}, "protobuf-net.Core/3.1.22": {"type": "package", "serviceable": true, "sha512": "sha512-82iIFBuQW2K8kixHQ1Eo6qWfnztNYRGU6OGqGmq7c0r8T9X9y9j79SHQSeI/hK3gK6EvHbYMfeGhpRJzA/zv1Q==", "path": "protobuf-net.core/3.1.22", "hashPath": "protobuf-net.core.3.1.22.nupkg.sha512"}, "RestSharp/112.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "path": "restsharp/112.1.0", "hashPath": "restsharp.112.1.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Scrutor/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BwqCnFzp2/Z+pq17iztxlIkR/ZANyPRR4PdE57WL1w/JW4AM/2imoxBWTL3+G+YXA46ce4s9OUgwWqTXYrtI8A==", "path": "scrutor/3.3.0", "hashPath": "scrutor.3.3.0.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.AspNetCore/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iMwFUJDN+/yWIPz4TKCliagJ1Yn//SceCYCzgdPwe/ECYUwb5/WUL8cTzRKV+tFwxGjLEV/xpm0GupS5RwbhSQ==", "path": "serilog.aspnetcore/6.1.0", "hashPath": "serilog.aspnetcore.6.1.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AdZXURQ0dQCCjst3Jn3lwFtGicWjGE4wov9E5BPc4N5cruGmd2y9wprCYEjFteU84QMbxk35fpeTuHs6M4VGYw==", "path": "serilog.enrichers.environment/2.3.0", "hashPath": "serilog.enrichers.environment.2.3.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Formatting.Elasticsearch/10.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N9FsqRnl+rQOD2ldJMbEgPySwFDkUB8s6PCj5e92ryektgyZNFze3R2c1nwGOz6jhJ2aIzBIBNDM69CCzdHO0w==", "path": "serilog.formatting.elasticsearch/10.0.0", "hashPath": "serilog.formatting.elasticsearch.10.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FpUWtc0YUQvCfrKRI73KbmpWK3RvWTQr9gMDfTPEtmVI6f7KkY8Egj6r1BQA1/4oyTjxRbTn5yKX+2+zaWTwrg==", "path": "serilog.settings.configuration/7.0.1", "hashPath": "serilog.settings.configuration.7.0.1.nupkg.sha512"}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "path": "serilog.sinks.async/1.5.0", "hashPath": "serilog.sinks.async.1.5.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "path": "serilog.sinks.console/4.1.0", "hashPath": "serilog.sinks.console.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.FastConsole/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2PMJe5DQbUf//WWSCGRt4EOrJYb53ILuB3vUY6T3ZL3HlX3YjiDsMCirmpLUp/SwG+Cp1Kt6sSc/QOsRkdGb4Q==", "path": "serilog.sinks.fastconsole/2.2.0", "hashPath": "serilog.sinks.fastconsole.2.2.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.SpectreConsole/0.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-Lw3CBuJQ+HY4Pso3km7isrDXdmLpwZ5sgt1SXr5v6UEkO2hsSP4iHIXvLKbCgJyvUyxikVn0iFQMj4eGfl4/TA==", "path": "serilog.sinks.spectreconsole/0.3.3", "hashPath": "serilog.sinks.spectreconsole.0.3.3.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "SixLabors.Fonts/1.0.0-beta18": {"type": "package", "serviceable": true, "sha512": "sha512-evykNmy/kEE9EAEKgZm3MNUYXuMHFfmcLUNPw7Ho5q7OI96GFkkIxBm+QaKOTPBKw+L0AjKOs+ArVg8P40Ac9g==", "path": "sixlabors.fonts/1.0.0-beta18", "hashPath": "sixlabors.fonts.1.0.0-beta18.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-8yonNRWX3vUE9k29ta0Hbfa0AEc0hbDjSH/nZ3vOTJEXmY6hLnGsjDKoz96Z+AgOsrdkAu6PdL/Ebaf70aitzw==", "path": "sixlabors.imagesharp/2.1.3", "hashPath": "sixlabors.imagesharp.2.1.3.nupkg.sha512"}, "SkiaSharp/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "path": "skiasharp/2.88.9", "hashPath": "skiasharp.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-iQcOUE0tPZvBUxOdZaP3LIdAC21H8BEMhDvpCQ/mUUvbKGLd5rF7veJVSZBNu20SuCC0oZpEdGxB+mLVOK8uzw==", "path": "skiasharp.nativeassets.linux/2.88.6", "hashPath": "skiasharp.nativeassets.linux.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-/DoKtdyvRgCC5GR/SH+ps3ZiOjmf0BYpAyrhWQELFOO1hdcqddrDVJjDNCOJ41vV+NlS5b3kcDoZZ7jLhFjyXg==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.8", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "path": "skiasharp.nativeassets.macos/2.88.9", "hashPath": "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "path": "skiasharp.nativeassets.win32/2.88.9", "hashPath": "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512"}, "Snowflake.Core/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YB4SqYbTgG6UvwFHFZOWny/pE415KNjRbbiGwfPOUDWUCEjYLB9P11EiIHQ7y6LlkjejVAAIi8Wt3WM7Ys9qyw==", "path": "snowflake.core/2.0.0", "hashPath": "snowflake.core.2.0.0.nupkg.sha512"}, "Spectre.Console/0.45.0": {"type": "package", "serviceable": true, "sha512": "sha512-e//13o8/BCrWmwN26eJ4zCzD2iq7iUlqQd+nDI9nJUdnJ/rYAanYiNFZZ7YHwlv48IKuKtRYYP6/wPt1DG67ww==", "path": "spectre.console/0.45.0", "hashPath": "spectre.console.0.45.0.nupkg.sha512"}, "Spire.Officefor.NETStandard/9.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-qTd+Zr308FMM+568vuQxZpxHQ97jJzqGEEsxlXPQP2B+j+RMega3hSg5qiADyS6KMecA9AzckLsUhS0f2AZZsQ==", "path": "spire.officefor.netstandard/9.2.1", "hashPath": "spire.officefor.netstandard.9.2.1.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "path": "sqlitepclraw.core/2.1.6", "hashPath": "sqlitepclraw.core.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-2<PERSON>bJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-ubV7ls5zrgP5r+hAy622etjhTmrgKiK9wCX/5P3kHGfySHV1JLhsGxI4+Pc4JDCsIpveaspip+uzaKrK4/xGxQ==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q0NAjF9hvkxLbNedIrCqKd3uru0enzZ49GaQtenvsLDQ29aHwlSqg1mRkVYxZ/85UYJFgXh+XHqABSrMgun4aw==", "path": "sqlsugarcore.dm/8.6.0", "hashPath": "sqlsugarcore.dm.8.6.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.6.801": {"type": "package", "serviceable": true, "sha512": "sha512-FM8e18+lVENoG1qK1sK0TuIuCL1ZT/4dWF0wVvh3q03WU5Eu8l+7YP/qJvg9nRP034U1shgJAETpoHq2uXAHmQ==", "path": "sqlsugarcore.kdbndp/9.3.6.801", "hashPath": "sqlsugarcore.kdbndp.9.3.6.801.nupkg.sha512"}, "StackExchange.Redis/2.8.31": {"type": "package", "serviceable": true, "sha512": "sha512-RCHVQa9Zke8k0oBgJn1Yl6BuYy8i6kv+sdMObiH60nOwD6QvWAjxdDwOm+LO78E8WsGiPqgOuItkz98fPS6haQ==", "path": "stackexchange.redis/2.8.31", "hashPath": "stackexchange.redis.2.8.31.nupkg.sha512"}, "StackExchange.Redis.Extensions.AspNetCore/10.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kEOveCZy04/nWLQmC0EnLk2a+aBKQs+qUFO+6o7cdOaZMvcbBVExkytwaCS1J/ddr5buOO5Gn8k3SZbkfFixIA==", "path": "stackexchange.redis.extensions.aspnetcore/10.2.0", "hashPath": "stackexchange.redis.extensions.aspnetcore.10.2.0.nupkg.sha512"}, "StackExchange.Redis.Extensions.Core/10.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wpgpVdZIm7sw5rx19SGXbPWeVBjDFVjxDa3GWUMZRcrbmOMCNNA54+7xPQP70pUZmDYuEYU1fHyb/HU0X1+Mqg==", "path": "stackexchange.redis.extensions.core/10.2.0", "hashPath": "stackexchange.redis.extensions.core.10.2.0.nupkg.sha512"}, "StackExchange.Redis.Extensions.MsgPack/10.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOwEOyK2O1HNZWH+ZjD46CkOChywspRqHVuadOvaBL6As4urYn9iVmkbP68TdTOVAcf4GcjnvPBb34cJ/7MmPg==", "path": "stackexchange.redis.extensions.msgpack/10.2.0", "hashPath": "stackexchange.redis.extensions.msgpack.10.2.0.nupkg.sha512"}, "StackExchange.Redis.Extensions.Newtonsoft/10.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-BXguD4Ul/HtxP57Z0O1fQTaMD0uzg79hPCyZDOIf9CnhJkwcC9HUpTybQSFh0E64k0EIeyyikFSVV7uPbzHahQ==", "path": "stackexchange.redis.extensions.newtonsoft/10.2.0", "hashPath": "stackexchange.redis.extensions.newtonsoft.10.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-cnzQDn0Le+hInsw2SYwlOhOCPXpYi/szcvnyqZJ12v+QyrLBwAmWXBg6RIyHB18s/mLeywC+Rg2O9ndz0IUNYQ==", "path": "swashbuckle.aspnetcore/6.2.3", "hashPath": "swashbuckle.aspnetcore.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-AVvG44LqDjB765pVldSbRPpH6iTIo6xmyoZE9aP8FcREMMUj7WB1cSMF+bWtUCkeEVwVTr8iGDxLfrCS03uIuQ==", "path": "swashbuckle.aspnetcore.filters/7.0.5", "hashPath": "swashbuckle.aspnetcore.filters.7.0.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-iY3FK5URcNqdESPFDWL0YAe7X2sxZJj7YM5gTCIHvtu4d/h9NurhBLII96+obQghbJ7i45TVlPeYo4jb9WmVGg==", "path": "swashbuckle.aspnetcore.filters.abstractions/7.0.5", "hashPath": "swashbuckle.aspnetcore.filters.abstractions.7.0.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-qOF7j1sL0bWm8g/qqHVPCvkO3JlVvUIB8WfC98kSh6BT5y5DAnBNctfac7XR5EZf+eD7/WasvANncTqwZYfmWQ==", "path": "swashbuckle.aspnetcore.swagger/6.2.3", "hashPath": "swashbuckle.aspnetcore.swagger.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-+Xq7WdMCCfcXlnbLJVFNgY8ITdP2TRYIlpbt6IKzDw5FwFxdi9lBfNDtcT+/wkKwX70iBBFmXldnnd02/VO72A==", "path": "swashbuckle.aspnetcore.swaggergen/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-bCRI87uKJVb4G+KURWm8LQrL64St04dEFZcF6gIM67Zc0Sr/N47EO83ybLMYOvfNdO1DCv8xwPcrz9J/VEhQ5g==", "path": "swashbuckle.aspnetcore.swaggerui/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.2.3.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-vDDPWwHn3/DNZ+kPkdXHoada+tKPEC9bVqDOr4hK6HBSP7hGCUTA0Zw6WU5qpGaqa5M1/V+axHMIv+DNEbIf6g==", "path": "system.directoryservices.protocols/6.0.2", "hashPath": "system.directoryservices.protocols.6.0.2.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-C+Q5ORsFycRkRuvy/Xd0Pv5xVpmWSAvQYZAGs7VQogmkqlLhvfZXTgBIlHqC3cxkstSoLJAYx6xZB7foQ2y5eg==", "path": "system.identitymodel.tokens.jwt/6.10.0", "hashPath": "system.identitymodel.tokens.jwt.6.10.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "path": "system.io.hashing/7.0.0", "hashPath": "system.io.hashing.7.0.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Async/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TkDTElj3vvizUnhS4mj0oZW8kX6KOBcsUkj79w/q6IUI7nsW+bXmWZfixknClug/IA+8vTWcArXSjIxn9hIWxQ==", "path": "system.linq.async/5.1.0", "hashPath": "system.linq.async.5.1.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-o+6pt8HrLDamzudt4Zs9nEpjgoVFthzW73c2Izu64OVGJ38fpkebPgWuZ3wl4oaaUhlg8dlJCeiJoKNlKnvH7Q==", "path": "system.linq.dynamic.core/1.3.3", "hashPath": "system.linq.dynamic.core.1.3.3.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-dB4hD50X7FaCCPoMJ+TShvSVXEHWBD/GKEd494N4a3V+avJmNFmKK7bM40J1zsj+QWt66DG2YkwWlRf/OHx8zw==", "path": "system.private.servicemodel/4.10.0", "hashPath": "system.private.servicemodel.4.10.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qAo4jyXtC9i71iElngX7P2r+zLaiHzxKwf66sc3X91tL5Ks6fnQ1vxL04o7ZSm3sYfLExySL7GN8aTpNYpU1qw==", "path": "system.reflection.emit/4.6.0", "hashPath": "system.reflection.emit.4.6.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-j/V5HVvxvBQ7uubYD0PptQW2KGsi1Pc2kZ9yfwLixv3ADdjL/4M78KyC5e+ymW612DY8ZE4PFoZmWpoNmN2mqg==", "path": "system.reflection.emit.lightweight/4.6.0", "hashPath": "system.reflection.emit.lightweight.4.6.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "path": "system.security.cryptography.pkcs/6.0.4", "hashPath": "system.security.cryptography.pkcs.6.0.4.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-htdZ/71tGUl+Yj/URS2dzK7v4hRR/o1/pH2Vnse9vlqZnChJTz7dV3Wk6GsHDUOuYzcufyCMuM79W6aHmL9v8w==", "path": "system.servicemodel.duplex/4.8.1", "hashPath": "system.servicemodel.duplex.4.8.1.nupkg.sha512"}, "System.ServiceModel.Federation/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-MoI7+PiitoeT/6Ggmlaz+kDBHodCu/OoPLdgd3TaEBR6Y7ccY0rRgh+d4O06+vajtejioynO366Ziqa3c9O70g==", "path": "system.servicemodel.federation/4.8.1", "hashPath": "system.servicemodel.federation.4.8.1.nupkg.sha512"}, "System.ServiceModel.Http/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-/PbmNSEwTQ7Vizor3F/Zp8bzR6L9YZNGIwGr1Tyc//ZZuAYDhiwiMbNpX3EnPZM63qD2bJmR/FWH9S5Ffp8K6g==", "path": "system.servicemodel.http/4.10.0", "hashPath": "system.servicemodel.http.4.10.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-2h2Wq1wr/aBj8BwydO8h8AV5HvE7lEhgRyE6sDsftGTOr1rg5us9zxcJGT+WMlJ7pSTPXSTYCOEM/FfqHe0SEQ==", "path": "system.servicemodel.nettcp/4.8.1", "hashPath": "system.servicemodel.nettcp.4.8.1.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-BtrvvpgU2HolcC0tUf1g+n4Fk5kLhfbIBgRibcGe7TDHXcy6zTfkyXxR88rl2tO4KEPLkJXxWf/HW/LJmsI0Ew==", "path": "system.servicemodel.primitives/4.10.0", "hashPath": "system.servicemodel.primitives.4.10.0.nupkg.sha512"}, "System.ServiceModel.Security/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-vCBnopz067lS+7Tn+dvpWPH9Yn5CYvSMd5StRzqa7cFDX4O7jkChRSoFWeMaNNqJCwQjafphaQn9IViwqufHZQ==", "path": "system.servicemodel.security/4.8.1", "hashPath": "system.servicemodel.security.4.8.1.nupkg.sha512"}, "System.ServiceProcess.ServiceController/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qMBvG8ZFbkXoe0Z5/D7FAAadfPkH2v7vSuh2xsLf3U6jNoejpIdeV18A0htiASsLK1CCAc/p59kaLXlt2yB1gw==", "path": "system.serviceprocess.servicecontroller/6.0.0", "hashPath": "system.serviceprocess.servicecontroller.6.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-+MvhNtcvIbqmhANyKu91jQnvIRVSTiaOiFNfKWwXGHG48YAb4I/TyH8spsySiPYla7gKal5ZnF3teJqZAximyQ==", "path": "system.threading.tasks.extensions/4.5.3", "hashPath": "system.threading.tasks.extensions.4.5.3.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "Unchase.Swashbuckle.AspNetCore.Extensions/2.6.12": {"type": "package", "serviceable": true, "sha512": "sha512-na7B3cmNK8L8YuxVl/W9YeZn+jrDz9LlmZa1ZcosKtHQYFOWAnPta2njwa91UyeJzMGjdHoIVE74Q1TGxuUHug==", "path": "unchase.swashbuckle.aspnetcore.extensions/2.6.12", "hashPath": "unchase.swashbuckle.aspnetcore.extensions.2.6.12.nupkg.sha512"}, "XH.LAB.UTILS/6.25.301.13": {"type": "package", "serviceable": true, "sha512": "sha512-R2ssTspbs6+2aqGu60cExG8TgBNJDGHQofa6AVSbQ6zHyyLI2Y7Eqz1rXigzhlxdKbp/p653UMQxBv6rIMvxFQ==", "path": "xh.lab.utils/6.25.301.13", "hashPath": "xh.lab.utils.6.25.301.13.nupkg.sha512"}, "Xinghe.Utility/6.25.206": {"type": "package", "serviceable": true, "sha512": "sha512-cr7vLHdBO+CSgscmucSFNLA/g5njDkHT/SFCb9Z8j/hpVbtUzafR85vCCrZK0AEh8vfXTEurDZocTTJvoOYaQA==", "path": "xinghe.utility/6.25.206", "hashPath": "xinghe.utility.6.25.206.nupkg.sha512"}, "Yarp.ReverseProxy/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-VgRuCBxmh5ND4VuFhvIN3AAeoxFhYkS2hNINk6AVCrOVTlpk7OwdrTXi8NHACfqfhDL+/oYCZrF9RxN+yiYnEg==", "path": "yarp.reverseproxy/2.1.0", "hashPath": "yarp.reverseproxy.2.1.0.nupkg.sha512"}, "XH.H82.Base/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "XH.H82.IServices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "XH.H82.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}