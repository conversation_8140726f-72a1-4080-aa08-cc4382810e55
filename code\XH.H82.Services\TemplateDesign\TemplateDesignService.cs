﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Newtonsoft.Json;
using XH.H82.IServices.TemplateDesign;
using XH.H82.Models.SugarDbContext;
using XH.H82.Models.TemplateDesign;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.TemplateDesign;

public class TemplateDesignService : ITemplateDesignService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    public TemplateDesignService(ISqlSugarUow<SugarDbContext_Master> dbContext)
    {
        _dbContext = dbContext;
    }
    public ResultDto InsertPersonInfoSetting()
        { 
            ResultDto resultDto = new ResultDto();
            List<PropertyInfoModel> propertyInfos = EntityMetadataGenerator.GenerateMetadataJson<Equipment>();
            SYS6_MODULE_FUNC_DICT formDict = _dbContext.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == "H8205612")?.First();
            if (formDict != null)
            {
                PageSettingForm PageSettingForm = new PageSettingForm();
                PageSettingForm.form = new List<Form>();
                foreach (var item in propertyInfos)
                {
                    Form form = new Form();
                    form.formCname = item.Description;
                    form.formName = item.Description;
                    form.formCode = item.PropertyName;
                    form.ifShow = false;
                    form.titleShow = true;
                    form.titleColor = "#000000";
                    form.titleSize = 13;
                    form.titleStyle = "0";
                    form.titleAlign = "3";
                    form.contentLine = 1;
                    form.contentMaxLine = 1;
                    form.contentHeightClass = "1";
                    form.contentHeightRatio = "";
                    form.contentAlign = "1";
                    form.contentFontSize = 13;
                    form.contentStyle = "0";
                    form.titleAndContentType = "1";
                    form.contentEnlarge = false;
                    //form.ifRequired = dto.ifRequired;
                    form.replaceField = true;
                    form.onlyRead = "1";
                    form.dataType = item.DbType;
                    //form.dataClass = "1";
                    form.allowMaintainDropDownData = false;
                    form.editeState = false;
                    form.unitFlag = false;
                    PageSettingForm.form.Add(form);
                }
                formDict.FORM_COL_JSON= JsonConvert.SerializeObject(PageSettingForm);
                _dbContext.Db.Updateable(formDict).ExecuteCommand();
            }
            return resultDto;
        }
}