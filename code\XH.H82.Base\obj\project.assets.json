{"version": 3, "targets": {"net6.0": {"AspNetCoreRateLimit/4.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/net6.0/AspNetCoreRateLimit.dll": {}}, "runtime": {"lib/net6.0/AspNetCoreRateLimit.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Autofac/6.4.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/net6.0/Autofac.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Autofac.dll": {"related": ".xml"}}}, "Autofac.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Autofac": "6.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Autofac.Extras.DynamicProxy/6.0.1": {"type": "package", "dependencies": {"Autofac": "6.2.0", "Castle.Core": "4.4.0"}, "compile": {"lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll": {"related": ".xml"}}}, "AutoMapper/12.0.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0"}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"type": "package", "dependencies": {"AutoMapper": "12.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "Ben.Demystifier/0.4.1": {"type": "package", "dependencies": {"System.Reflection.Metadata": "5.0.0"}, "compile": {"lib/netstandard2.1/Ben.Demystifier.dll": {}}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {}}}, "BouncyCastle/1.8.9": {"type": "package", "compile": {"lib/BouncyCastle.Crypto.dll": {}}, "runtime": {"lib/BouncyCastle.Crypto.dll": {}}}, "Castle.Core/5.1.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "Dapper/2.0.123": {"type": "package", "compile": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}}, "EasyCaching.Core/1.7.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "compile": {"lib/net6.0/EasyCaching.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/EasyCaching.Core.dll": {"related": ".xml"}}}, "EasyCaching.Serialization.Protobuf/1.7.0": {"type": "package", "dependencies": {"EasyCaching.Core": "1.7.0", "protobuf-net": "3.1.0"}, "compile": {"lib/net6.0/EasyCaching.Serialization.Protobuf.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/EasyCaching.Serialization.Protobuf.dll": {"related": ".xml"}}}, "Elastic.Channels/0.7.0": {"type": "package", "dependencies": {"System.Threading.Channels": "4.7.1"}, "compile": {"lib/netstandard2.1/Elastic.Channels.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.Channels.dll": {"related": ".pdb;.xml"}}}, "Elastic.Clients.Elasticsearch/8.15.0": {"type": "package", "dependencies": {"Elastic.Transport": "0.4.22"}, "compile": {"lib/net6.0/Elastic.Clients.Elasticsearch.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Elastic.Clients.Elasticsearch.dll": {"related": ".xml"}}}, "Elastic.CommonSchema/8.11.1": {"type": "package", "dependencies": {"Ben.Demystifier": "0.4.1"}, "compile": {"lib/net6.0/Elastic.CommonSchema.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Elastic.CommonSchema.dll": {"related": ".pdb;.xml"}}}, "Elastic.CommonSchema.Serilog/8.11.1": {"type": "package", "dependencies": {"Elastic.CommonSchema": "8.11.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Serilog": "2.9.0"}, "compile": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"related": ".pdb;.xml"}}}, "Elastic.Ingest.Elasticsearch/0.7.0": {"type": "package", "dependencies": {"Elastic.Ingest.Transport": "0.7.0"}, "compile": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.dll": {"related": ".pdb;.xml"}}}, "Elastic.Ingest.Elasticsearch.CommonSchema/8.11.1": {"type": "package", "dependencies": {"Elastic.CommonSchema": "8.11.1", "Elastic.Ingest.Elasticsearch": "0.7.0"}, "compile": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.dll": {"related": ".pdb;.xml"}}}, "Elastic.Ingest.Transport/0.7.0": {"type": "package", "dependencies": {"Elastic.Channels": "0.7.0", "Elastic.Transport": "0.4.18", "System.Threading.Channels": "4.7.1"}, "compile": {"lib/netstandard2.1/Elastic.Ingest.Transport.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.Ingest.Transport.dll": {"related": ".pdb;.xml"}}}, "Elastic.Serilog.Sinks/8.11.1": {"type": "package", "dependencies": {"Elastic.CommonSchema.Serilog": "8.11.1", "Elastic.Ingest.Elasticsearch.CommonSchema": "8.11.1"}, "compile": {"lib/netstandard2.1/Elastic.Serilog.Sinks.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Elastic.Serilog.Sinks.dll": {"related": ".pdb;.xml"}}}, "Elastic.Transport/0.4.22": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0", "System.Text.Json": "8.0.4"}, "compile": {"lib/net6.0/Elastic.Transport.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Elastic.Transport.dll": {"related": ".pdb;.xml"}}}, "Enums.NET/4.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "EPPlus/6.2.6": {"type": "package", "dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.1", "System.Security.Cryptography.Pkcs": "6.0.3", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/net6.0/EPPlus.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/EPPlus.dll": {"related": ".xml"}}}, "EPPlus.Interfaces/6.1.1": {"type": "package", "compile": {"lib/net6.0/EPPlus.Interfaces.dll": {}}, "runtime": {"lib/net6.0/EPPlus.Interfaces.dll": {}}}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "dependencies": {"EPPlus.Interfaces": "6.1.1", "System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/EPPlus.System.Drawing.dll": {}}, "runtime": {"lib/net6.0/EPPlus.System.Drawing.dll": {}}}, "FileHelpers/3.5.2": {"type": "package", "compile": {"lib/netstandard2.0/FileHelpers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FileHelpers.dll": {"related": ".xml"}}}, "FireflySoft.RateLimit.AspNetCore/3.0.0": {"type": "package", "dependencies": {"FireflySoft.RateLimit.Core": "3.0.0"}, "compile": {"lib/net6.0/FireflySoft.RateLimit.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FireflySoft.RateLimit.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "FireflySoft.RateLimit.Core/3.0.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.1", "Nito.AsyncEx": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "StackExchange.Redis": "2.2.4", "System.Linq.Async": "5.1.0"}, "compile": {"lib/netstandard2.0/FireflySoft.RateLimit.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FireflySoft.RateLimit.Core.dll": {"related": ".xml"}}}, "FSharp.Core/6.0.5": {"type": "package", "compile": {"lib/netstandard2.1/FSharp.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FSharp.Core.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.1/cs/FSharp.Core.resources.dll": {"locale": "cs"}, "lib/netstandard2.1/de/FSharp.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.1/es/FSharp.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.1/fr/FSharp.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.1/it/FSharp.Core.resources.dll": {"locale": "it"}, "lib/netstandard2.1/ja/FSharp.Core.resources.dll": {"locale": "ja"}, "lib/netstandard2.1/ko/FSharp.Core.resources.dll": {"locale": "ko"}, "lib/netstandard2.1/pl/FSharp.Core.resources.dll": {"locale": "pl"}, "lib/netstandard2.1/pt-BR/FSharp.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.1/ru/FSharp.Core.resources.dll": {"locale": "ru"}, "lib/netstandard2.1/tr/FSharp.Core.resources.dll": {"locale": "tr"}, "lib/netstandard2.1/zh-Hans/FSharp.Core.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.1/zh-Hant/FSharp.Core.resources.dll": {"locale": "zh-Han<PERSON>"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "HashDepot/2.0.3": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.Runtime": "4.3.1"}, "compile": {"lib/netstandard2.0/HashDepot.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/HashDepot.dll": {"related": ".xml"}}}, "iTextSharp/5.5.13.3": {"type": "package", "dependencies": {"BouncyCastle": "1.8.9"}, "compile": {"lib/itextsharp.dll": {}}, "runtime": {"lib/itextsharp.dll": {}}}, "JWT/10.1.1": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.2", "System.Text.Json": "6.0.7"}, "compile": {"lib/net6.0/JWT.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/JWT.dll": {"related": ".pdb;.xml"}}}, "KYSharp.SM.Core/1.0.1": {"type": "package", "dependencies": {"Portable.BouncyCastle": "1.8.10"}, "compile": {"lib/netstandard2.0/KYSharp.SM.dll": {}}, "runtime": {"lib/netstandard2.0/KYSharp.SM.dll": {}}}, "Mapster/7.4.0": {"type": "package", "dependencies": {"Mapster.Core": "1.2.1"}, "compile": {"lib/net6.0/Mapster.dll": {}}, "runtime": {"lib/net6.0/Mapster.dll": {}}}, "Mapster.Core/1.2.1": {"type": "package", "compile": {"lib/net6.0/Mapster.Core.dll": {}}, "runtime": {"lib/net6.0/Mapster.Core.dll": {}}}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Mapster.DependencyInjection.dll": {}}, "runtime": {"lib/net6.0/Mapster.DependencyInjection.dll": {}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "compile": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"related": ".xml"}}}, "MessagePack/2.1.90": {"type": "package", "dependencies": {"MessagePack.Annotations": "2.1.90", "Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Memory": "4.5.3", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2", "System.Threading.Tasks.Extensions": "4.5.3"}, "compile": {"lib/netcoreapp2.1/MessagePack.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/MessagePack.dll": {"related": ".xml"}}}, "MessagePack.Annotations/2.1.90": {"type": "package", "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.15": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/6.0.35": {"type": "package", "dependencies": {"MessagePack": "2.1.90", "Microsoft.Extensions.Options": "6.0.0", "StackExchange.Redis": "2.2.4"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Bcl.TimeProvider/8.0.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/2.1.7": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0"}, "compile": {"ref/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Data.Sqlite/8.0.1": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}, "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.Data.Sqlite.Core/8.0.1": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/6.0.16": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.16", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.16": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.16": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.12": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "6.0.12", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.11": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "2.1.4", "Microsoft.EntityFrameworkCore.Relational": "6.0.11"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Hosting/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Logging.Console": "6.0.0", "Microsoft.Extensions.Logging.Debug": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "Microsoft.Extensions.Logging.EventSource": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Hosting": "6.0.1", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "System.ServiceProcess.ServiceController": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Console/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Text.Json": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "compile": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.10.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.10.0", "System.IdentityModel.Tokens.Jwt": "6.10.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.10.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.10.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"type": "package", "compile": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.3.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "MsgPack.Cli/1.0.1": {"type": "package", "dependencies": {"System.CodeDom": "4.4.0", "System.Numerics.Vectors": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.LightWeight": "4.3.0"}, "compile": {"lib/netstandard2.0/MsgPack.dll": {"related": ".XML"}}, "runtime": {"lib/netstandard2.0/MsgPack.dll": {"related": ".XML"}}}, "MySqlConnector/2.2.5": {"type": "package", "compile": {"lib/net6.0/MySqlConnector.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MySqlConnector.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Nito.AsyncEx/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "Nito.AsyncEx.Interop.WaitHandles": "5.1.0", "Nito.AsyncEx.Oop": "5.1.0", "Nito.AsyncEx.Tasks": "5.1.0", "Nito.Cancellation": "1.1.0"}}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Interop.WaitHandles/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Interop.WaitHandles.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Interop.WaitHandles.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Oop/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Coordination": "5.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Oop.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Oop.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Cancellation/1.1.0": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.0"}, "compile": {"lib/netstandard2.0/Nito.Cancellation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.Cancellation.dll": {"related": ".xml"}}}, "Nito.Collections.Deque/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.4.0"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "NodaTime/3.1.10": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.7.1"}, "compile": {"lib/net6.0/NodaTime.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/NodaTime.dll": {"related": ".pdb;.xml"}}}, "Npgsql/6.0.8": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "6.0.12", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.12", "Microsoft.EntityFrameworkCore.Relational": "6.0.12", "Npgsql": "6.0.8"}, "compile": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}}, "NPOI/2.6.0": {"type": "package", "dependencies": {"Enums.NET": "4.0.0", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.0", "Portable.BouncyCastle": "1.9.0", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0-beta18", "SixLabors.ImageSharp": "2.1.3", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/net6.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/net6.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}, "runtime": {"lib/net6.0/NPOI.OOXML.dll": {"related": ".deps.json;.pdb;.xml"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"related": ".pdb"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"related": ".dll.config;.pdb"}, "lib/net6.0/NPOI.dll": {"related": ".OOXML.deps.json;.OOXML.pdb;.OOXML.xml;.OpenXml4Net.pdb;.OpenXmlFormats.dll.config;.OpenXmlFormats.pdb;.pdb;.xml"}}}, "Npoi.Mapper/6.0.0": {"type": "package", "dependencies": {"NPOI": "2.6.0"}, "compile": {"lib/net6.0/Npoi.Mapper.dll": {}}, "runtime": {"lib/net6.0/Npoi.Mapper.dll": {}}}, "Oracle.EntityFrameworkCore/6.21.61": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "[6.0.3, 7.0.0)", "Oracle.ManagedDataAccess.Core": "[3.21.61, 3.22.0)"}, "compile": {"lib/net6.0/Oracle.EntityFrameworkCore.dll": {}}, "runtime": {"lib/net6.0/Oracle.EntityFrameworkCore.dll": {}}}, "Oracle.ManagedDataAccess.Core/3.21.140": {"type": "package", "dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.2", "System.Security.Cryptography.Pkcs": "6.0.4"}, "compile": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {}}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {}}}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "dependencies": {"System.Text.Encoding.CodePages": "4.7.1"}, "compile": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {}}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "dependencies": {"System.IO.Pipelines": "5.0.1"}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "Polly/7.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "[6.0.7, 7.0.0)", "Microsoft.Extensions.DependencyInjection": "6.0.0", "MySqlConnector": "2.1.2"}, "compile": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}}, "Portable.BouncyCastle/1.9.0": {"type": "package", "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}}, "protobuf-net/3.1.0": {"type": "package", "dependencies": {"protobuf-net.Core": "3.1.0"}, "compile": {"lib/net5.0/protobuf-net.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/protobuf-net.dll": {"related": ".xml"}}}, "protobuf-net.Core/3.1.22": {"type": "package", "compile": {"lib/net6.0/protobuf-net.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/protobuf-net.Core.dll": {"related": ".xml"}}}, "RestSharp/112.1.0": {"type": "package", "compile": {"lib/net6.0/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/RestSharp.dll": {"related": ".xml"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Scrutor/3.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.9", "Microsoft.Extensions.DependencyModel": "3.1.6"}, "compile": {"lib/netcoreapp3.1/Scrutor.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Scrutor.dll": {"related": ".pdb;.xml"}}}, "Serilog/3.1.1": {"type": "package", "compile": {"lib/net6.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/6.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Enrichers.Environment/2.3.0": {"type": "package", "dependencies": {"Serilog": "2.3.0"}, "compile": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {}}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {}}}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "compile": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "2.0.0", "Serilog": "2.9.0"}, "compile": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "dependencies": {"Serilog": "2.8.0"}, "compile": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Formatting.Elasticsearch/10.0.0": {"type": "package", "dependencies": {"Serilog": "2.12.0"}, "compile": {"lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/7.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Serilog": "2.12.0"}, "compile": {"lib/net6.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "dependencies": {"Serilog": "2.9.0"}, "compile": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.FastConsole/2.2.0": {"type": "package", "dependencies": {"Serilog": "2.11.0", "System.Threading.Channels": "6.0.0"}, "compile": {"lib/net6.0/Serilog.Sinks.FastConsole.dll": {}}, "runtime": {"lib/net6.0/Serilog.Sinks.FastConsole.dll": {}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "Serilog.Sinks.SpectreConsole/0.3.3": {"type": "package", "dependencies": {"FSharp.Core": "6.0.5", "Serilog": "2.10.0", "Spectre.Console": "0.45.0"}, "compile": {"lib/netstandard2.0/Serilog.Sinks.SpectreConsole.dll": {}}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.SpectreConsole.dll": {}}}, "SharpZipLib/1.3.3": {"type": "package", "compile": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0-beta18": {"type": "package", "compile": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "Snowflake.Core/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Snowflake.Core.dll": {}}, "runtime": {"lib/netstandard2.0/Snowflake.Core.dll": {}}}, "Spectre.Console/0.45.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net6.0/Spectre.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Spectre.Console.dll": {"related": ".xml"}}}, "Spire.Officefor.NETStandard/9.2.1": {"type": "package", "dependencies": {"SkiaSharp": "1.68.0", "System.Buffers": "4.5.0", "System.Memory": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Text.Encoding.CodePages": "4.5.0"}, "compile": {"lib/netstandard2.0/Spire.Barcode.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Doc.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Email.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Pdf.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Presentation.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.XLS.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Spire.Barcode.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Doc.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Email.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Pdf.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.Presentation.dll": {"related": ".xml"}, "lib/netstandard2.0/Spire.XLS.dll": {"related": ".xml"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "SqlSugarCore/*********": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.Data.Sqlite": "8.0.1", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.2", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "3.21.100", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.6.0", "SqlSugarCore.Kdbndp": "9.3.6.801", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "compile": {"lib/netstandard2.1/SqlSugar.dll": {}}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {}}}, "SqlSugarCore.Dm/8.6.0": {"type": "package", "dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/DM.DmProvider.dll": {}}, "runtime": {"lib/netstandard2.0/DM.DmProvider.dll": {}}}, "SqlSugarCore.Kdbndp/9.3.6.801": {"type": "package", "compile": {"lib/netstandard2.1/Kdbndp.dll": {}}, "runtime": {"lib/netstandard2.1/Kdbndp.dll": {}}}, "StackExchange.Redis/2.8.31": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "StackExchange.Redis.Extensions.AspNetCore/10.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "compile": {"lib/net6.0/StackExchange.Redis.Extensions.AspNetCore.dll": {}}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.AspNetCore.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "StackExchange.Redis.Extensions.Core/10.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.1", "StackExchange.Redis": "2.7.17"}, "compile": {"lib/net6.0/StackExchange.Redis.Extensions.Core.dll": {}}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.Core.dll": {}}}, "StackExchange.Redis.Extensions.MsgPack/10.2.0": {"type": "package", "dependencies": {"MsgPack.Cli": "1.0.1", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "compile": {"lib/net6.0/StackExchange.Redis.Extensions.MsgPack.dll": {}}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.MsgPack.dll": {}}}, "StackExchange.Redis.Extensions.Newtonsoft/10.2.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.3", "StackExchange.Redis.Extensions.Core": "10.2.0"}, "compile": {"lib/net6.0/StackExchange.Redis.Extensions.Newtonsoft.dll": {}}, "runtime": {"lib/net6.0/StackExchange.Redis.Extensions.Newtonsoft.dll": {}}}, "Swashbuckle.AspNetCore/6.2.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.2.3", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3", "Swashbuckle.AspNetCore.SwaggerUI": "6.2.3"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "2.1.0", "Microsoft.OpenApi": "1.3.1", "Scrutor": "3.3.0", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "Swashbuckle.AspNetCore.SwaggerGen": "5.0.0"}, "compile": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.dll": {}}, "runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.dll": {}}}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"type": "package", "compile": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll": {}}, "runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll": {}}}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.2.3"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"type": "package", "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Data.Common/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.2/System.Data.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.2/System.Data.Common.dll": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.Protocols/6.0.2": {"type": "package", "compile": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Hashing/7.0.0": {"type": "package", "compile": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Pipelines/5.0.1": {"type": "package", "compile": {"ref/netcoreapp2.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Linq.Async/5.1.0": {"type": "package", "compile": {"ref/netcoreapp3.1/System.Linq.Async.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/System.Linq.Async.dll": {"related": ".xml"}}}, "System.Linq.Dynamic.Core/1.3.3": {"type": "package", "compile": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Private.ServiceModel/4.10.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "compile": {"ref/netstandard2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.6.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.Lightweight/4.6.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/4.7.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "dependencies": {"System.Formats.Asn1": "6.0.0"}, "compile": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Duplex/4.8.1": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.8.1", "System.ServiceModel.Primitives": "4.8.1"}, "compile": {"ref/netstandard2.0/System.ServiceModel.Duplex.dll": {}}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"related": ".pdb"}}}, "System.ServiceModel.Http/4.10.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "compile": {"ref/net6.0/System.ServiceModel.Http.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}}, "System.ServiceModel.NetTcp/4.8.1": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.8.1", "System.ServiceModel.Primitives": "4.8.1"}, "compile": {"ref/netstandard2.0/System.ServiceModel.NetTcp.dll": {}}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}}, "System.ServiceModel.Primitives/4.10.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.10.0"}, "compile": {"ref/net6.0/System.ServiceModel.Primitives.dll": {}, "ref/net6.0/System.ServiceModel.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}, "lib/net6.0/System.ServiceModel.dll": {"related": ".Primitives.pdb"}}}, "System.ServiceModel.Security/4.8.1": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.8.1", "System.ServiceModel.Primitives": "4.8.1"}, "compile": {"ref/netstandard2.0/System.ServiceModel.Security.dll": {}}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"related": ".pdb"}}}, "System.ServiceProcess.ServiceController/6.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "dependencies": {"System.Runtime": "4.3.1"}, "compile": {"ref/netcoreapp1.1/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading.Channels/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "Unchase.Swashbuckle.AspNetCore.Extensions/2.6.12": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "6.2.2"}, "compile": {"lib/netstandard2.0/Unchase.Swashbuckle.AspNetCore.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Unchase.Swashbuckle.AspNetCore.Extensions.dll": {"related": ".xml"}}}, "XH.LAB.UTILS/6.25.301.13": {"type": "package", "dependencies": {"Autofac.Extras.DynamicProxy": "6.0.1", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Spire.Officefor.NETStandard": "9.2.1", "SqlSugarCore": "*********", "Xinghe.Utility": "6.25.206", "Yarp.ReverseProxy": "2.1.0", "iTextSharp": "5.5.13.3"}, "compile": {"lib/net6.0/XH.LAB.UTILS.dll": {}}, "runtime": {"lib/net6.0/XH.LAB.UTILS.dll": {}}}, "Xinghe.Utility/6.25.206": {"type": "package", "dependencies": {"AspNetCoreRateLimit": "4.0.2", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.0", "Autofac": "6.4.0", "Autofac.Extensions.DependencyInjection": "8.0.0", "Castle.Core": "5.1.0", "Dapper": "2.0.123", "Elastic.Clients.Elasticsearch": "8.15.0", "Elastic.Serilog.Sinks": "8.11.1", "FileHelpers": "3.5.2", "HashDepot": "2.0.3", "JWT": "10.1.1", "KYSharp.SM.Core": "1.0.1", "Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.15", "Microsoft.AspNetCore.SignalR.StackExchangeRedis": "6.0.35", "Microsoft.EntityFrameworkCore": "6.0.16", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.11", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Hosting.WindowsServices": "6.0.1", "NPOI": "2.6.0", "Newtonsoft.Json": "13.0.3", "NodaTime": "3.1.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.8", "Npoi.Mapper": "6.0.0", "Oracle.EntityFrameworkCore": "6.21.61", "Oracle.ManagedDataAccess.Core": "3.21.140", "Polly": "7.2.3", "Pomelo.EntityFrameworkCore.MySql": "6.0.2", "RestSharp": "112.1.0", "Serilog.AspNetCore": "6.1.0", "Serilog.Enrichers.Environment": "2.3.0", "Serilog.Formatting.Elasticsearch": "10.0.0", "Serilog.Settings.Configuration": "7.0.1", "Serilog.Sinks.Async": "1.5.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.SpectreConsole": "0.3.3", "Snowflake.Core": "2.0.0", "SqlSugarCore": "*********", "SqlSugarCore.Dm": "8.6.0", "StackExchange.Redis": "2.8.31", "StackExchange.Redis.Extensions.AspNetCore": "10.2.0", "StackExchange.Redis.Extensions.Core": "10.2.0", "StackExchange.Redis.Extensions.MsgPack": "10.2.0", "StackExchange.Redis.Extensions.Newtonsoft": "10.2.0", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Filters": "7.0.5", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "System.Data.SqlClient": "4.8.6", "System.Linq.Dynamic.Core": "1.3.3", "System.ServiceModel.Duplex": "4.8.1", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.NetTcp": "4.8.1", "System.ServiceModel.Security": "4.8.1", "Unchase.Swashbuckle.AspNetCore.Extensions": "2.6.12", "serilog": "3.1.1"}, "compile": {"lib/net6.0/Xinghe.Utility.dll": {}, "lib/net6/ClickHouse.Client.dll": {}}, "runtime": {"lib/net6.0/Xinghe.Utility.dll": {}, "lib/net6/ClickHouse.Client.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Yarp.ReverseProxy/2.1.0": {"type": "package", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.0", "System.Collections.Immutable": "8.0.0", "System.IO.Hashing": "7.0.0"}, "compile": {"lib/net6.0/Yarp.ReverseProxy.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Yarp.ReverseProxy.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "XH.H82.Models/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"EPPlus": "6.2.6", "XH.LAB.UTILS": "6.25.301.13", "Xinghe.Utility": "6.25.206", "protobuf-net.Core": "3.1.22"}, "compile": {"bin/placeholder/XH.H82.Models.dll": {}}, "runtime": {"bin/placeholder/XH.H82.Models.dll": {}}}}}, "libraries": {"AspNetCoreRateLimit/4.0.2": {"sha512": "FzXAJFgaRjKfnKAVwjEEC7OAGQM5v/I3sQw2tpzmR0yHTCGhUAxZzDuwZiXTk8XLrI6vovzkqKkfKmiDl3nYMg==", "type": "package", "path": "aspnetcoreratelimit/4.0.2", "files": [".nupkg.metadata", ".signature.p7s", "aspnetcoreratelimit.4.0.2.nupkg.sha512", "aspnetcoreratelimit.nuspec", "lib/net5.0/AspNetCoreRateLimit.dll", "lib/net6.0/AspNetCoreRateLimit.dll", "lib/netcoreapp3.1/AspNetCoreRateLimit.dll", "lib/netstandard2.0/AspNetCoreRateLimit.dll"]}, "Autofac/6.4.0": {"sha512": "tkFxl6wAPuwVhrlN8wuNADnd+k2tv4ReP7ZZSL0vjfcN0RcfC9v25ogxK6b03HC7D4NwWjSLf1G/zTG8Bw43wQ==", "type": "package", "path": "autofac/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.6.4.0.nupkg.sha512", "autofac.nuspec", "icon.png", "lib/net5.0/Autofac.dll", "lib/net5.0/Autofac.xml", "lib/net6.0/Autofac.dll", "lib/net6.0/Autofac.xml", "lib/netstandard2.0/Autofac.dll", "lib/netstandard2.0/Autofac.xml", "lib/netstandard2.1/Autofac.dll", "lib/netstandard2.1/Autofac.xml"]}, "Autofac.Extensions.DependencyInjection/8.0.0": {"sha512": "nGrXNpQX2FiZpIBydK9cxZnnoqP/cUd3k/53uRERYEqLtWzKtE15R6L+j5q5ax5Rv/+3wAIkOaPePkahfqrwjg==", "type": "package", "path": "autofac.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512", "autofac.extensions.dependencyinjection.nuspec", "icon.png", "lib/net5.0/Autofac.Extensions.DependencyInjection.dll", "lib/net5.0/Autofac.Extensions.DependencyInjection.xml", "lib/net6.0/Autofac.Extensions.DependencyInjection.dll", "lib/net6.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.xml"]}, "Autofac.Extras.DynamicProxy/6.0.1": {"sha512": "+ulCOXUjbJ5dljBPZf3gRxQNPRGqg/h1cNTZ4SpXK3qiamTfRsW3gXs2w/IJ+CIDmHrFGYXLqzNd5etvzwB8kA==", "type": "package", "path": "autofac.extras.dynamicproxy/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.extras.dynamicproxy.6.0.1.nupkg.sha512", "autofac.extras.dynamicproxy.nuspec", "icon.png", "lib/netstandard2.0/Autofac.Extras.DynamicProxy.dll", "lib/netstandard2.0/Autofac.Extras.DynamicProxy.xml", "lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll", "lib/netstandard2.1/Autofac.Extras.DynamicProxy.xml"]}, "AutoMapper/12.0.1": {"sha512": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "type": "package", "path": "automapper/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.12.0.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.dll", "lib/netstandard2.1/AutoMapper.xml"]}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"sha512": "XCJ4E3oKrbRl1qY9Mr+7uyC0xZj1+bqQjmQRWTiTKiVuuXTny+7YFWHi20tPjwkMukLbicN6yGlDy5PZ4wyi1w==", "type": "package", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.extensions.microsoft.dependencyinjection.12.0.0.nupkg.sha512", "automapper.extensions.microsoft.dependencyinjection.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll"]}, "Ben.Demystifier/0.4.1": {"sha512": "axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "type": "package", "path": "ben.demystifier/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "ben.demystifier.0.4.1.nupkg.sha512", "ben.demystifier.nuspec", "icon.png", "lib/net45/Ben.Demystifier.dll", "lib/netstandard2.0/Ben.Demystifier.dll", "lib/netstandard2.1/Ben.Demystifier.dll", "readme.md"]}, "BouncyCastle/1.8.9": {"sha512": "axnBgvdD5n+FnEG6efk/tfKuMFru7R/EoISH9zjh319yb3HD24TEHSAbNN/lTRT2ulOGRxDgOsCjkuk08iwWPg==", "type": "package", "path": "bouncycastle/1.8.9", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "bouncycastle.1.8.9.nupkg.sha512", "bouncycastle.nuspec", "lib/BouncyCastle.Crypto.dll"]}, "Castle.Core/5.1.0": {"sha512": "31UJpTHOiWq95CDOHazE3Ub/hE/PydNWsJMwnEVTqFFP4WhAugwpaVGxzOxKgNeSUUeqS2W6lxV+q7u1pAOfXg==", "type": "package", "path": "castle.core/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.0.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "Dapper/2.0.123": {"sha512": "RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "type": "package", "path": "dapper/2.0.123", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.0.123.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml"]}, "EasyCaching.Core/1.7.0": {"sha512": "RU4mL51t7a1rXSK12LX4WaxpHap8cD3HDqJQkI1Y+BHGwv29nA4nuh4Rw29YlFz+jME6AdgnkHlP5sjJTLWOsg==", "type": "package", "path": "easycaching.core/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "easycaching.core.1.7.0.nupkg.sha512", "easycaching.core.nuspec", "lib/net6.0/EasyCaching.Core.dll", "lib/net6.0/EasyCaching.Core.xml", "lib/netstandard2.0/EasyCaching.Core.dll", "lib/netstandard2.0/EasyCaching.Core.xml", "nuget-icon.png"]}, "EasyCaching.Serialization.Protobuf/1.7.0": {"sha512": "AnoLv6GXEBFjxVZrdPw0H0ro+n4MHUeUzAEwKX8/WSjWTtcMLkqBpVWr28RHk+Hy7ItpcSvusnJcLL6FB4MjBA==", "type": "package", "path": "easycaching.serialization.protobuf/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "easycaching.serialization.protobuf.1.7.0.nupkg.sha512", "easycaching.serialization.protobuf.nuspec", "lib/net6.0/EasyCaching.Serialization.Protobuf.dll", "lib/net6.0/EasyCaching.Serialization.Protobuf.xml", "lib/netstandard2.0/EasyCaching.Serialization.Protobuf.dll", "lib/netstandard2.0/EasyCaching.Serialization.Protobuf.xml", "nuget-icon.png"]}, "Elastic.Channels/0.7.0": {"sha512": "IcL3NteG4QU6FlqMbUw05UGtZcHG7RgTGqiFotV/4kIua79l0WTXpSWTaN0WkRs2C7nyjW2QdITIEJRDHFYgsA==", "type": "package", "path": "elastic.channels/0.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.channels.0.7.0.nupkg.sha512", "elastic.channels.nuspec", "lib/netstandard2.0/Elastic.Channels.dll", "lib/netstandard2.0/Elastic.Channels.pdb", "lib/netstandard2.0/Elastic.Channels.xml", "lib/netstandard2.1/Elastic.Channels.dll", "lib/netstandard2.1/Elastic.Channels.pdb", "lib/netstandard2.1/Elastic.Channels.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Clients.Elasticsearch/8.15.0": {"sha512": "cj3mYmQyXtxelaOhPrSfuwjGYAaN49/1BwN7x9oFVaQlerE00FoFH00ID9LstXSsq7gtrq70Vcakl92V7rjT5w==", "type": "package", "path": "elastic.clients.elasticsearch/8.15.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "elastic.clients.elasticsearch.8.15.0.nupkg.sha512", "elastic.clients.elasticsearch.nuspec", "lib/net462/Elastic.Clients.Elasticsearch.dll", "lib/net462/Elastic.Clients.Elasticsearch.xml", "lib/net6.0/Elastic.Clients.Elasticsearch.dll", "lib/net6.0/Elastic.Clients.Elasticsearch.xml", "lib/net8.0/Elastic.Clients.Elasticsearch.dll", "lib/net8.0/Elastic.Clients.Elasticsearch.xml", "lib/netstandard2.0/Elastic.Clients.Elasticsearch.dll", "lib/netstandard2.0/Elastic.Clients.Elasticsearch.xml", "lib/netstandard2.1/Elastic.Clients.Elasticsearch.dll", "lib/netstandard2.1/Elastic.Clients.Elasticsearch.xml", "nuget-icon.png"]}, "Elastic.CommonSchema/8.11.1": {"sha512": "Z4vl/VUbH7A3UcyL0xe2s1fdATt2Hro3NBhshnZ/yRNbDwEwuQnbj2IHcv9+L/FfT+jgZricXgD769NTqOedhA==", "type": "package", "path": "elastic.commonschema/8.11.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.commonschema.8.11.1.nupkg.sha512", "elastic.commonschema.nuspec", "lib/net461/Elastic.CommonSchema.dll", "lib/net461/Elastic.CommonSchema.pdb", "lib/net461/Elastic.CommonSchema.xml", "lib/net6.0/Elastic.CommonSchema.dll", "lib/net6.0/Elastic.CommonSchema.pdb", "lib/net6.0/Elastic.CommonSchema.xml", "lib/netstandard2.0/Elastic.CommonSchema.dll", "lib/netstandard2.0/Elastic.CommonSchema.pdb", "lib/netstandard2.0/Elastic.CommonSchema.xml", "lib/netstandard2.1/Elastic.CommonSchema.dll", "lib/netstandard2.1/Elastic.CommonSchema.pdb", "lib/netstandard2.1/Elastic.CommonSchema.xml", "license.txt", "nuget-icon.png"]}, "Elastic.CommonSchema.Serilog/8.11.1": {"sha512": "QPRqMOvlRx5rEU9KGtnhHmgcy7yOm/b4v8/GEzIwRcRW50B7AukCuFeNzewW5guuwPxGsSgveIQvHHo5dlsoLg==", "type": "package", "path": "elastic.commonschema.serilog/8.11.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.commonschema.serilog.8.11.1.nupkg.sha512", "elastic.commonschema.serilog.nuspec", "lib/net461/Elastic.CommonSchema.Serilog.dll", "lib/net461/Elastic.CommonSchema.Serilog.pdb", "lib/net461/Elastic.CommonSchema.Serilog.xml", "lib/netstandard2.0/Elastic.CommonSchema.Serilog.dll", "lib/netstandard2.0/Elastic.CommonSchema.Serilog.pdb", "lib/netstandard2.0/Elastic.CommonSchema.Serilog.xml", "lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll", "lib/netstandard2.1/Elastic.CommonSchema.Serilog.pdb", "lib/netstandard2.1/Elastic.CommonSchema.Serilog.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Ingest.Elasticsearch/0.7.0": {"sha512": "7MmRkwVYphGhXAVXyrQxhnNpiyrkwVIzNuOJy/MXjPZPc0Tc6bwXLThqyCxR1JFE/hugHeoMFEAdb/TABHaJ6g==", "type": "package", "path": "elastic.ingest.elasticsearch/0.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.ingest.elasticsearch.0.7.0.nupkg.sha512", "elastic.ingest.elasticsearch.nuspec", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.dll", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.pdb", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.xml", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.dll", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.pdb", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Ingest.Elasticsearch.CommonSchema/8.11.1": {"sha512": "qkeBbOt7ZtcK7PiOhUH5l6FOSHnqzLuDCfmxU0xRgN8X5WC95IaGK7YD5FEwFGPiOA7206GBVT0e/9TFXdrZGw==", "type": "package", "path": "elastic.ingest.elasticsearch.commonschema/8.11.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.ingest.elasticsearch.commonschema.8.11.1.nupkg.sha512", "elastic.ingest.elasticsearch.commonschema.nuspec", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.CommonSchema.dll", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.CommonSchema.pdb", "lib/netstandard2.0/Elastic.Ingest.Elasticsearch.CommonSchema.xml", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.dll", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.pdb", "lib/netstandard2.1/Elastic.Ingest.Elasticsearch.CommonSchema.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Ingest.Transport/0.7.0": {"sha512": "ury3crl3KPMe8TCluusrq0+hzloYp0/kslhEdtXdnQ0qhHmI1CCJCYfDKK/tE7VSjnuEA9tFkdoUp2M+PqrmFg==", "type": "package", "path": "elastic.ingest.transport/0.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.ingest.transport.0.7.0.nupkg.sha512", "elastic.ingest.transport.nuspec", "lib/netstandard2.0/Elastic.Ingest.Transport.dll", "lib/netstandard2.0/Elastic.Ingest.Transport.pdb", "lib/netstandard2.0/Elastic.Ingest.Transport.xml", "lib/netstandard2.1/Elastic.Ingest.Transport.dll", "lib/netstandard2.1/Elastic.Ingest.Transport.pdb", "lib/netstandard2.1/Elastic.Ingest.Transport.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Serilog.Sinks/8.11.1": {"sha512": "cbKyJr0e8Ix/MiEkjVMbCxGUT4BVLd5AsBXrHkhvyQnrzWOfS19m12YazFXZBVmsDEnjiYM16Svd/62JiUJSSA==", "type": "package", "path": "elastic.serilog.sinks/8.11.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "elastic.serilog.sinks.8.11.1.nupkg.sha512", "elastic.serilog.sinks.nuspec", "lib/netstandard2.0/Elastic.Serilog.Sinks.dll", "lib/netstandard2.0/Elastic.Serilog.Sinks.pdb", "lib/netstandard2.0/Elastic.Serilog.Sinks.xml", "lib/netstandard2.1/Elastic.Serilog.Sinks.dll", "lib/netstandard2.1/Elastic.Serilog.Sinks.pdb", "lib/netstandard2.1/Elastic.Serilog.Sinks.xml", "license.txt", "nuget-icon.png"]}, "Elastic.Transport/0.4.22": {"sha512": "9J5GPHJcT8sewn2zVfWTrsCQvfQYgUiY/jx+IRjWUk7XNPd837qfEL42I5baH69cyW/e4RoDy8v76yxqz95tDw==", "type": "package", "path": "elastic.transport/0.4.22", "files": [".nupkg.metadata", ".signature.p7s", "elastic.transport.0.4.22.nupkg.sha512", "elastic.transport.nuspec", "lib/net462/Elastic.Transport.dll", "lib/net462/Elastic.Transport.pdb", "lib/net462/Elastic.Transport.xml", "lib/net6.0/Elastic.Transport.dll", "lib/net6.0/Elastic.Transport.pdb", "lib/net6.0/Elastic.Transport.xml", "lib/net8.0/Elastic.Transport.dll", "lib/net8.0/Elastic.Transport.pdb", "lib/net8.0/Elastic.Transport.xml", "lib/netstandard2.0/Elastic.Transport.dll", "lib/netstandard2.0/Elastic.Transport.pdb", "lib/netstandard2.0/Elastic.Transport.xml", "lib/netstandard2.1/Elastic.Transport.dll", "lib/netstandard2.1/Elastic.Transport.pdb", "lib/netstandard2.1/Elastic.Transport.xml", "license.txt", "nuget-icon.png"]}, "Enums.NET/4.0.0": {"sha512": "d47SgeuGxKpalKhYoHqFkDPmO9SoE3amSwVNDoUdy4d675/tX7bLyZFHdjfo3Tobth9Y80VnjfasQ/PD4LqUuA==", "type": "package", "path": "enums.net/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "enums.net.4.0.0.nupkg.sha512", "enums.net.nuspec", "lib/net45/Enums.NET.dll", "lib/net45/Enums.NET.pdb", "lib/net45/Enums.NET.xml", "lib/netcoreapp3.0/Enums.NET.dll", "lib/netcoreapp3.0/Enums.NET.pdb", "lib/netcoreapp3.0/Enums.NET.xml", "lib/netstandard1.0/Enums.NET.dll", "lib/netstandard1.0/Enums.NET.pdb", "lib/netstandard1.0/Enums.NET.xml", "lib/netstandard1.1/Enums.NET.dll", "lib/netstandard1.1/Enums.NET.pdb", "lib/netstandard1.1/Enums.NET.xml", "lib/netstandard1.3/Enums.NET.dll", "lib/netstandard1.3/Enums.NET.pdb", "lib/netstandard1.3/Enums.NET.xml", "lib/netstandard2.0/Enums.NET.dll", "lib/netstandard2.0/Enums.NET.pdb", "lib/netstandard2.0/Enums.NET.xml", "lib/netstandard2.1/Enums.NET.dll", "lib/netstandard2.1/Enums.NET.pdb", "lib/netstandard2.1/Enums.NET.xml"]}, "EPPlus/6.2.6": {"sha512": "eAPQbEHPCsEh8pAICPP4YYhhBuUHq2HH6oLqHzfgUP+GynB7MFHL+Jjx1X7rZPD+iA3c12ilz/WygRylc+xriw==", "type": "package", "path": "epplus/6.2.6", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.6.2.6.nupkg.sha512", "epplus.nuspec", "lib/net35/EPPlus.dll", "lib/net35/EPPlus.xml", "lib/net462/EPPlus.dll", "lib/net462/EPPlus.xml", "lib/net6.0/EPPlus.dll", "lib/net6.0/EPPlus.xml", "lib/net7.0/EPPlus.dll", "lib/net7.0/EPPlus.xml", "lib/netstandard2.0/EPPlus.dll", "lib/netstandard2.0/EPPlus.xml", "lib/netstandard2.1/EPPlus.dll", "lib/netstandard2.1/EPPlus.xml", "license.md", "readme.md", "readme.txt"]}, "EPPlus.Interfaces/6.1.1": {"sha512": "y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "type": "package", "path": "epplus.interfaces/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.interfaces.6.1.1.nupkg.sha512", "epplus.interfaces.nuspec", "lib/net35/EPPlus.Interfaces.dll", "lib/net462/EPPlus.Interfaces.dll", "lib/net6.0/EPPlus.Interfaces.dll", "lib/net7.0/EPPlus.Interfaces.dll", "lib/netstandard2.0/EPPlus.Interfaces.dll", "lib/netstandard2.1/EPPlus.Interfaces.dll", "license.md", "readme.md"]}, "EPPlus.System.Drawing/6.1.1": {"sha512": "lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "type": "package", "path": "epplus.system.drawing/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.system.drawing.6.1.1.nupkg.sha512", "epplus.system.drawing.nuspec", "lib/net35/EPPlus.System.Drawing.dll", "lib/net462/EPPlus.System.Drawing.dll", "lib/net6.0/EPPlus.System.Drawing.dll", "lib/net7.0/EPPlus.System.Drawing.dll", "lib/netstandard2.0/EPPlus.System.Drawing.dll", "lib/netstandard2.1/EPPlus.System.Drawing.dll", "license.md", "readme.md"]}, "FileHelpers/3.5.2": {"sha512": "Z0jwjF4AmDBzRWfHFMdrcuBgmoQ2ChLaTmhRjnmReoUPUkT/+5StzWCB0N0RpslYeXiSyvcntcZNLtY58todFw==", "type": "package", "path": "filehelpers/3.5.2", "files": [".nupkg.metadata", ".signature.p7s", "filehelpers.3.5.2.nupkg.sha512", "filehelpers.nuspec", "lib/net40/FileHelpers.dll", "lib/net40/FileHelpers.xml", "lib/net45/FileHelpers.dll", "lib/net45/FileHelpers.xml", "lib/netstandard2.0/FileHelpers.dll", "lib/netstandard2.0/FileHelpers.xml"]}, "FireflySoft.RateLimit.AspNetCore/3.0.0": {"sha512": "n1yEOIE7wlHJiDr0pL/I37I+35B1TO76xyyY+Wl0pvU4K7RwhRgH99f53bkxYeXwHwInxTb6DGPJsKDWDyK09w==", "type": "package", "path": "fireflysoft.ratelimit.aspnetcore/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fireflysoft.ratelimit.aspnetcore.3.0.0.nupkg.sha512", "fireflysoft.ratelimit.aspnetcore.nuspec", "lib/net5.0/FireflySoft.RateLimit.AspNetCore.dll", "lib/net5.0/FireflySoft.RateLimit.AspNetCore.xml", "lib/net6.0/FireflySoft.RateLimit.AspNetCore.dll", "lib/net6.0/FireflySoft.RateLimit.AspNetCore.xml", "lib/netcoreapp3.1/FireflySoft.RateLimit.AspNetCore.dll", "lib/netcoreapp3.1/FireflySoft.RateLimit.AspNetCore.xml"]}, "FireflySoft.RateLimit.Core/3.0.0": {"sha512": "VO2wzTkgIZbTfUOEr+8ohQ1aB8gyXTK/KCrA9E2LKfa4r7iAfyuZOF3JNmuOtbHUODk5PfwCEOcOBodJuNzOKg==", "type": "package", "path": "fireflysoft.ratelimit.core/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fireflysoft.ratelimit.core.3.0.0.nupkg.sha512", "fireflysoft.ratelimit.core.nuspec", "lib/netstandard2.0/FireflySoft.RateLimit.Core.dll", "lib/netstandard2.0/FireflySoft.RateLimit.Core.xml"]}, "FSharp.Core/6.0.5": {"sha512": "FwdQVtpj34xt8vKyFUUeNIS+obWlEnSrSW7y1ivRVts/ZsrUsKyOd0bZehgFhWdnB/NBsa9DCWvNFMTO0XDFcg==", "type": "package", "path": "fsharp.core/6.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "contentFiles/any/netstandard2.0/FSharp.Core.xml", "contentFiles/any/netstandard2.1/FSharp.Core.xml", "fsharp.core.6.0.5.nupkg.sha512", "fsharp.core.nuspec", "lib/netstandard2.0/FSharp.Core.dll", "lib/netstandard2.0/FSharp.Core.xml", "lib/netstandard2.0/cs/FSharp.Core.resources.dll", "lib/netstandard2.0/de/FSharp.Core.resources.dll", "lib/netstandard2.0/es/FSharp.Core.resources.dll", "lib/netstandard2.0/fr/FSharp.Core.resources.dll", "lib/netstandard2.0/it/FSharp.Core.resources.dll", "lib/netstandard2.0/ja/FSharp.Core.resources.dll", "lib/netstandard2.0/ko/FSharp.Core.resources.dll", "lib/netstandard2.0/pl/FSharp.Core.resources.dll", "lib/netstandard2.0/pt-BR/FSharp.Core.resources.dll", "lib/netstandard2.0/ru/FSharp.Core.resources.dll", "lib/netstandard2.0/tr/FSharp.Core.resources.dll", "lib/netstandard2.0/zh-<PERSON>/FSharp.Core.resources.dll", "lib/netstandard2.0/zh-Hant/FSharp.Core.resources.dll", "lib/netstandard2.1/FSharp.Core.dll", "lib/netstandard2.1/FSharp.Core.xml", "lib/netstandard2.1/cs/FSharp.Core.resources.dll", "lib/netstandard2.1/de/FSharp.Core.resources.dll", "lib/netstandard2.1/es/FSharp.Core.resources.dll", "lib/netstandard2.1/fr/FSharp.Core.resources.dll", "lib/netstandard2.1/it/FSharp.Core.resources.dll", "lib/netstandard2.1/ja/FSharp.Core.resources.dll", "lib/netstandard2.1/ko/FSharp.Core.resources.dll", "lib/netstandard2.1/pl/FSharp.Core.resources.dll", "lib/netstandard2.1/pt-BR/FSharp.Core.resources.dll", "lib/netstandard2.1/ru/FSharp.Core.resources.dll", "lib/netstandard2.1/tr/FSharp.Core.resources.dll", "lib/netstandard2.1/zh-<PERSON>/FSharp.Core.resources.dll", "lib/netstandard2.1/zh-Hant/FSharp.Core.resources.dll"]}, "HashDepot/2.0.3": {"sha512": "K033sUok3i8MhGM/10tX6Bq/7MTWZJxS3eAU1z+zFv6k2+1FV9+gEAuvZYHYYJeB4v8rbkaL9fHzLoHBZOTXDA==", "type": "package", "path": "hashdepot/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "hashdepot.2.0.3.nupkg.sha512", "hashdepot.nuspec", "lib/netstandard2.0/HashDepot.dll", "lib/netstandard2.0/HashDepot.xml"]}, "iTextSharp/5.5.13.3": {"sha512": "vtnMhTEJdZFCkLqaQLjD8aqTBIVKHPrSFuSXnxbLEJlvE/j/l88fvG9wtsejXTmhtErMo0lA9T2LdfdfbwplYw==", "type": "package", "path": "itextsharp/5.5.13.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "gnu-agpl-v3.0.md", "itextsharp.5.5.13.3.nupkg.sha512", "itextsharp.nuspec", "lib/iTextSharp.xml", "lib/itextsharp.dll", "notice.txt"]}, "JWT/10.1.1": {"sha512": "OJ7Vr6Rf539Ty8iw1FDcfM2Adp7BVT0tOEld5NZnxcVguzczFfv2dsdUKGWf9FVc4VCcVRXv0KZ7Q3/zs+b0Iw==", "type": "package", "path": "jwt/10.1.1", "files": [".nupkg.metadata", ".signature.p7s", "jwt.10.1.1.nupkg.sha512", "jwt.nuspec", "lib/net35/JWT.dll", "lib/net35/JWT.pdb", "lib/net35/JWT.xml", "lib/net40/JWT.dll", "lib/net40/JWT.pdb", "lib/net40/JWT.xml", "lib/net462/JWT.dll", "lib/net462/JWT.pdb", "lib/net462/JWT.xml", "lib/net6.0/JWT.dll", "lib/net6.0/JWT.pdb", "lib/net6.0/JWT.xml", "lib/netstandard1.3/JWT.dll", "lib/netstandard1.3/JWT.pdb", "lib/netstandard1.3/JWT.xml", "lib/netstandard2.0/JWT.dll", "lib/netstandard2.0/JWT.pdb", "lib/netstandard2.0/JWT.xml"]}, "KYSharp.SM.Core/1.0.1": {"sha512": "Ns0yhMLNhYVR6PJKiZ6LGi2MB4KnBl/L4lntvpiGZr5TOKEc9KOWoVED4XKP9By1G9TE9EOkZcLbcpa0xdBLMA==", "type": "package", "path": "kysharp.sm.core/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "kysharp.sm.core.1.0.1.nupkg.sha512", "kysharp.sm.core.nuspec", "lib/netstandard2.0/KYSharp.SM.dll"]}, "Mapster/7.4.0": {"sha512": "RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "type": "package", "path": "mapster/7.4.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.dll", "lib/net7.0/Mapster.dll", "mapster.7.4.0.nupkg.sha512", "mapster.nuspec"]}, "Mapster.Core/1.2.1": {"sha512": "11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "type": "package", "path": "mapster.core/1.2.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.Core.dll", "lib/net7.0/Mapster.Core.dll", "mapster.core.1.2.1.nupkg.sha512", "mapster.core.nuspec"]}, "Mapster.DependencyInjection/1.0.1": {"sha512": "LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "type": "package", "path": "mapster.dependencyinjection/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.DependencyInjection.dll", "lib/net7.0/Mapster.DependencyInjection.dll", "mapster.dependencyinjection.1.0.1.nupkg.sha512", "mapster.dependencyinjection.nuspec"]}, "MathNet.Numerics.Signed/4.15.0": {"sha512": "LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "type": "package", "path": "mathnet.numerics.signed/4.15.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net40/MathNet.Numerics.dll", "lib/net40/MathNet.Numerics.xml", "lib/net461/MathNet.Numerics.dll", "lib/net461/MathNet.Numerics.xml", "lib/netstandard1.3/MathNet.Numerics.dll", "lib/netstandard1.3/MathNet.Numerics.xml", "lib/netstandard2.0/MathNet.Numerics.dll", "lib/netstandard2.0/MathNet.Numerics.xml", "mathnet.numerics.signed.4.15.0.nupkg.sha512", "mathnet.numerics.signed.nuspec"]}, "MessagePack/2.1.90": {"sha512": "frdAkIQyILYjAhH8Q107N77eccLjnkYr8/L+3vHeCezI0rvfCB2xiZjin936QFtskLBmgW1D9y6bKs45zddVLQ==", "type": "package", "path": "messagepack/2.1.90", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/netcoreapp2.1/MessagePack.dll", "lib/netcoreapp2.1/MessagePack.xml", "lib/netstandard2.0/MessagePack.dll", "lib/netstandard2.0/MessagePack.xml", "messagepack.2.1.90.nupkg.sha512", "messagepack.nuspec"]}, "MessagePack.Annotations/2.1.90": {"sha512": "YnS4qdOMFJuC8TDvXK8ECjWn+hI/B6apwMSgQsiynVXxsvFiiSyun7OXgVT7KxkR8qLinFuASzoG/5zv3kemwQ==", "type": "package", "path": "messagepack.annotations/2.1.90", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/netstandard2.0/MessagePack.Annotations.dll", "lib/netstandard2.0/MessagePack.Annotations.xml", "messagepack.annotations.2.1.90.nupkg.sha512", "messagepack.annotations.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.15": {"sha512": "FvMT6e6EzBVeKzEh5vT25LrWtc3HNcnlanbFUli+PIoL8WKuCUOUSpwN7XFJJBLUW3S+QT/xG+Qa6jo+FlM1zg==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/6.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.6.0.15.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"sha512": "vbFDyKsSYBnxl3+RABtN79b0vsTcG66fDY8vD6Nqvu9uLtSej70Q5NcbGlnN6bJpZci5orSdgFTHMhBywivDPg==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"sha512": "M8Gk5qrUu5nFV7yE3SZgATt/5B1a5Qs8ZnXXeO/Pqu68CEiBHJWc10sdGdO5guc3zOFdm7H966mVnpZtEX4vSA==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"sha512": "UmkUePxRjsQW0j5euFFscBwjvTu25b8+qIK/2fI3GvcqQ+mkwgbWNAT8b/Gkoei1m2bTWC07lSdutuRDPPLcJA==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/6.0.35": {"sha512": "NC4bU6lQNQ8G9fkY+ArQpLNH6E0zki6lgXLxbBXaU+TPhTM0g+wbHCx8PsDxw0J097XpRO0HBnov9xSFFC0tSw==", "type": "package", "path": "microsoft.aspnetcore.signalr.stackexchangeredis/6.0.35", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll", "lib/net6.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.xml", "microsoft.aspnetcore.signalr.stackexchangeredis.6.0.35.nupkg.sha512", "microsoft.aspnetcore.signalr.stackexchangeredis.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.TimeProvider/8.0.0": {"sha512": "f5Kr5JepAbiGo7uDmhgvMqhntwxqXNn6/IpTBSSI4cuHhgnJGrLxFRhMjVpRkLPp6zJXO0/G0l3j9p9zSJxa+w==", "type": "package", "path": "microsoft.bcl.timeprovider/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.TimeProvider.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.TimeProvider.targets", "lib/net462/Microsoft.Bcl.TimeProvider.dll", "lib/net462/Microsoft.Bcl.TimeProvider.xml", "lib/net8.0/Microsoft.Bcl.TimeProvider.dll", "lib/net8.0/Microsoft.Bcl.TimeProvider.xml", "lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "lib/netstandard2.0/Microsoft.Bcl.TimeProvider.xml", "microsoft.bcl.timeprovider.8.0.0.nupkg.sha512", "microsoft.bcl.timeprovider.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/2.1.7": {"sha512": "dSdlcXPszeOjjDX9a0buMFgYqKrI5bTxdSgX3JyCa+OL80NUstJSxOJr0j9oOn8mpP5PgWeRC2bVf/Zf2Cjv+g==", "type": "package", "path": "microsoft.data.sqlclient/2.1.7", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.Data.SqlClient.dll", "lib/net46/Microsoft.Data.SqlClient.pdb", "lib/net46/Microsoft.Data.SqlClient.xml", "lib/net46/de/Microsoft.Data.SqlClient.resources.dll", "lib/net46/es/Microsoft.Data.SqlClient.resources.dll", "lib/net46/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net46/it/Microsoft.Data.SqlClient.resources.dll", "lib/net46/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net46/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net46/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net46/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net46/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net46/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.xml", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.2.1.7.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net46/Microsoft.Data.SqlClient.dll", "ref/net46/Microsoft.Data.SqlClient.pdb", "ref/net46/Microsoft.Data.SqlClient.xml", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.xml", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net46/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net46/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"sha512": "JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.Data.Sqlite/8.0.1": {"sha512": "+7uDWNYZmLrVq9eABAKwy1phGbpoFVohKCUoh/nGg9WiBwi856EkAJYFiQhTJWoXxzpInkLFj/6KACoSB7ODYg==", "type": "package", "path": "microsoft.data.sqlite/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/_._", "microsoft.data.sqlite.8.0.1.nupkg.sha512", "microsoft.data.sqlite.nuspec"]}, "Microsoft.Data.Sqlite.Core/8.0.1": {"sha512": "s8C8xbwMb79EqzTaIhwiBrYtbv6ATnUW19pJed4fKVgN5K4VPQ7JUGqBLztknvD6EJIMKrfRnINGTjnZghrDGw==", "type": "package", "path": "microsoft.data.sqlite.core/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.8.0.1.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/6.0.16": {"sha512": "Phfd0D0ew5VHGtM/j7o5HYapbt4R7d9Tkm4z+L4nmkhcEzWZjeuBCUBTOgJI7U4gI916QHliDCcp53FF7NBHIA==", "type": "package", "path": "microsoft.entityframeworkcore/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props", "lib/net6.0/Microsoft.EntityFrameworkCore.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.6.0.16.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.16": {"sha512": "ubRazZ/5BbOI4B11HcNAr0v4qE9MDcAAZtEFPqkGrnz4ClciA8qsM1Rd8IB25EoxfLnOv/Lnz7aW+/3G+D1MCg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.6.0.16.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.16": {"sha512": "gN2yqqz2R6jj80SUTc036cPPM6xv3e/T1qpQI8ZfWJG3hXjcISI93uZtnyLVpn1712SklQR1WMIZ/bGYUurjcA==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.6.0.16.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/6.0.12": {"sha512": "HBtRGHtF0Vf+BIQTkRGiopmE5rLYhj59xPpd17S1tLgYpiHDVbepCuHwh5H63fzjO99Z4tW5wmmEGF7KnD91WQ==", "type": "package", "path": "microsoft.entityframeworkcore.relational/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.6.0.12.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.11": {"sha512": "ua2+N4y/t5LZ78sO4ZBeP4d7mp/NFFWN2+wktMXCCBl3tR1s06r0I0W6lQz36Z4F1J45Lwb4O8djocqnEEYC2Q==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.6.0.11.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"sha512": "LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "type": "package", "path": "microsoft.extensions.apidescription.server/3.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"sha512": "bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "type": "package", "path": "microsoft.extensions.caching.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net461/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"sha512": "B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "type": "package", "path": "microsoft.extensions.caching.memory/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Memory.dll", "lib/net461/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/6.0.0": {"sha512": "tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "type": "package", "path": "microsoft.extensions.configuration/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"sha512": "f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"sha512": "tgU4u7bZsoS9MKVRiotVMAwHtbREHr5/5zSEV+JPhg46+ox47Au84E3D2IacAaB0bk5ePNaNieTlPrfjbbRJkg==", "type": "package", "path": "microsoft.extensions.configuration.binder/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Binder.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"sha512": "3nL1qCkZ1Oxx14ZTzgo4MmlO7tso7F+TtMZAY2jUAtTLyAcDp+EDjk3RqafoKiNaePyPvvlleEcBxh3b2Hzl1g==", "type": "package", "path": "microsoft.extensions.configuration.commandline/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"sha512": "pnyXV1LFOsYjGveuC07xp0YHIyGq7jRq5Ncb5zrrIieMLWVwgMyYxcOH0jTnBedDT4Gh1QinSqsjqzcieHk1og==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"sha512": "V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"sha512": "GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "type": "package", "path": "microsoft.extensions.configuration.json/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"sha512": "Fy8yr4V6obi7ZxvKYI1i85jqtwMq8tqyxQVZpRSkgeA8enqy/KvBIMdcuNdznlxQMZa72mvbHqb7vbg4Pyx95w==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"sha512": "vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"sha512": "xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/7.0.0": {"sha512": "oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "type": "package", "path": "microsoft.extensions.dependencymodel/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"sha512": "0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"sha512": "QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"sha512": "ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/6.0.1": {"sha512": "hbmizc9KPWOacLU8Z8YMaBG6KWdZFppczYV/KwnPGU/8xebWxQxdDeJmLOgg968prb7g2oQgnp6JVLX6lgby8g==", "type": "package", "path": "microsoft.extensions.hosting/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Hosting.dll", "lib/net461/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.6.0.1.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"sha512": "GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.1": {"sha512": "0vaOeMeDpVUqvOJ0pHugoOqTZULdq7c0kZLzzlrQwUZ7ScUat27d8hrK3IxJ7FWF3OBZXSjA2nWYRcdA5WG9wg==", "type": "package", "path": "microsoft.extensions.hosting.windowsservices/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Hosting.WindowsServices.dll", "lib/net461/Microsoft.Extensions.Hosting.WindowsServices.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.WindowsServices.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.WindowsServices.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.xml", "microsoft.extensions.hosting.windowsservices.6.0.1.nupkg.sha512", "microsoft.extensions.hosting.windowsservices.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/6.0.0": {"sha512": "eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "type": "package", "path": "microsoft.extensions.logging/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.6.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"sha512": "pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"sha512": "ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "type": "package", "path": "microsoft.extensions.logging.configuration/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Configuration.dll", "lib/net461/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/6.0.0": {"sha512": "gsqKzOEdsvq28QiXFxagmn1oRB9GeI5GgYCkoybZtQA0IUb7QPwf1WmN3AwJeNIsadTvIFQCiVK0OVIgKfOBGg==", "type": "package", "path": "microsoft.extensions.logging.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Logging.Console.dll", "lib/net461/Microsoft.Extensions.Logging.Console.xml", "lib/net6.0/Microsoft.Extensions.Logging.Console.dll", "lib/net6.0/Microsoft.Extensions.Logging.Console.xml", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.Console.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.6.0.0.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"sha512": "M9g/JixseSZATJE9tcMn9uzoD4+DbSglivFqVx8YkRJ7VVPmnvCEbOZ0AAaxsL1EKyI4cz07DXOOJExxNsUOHw==", "type": "package", "path": "microsoft.extensions.logging.debug/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Debug.dll", "lib/net461/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.6.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"sha512": "rlo0RxlMd0WtLG3CHI0qOTp6fFn7MvQjlrCjucA31RqmiMFCZkF8CHNbe8O7tbBIyyoLGWB1he9CbaA5iyHthg==", "type": "package", "path": "microsoft.extensions.logging.eventlog/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.EventLog.dll", "lib/net461/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.6.0.0.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"sha512": "BeDyyqt7nkm/nr+Gdk+L8n1tUT/u33VkbXAOesgYSNsxDM9hJ1NOBGoZfj9rCbeD2+9myElI6JOVVFmnzgeWQA==", "type": "package", "path": "microsoft.extensions.logging.eventsource/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Logging.EventSource.dll", "lib/net461/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.EventSource.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.6.0.0.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/5.0.10": {"sha512": "pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "type": "package", "path": "microsoft.extensions.objectpool/5.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net5.0/Microsoft.Extensions.ObjectPool.dll", "lib/net5.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.5.0.10.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/6.0.0": {"sha512": "dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "type": "package", "path": "microsoft.extensions.options/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.6.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"sha512": "bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.21.1": {"sha512": "vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "type": "package", "path": "microsoft.identity.client/4.21.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard1.3/Microsoft.Identity.Client.dll", "lib/netstandard1.3/Microsoft.Identity.Client.xml", "lib/uap10.0/Microsoft.Identity.Client.dll", "lib/uap10.0/Microsoft.Identity.Client.pri", "lib/uap10.0/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "lib/xamarinmac20/Microsoft.Identity.Client.dll", "lib/xamarinmac20/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.21.1.nupkg.sha512", "microsoft.identity.client.nuspec", "ref/MonoAndroid10.0/Microsoft.Identity.Client.dll", "ref/MonoAndroid10.0/Microsoft.Identity.Client.xml", "ref/MonoAndroid9.0/Microsoft.Identity.Client.dll", "ref/MonoAndroid9.0/Microsoft.Identity.Client.xml", "ref/Xamarin.iOS10/Microsoft.Identity.Client.dll", "ref/Xamarin.iOS10/Microsoft.Identity.Client.xml", "ref/net45/Microsoft.Identity.Client.dll", "ref/net45/Microsoft.Identity.Client.xml", "ref/net461/Microsoft.Identity.Client.dll", "ref/net461/Microsoft.Identity.Client.xml", "ref/netcoreapp2.1/Microsoft.Identity.Client.dll", "ref/netcoreapp2.1/Microsoft.Identity.Client.xml", "ref/netstandard1.3/Microsoft.Identity.Client.dll", "ref/netstandard1.3/Microsoft.Identity.Client.xml", "ref/uap10.0/Microsoft.Identity.Client.dll", "ref/uap10.0/Microsoft.Identity.Client.xml", "ref/xamarinmac20/Microsoft.Identity.Client.dll", "ref/xamarinmac20/Microsoft.Identity.Client.xml"]}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"sha512": "0qjS31rN1MQTc46tAYbzmMTSRfdV5ndZxSjYxIGqKSidd4wpNJfNII/pdhU5Fx8olarQoKL9lqqYw4yNOIwT0Q==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.10.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.10.0": {"sha512": "zbcwV6esnNzhZZ/VP87dji6VrUBLB5rxnZBkDMqNYpyG+nrBnBsbm4PUYLCBMUflHCM9EMLDG0rLnqqT+l0ldA==", "type": "package", "path": "microsoft.identitymodel.logging/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.10.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.10.0": {"sha512": "DFyXD0xylP+DknCT3hzJ7q/Q5qRNu0hO/gCU90O0ATdR0twZmlcuY9RNYaaDofXKVbzcShYNCFCGle2G/o8mkg==", "type": "package", "path": "microsoft.identitymodel.protocols/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.10.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"sha512": "LVvMXAWPbPeEWTylDrxunlHH2wFyE4Mv0L4gZrJHC4HTESbWHquKZb/y/S8jgiQEDycOP0PDQvbG4RR/tr2TVQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.10.0": {"sha512": "qbf1NslutDB4oLrriYTJpy7oB1pbh2ej2lEHd2IPDQH9C74ysOdhU5wAC7KoXblldbo7YsNR2QYFOqQM/b0Rsg==", "type": "package", "path": "microsoft.identitymodel.tokens/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.10.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"sha512": "T5ahjOqWFMTSb9wFHKFNAcGXm35BxbUbwARtAPLSSPPFehcLz5mwDsKO1RR9R2aZ2Lk1BNQC7Ja63onOBE6rpA==", "type": "package", "path": "microsoft.io.recyclablememorystream/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.2.2.1.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Net.Http.Headers/2.1.0": {"sha512": "c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "type": "package", "path": "microsoft.net.http.headers/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.1.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.1": {"sha512": "TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "type": "package", "path": "microsoft.netcore.platforms/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.1.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.3": {"sha512": "3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "type": "package", "path": "microsoft.netcore.targets/1.1.3", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.3.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.OpenApi/1.3.1": {"sha512": "2X5CCFJCnx6v86fnpOg4TKlU1ba7MSf1yakeT7VI4846s7i6fOkERwStX94Rcq8wJsLyQYsUitd6vR38viePeA==", "type": "package", "path": "microsoft.openapi/1.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.3.1.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "MsgPack.Cli/1.0.1": {"sha512": "od0WujAAuPuRLoGfGNfE2iRPP7Kj+o174Oed2uirT03/Dp4rkccnEIqQl/QRHjly79sMwh6xKaIVsLqYwWtiHA==", "type": "package", "path": "msgpack.cli/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10/MsgPack.XML", "lib/monoandroid10/MsgPack.dll", "lib/net35/MsgPack.XML", "lib/net35/MsgPack.dll", "lib/net45/MsgPack.XML", "lib/net45/MsgPack.dll", "lib/net46/MsgPack.XML", "lib/net46/MsgPack.dll", "lib/netstandard1.1/MsgPack.XML", "lib/netstandard1.1/MsgPack.dll", "lib/netstandard1.3/MsgPack.XML", "lib/netstandard1.3/MsgPack.dll", "lib/netstandard2.0/MsgPack.XML", "lib/netstandard2.0/MsgPack.dll", "lib/sl5/MsgPack.XML", "lib/sl5/MsgPack.dll", "lib/uap10/MsgPack.XML", "lib/uap10/MsgPack.dll", "lib/windowsphone8/MsgPack.XML", "lib/windowsphone8/MsgPack.dll", "lib/xamarin.ios10/MsgPack.XML", "lib/xamarin.ios10/MsgPack.dll", "msgpack.cli.1.0.1.nupkg.sha512", "msgpack.cli.nuspec"]}, "MySqlConnector/2.2.5": {"sha512": "6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "type": "package", "path": "mysqlconnector/2.2.5", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/MySqlConnector.dll", "lib/net461/MySqlConnector.xml", "lib/net471/MySqlConnector.dll", "lib/net471/MySqlConnector.xml", "lib/net6.0/MySqlConnector.dll", "lib/net6.0/MySqlConnector.xml", "lib/net7.0/MySqlConnector.dll", "lib/net7.0/MySqlConnector.xml", "lib/netcoreapp3.1/MySqlConnector.dll", "lib/netcoreapp3.1/MySqlConnector.xml", "lib/netstandard2.0/MySqlConnector.dll", "lib/netstandard2.0/MySqlConnector.xml", "lib/netstandard2.1/MySqlConnector.dll", "lib/netstandard2.1/MySqlConnector.xml", "logo.png", "mysqlconnector.2.2.5.nupkg.sha512", "mysqlconnector.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Nito.AsyncEx/5.1.0": {"sha512": "KOahZdW0fRLnoym5lGtyWpFUZ94I5s0wLgYF5RHhK8RUhXpCiEX+185qjNvkse3H9WJV2/pFfPlKZHv8Eej7Hw==", "type": "package", "path": "nito.asyncex/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "nito.asyncex.5.1.0.nupkg.sha512", "nito.asyncex.nuspec"]}, "Nito.AsyncEx.Context/5.1.0": {"sha512": "EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "type": "package", "path": "nito.asyncex.context/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.0.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Coordination/5.1.0": {"sha512": "Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "type": "package", "path": "nito.asyncex.coordination/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Coordination.dll", "lib/netstandard1.3/Nito.AsyncEx.Coordination.xml", "lib/netstandard2.0/Nito.AsyncEx.Coordination.dll", "lib/netstandard2.0/Nito.AsyncEx.Coordination.xml", "nito.asyncex.coordination.5.1.0.nupkg.sha512", "nito.asyncex.coordination.nuspec"]}, "Nito.AsyncEx.Interop.WaitHandles/5.1.0": {"sha512": "wFm3lrXXNPBtZHjLI21xhcADoh5CzO5KKNO38ybLO/CcL9zMUWWfsNiAFbw8JGp/wHoxhfdEUlThBnY3XaLR/w==", "type": "package", "path": "nito.asyncex.interop.waithandles/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Interop.WaitHandles.dll", "lib/netstandard1.3/Nito.AsyncEx.Interop.WaitHandles.xml", "lib/netstandard2.0/Nito.AsyncEx.Interop.WaitHandles.dll", "lib/netstandard2.0/Nito.AsyncEx.Interop.WaitHandles.xml", "nito.asyncex.interop.waithandles.5.1.0.nupkg.sha512", "nito.asyncex.interop.waithandles.nuspec"]}, "Nito.AsyncEx.Oop/5.1.0": {"sha512": "J5DVgQrmE9qMNhK2eEMsuEw7V7cw7MIPrv3jqqQWolzDXkOxJFFYKUK+4dnC6UAEmum3xRVD2oBAoXg0vdYDDQ==", "type": "package", "path": "nito.asyncex.oop/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Oop.dll", "lib/netstandard1.3/Nito.AsyncEx.Oop.xml", "lib/netstandard2.0/Nito.AsyncEx.Oop.dll", "lib/netstandard2.0/Nito.AsyncEx.Oop.xml", "nito.asyncex.oop.5.1.0.nupkg.sha512", "nito.asyncex.oop.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.0": {"sha512": "tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "type": "package", "path": "nito.asyncex.tasks/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.0.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Cancellation/1.1.0": {"sha512": "BSezc6jsEEAoa8UtVjQ6Qr/D5xX+FozlDKFHAvDeTv24I7ZZmmfbFxEmdjaSLnrboz1WMRjUKCQwZw7Gf4+WcA==", "type": "package", "path": "nito.cancellation/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.0/Nito.Cancellation.dll", "lib/netstandard1.0/Nito.Cancellation.xml", "lib/netstandard2.0/Nito.Cancellation.dll", "lib/netstandard2.0/Nito.Cancellation.xml", "nito.cancellation.1.1.0.nupkg.sha512", "nito.cancellation.nuspec"]}, "Nito.Collections.Deque/1.1.0": {"sha512": "RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "type": "package", "path": "nito.collections.deque/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.0/Nito.Collections.Deque.dll", "lib/netstandard1.0/Nito.Collections.Deque.xml", "lib/netstandard2.0/Nito.Collections.Deque.dll", "lib/netstandard2.0/Nito.Collections.Deque.xml", "nito.collections.deque.1.1.0.nupkg.sha512", "nito.collections.deque.nuspec"]}, "Nito.Disposables/2.2.0": {"sha512": "QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "type": "package", "path": "nito.disposables/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.0.nupkg.sha512", "nito.disposables.nuspec"]}, "NodaTime/3.1.10": {"sha512": "56V8ELg9Az1S0Nij44xILAtfUQs3WTLIwbTQTuWZxL8EagsfUkkPAoWxv/HyZccmfaUEqFHfHiAuqfj/NfbfrA==", "type": "package", "path": "nodatime/3.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/NodaTime.dll", "lib/net6.0/NodaTime.pdb", "lib/net6.0/NodaTime.xml", "lib/netstandard2.0/NodaTime.dll", "lib/netstandard2.0/NodaTime.pdb", "lib/netstandard2.0/NodaTime.xml", "nodatime.3.1.10.nupkg.sha512", "nodatime.nuspec"]}, "Npgsql/6.0.8": {"sha512": "wKa8MJEJaj0xQXUQZGv7q/KfPID23jSSvFFtljMworrv7dNajr0GN8PCU1SpywqHjMWdYEfK29DY1aYbiISbQg==", "type": "package", "path": "npgsql/6.0.8", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Npgsql.dll", "lib/net5.0/Npgsql.xml", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/netcoreapp3.1/Npgsql.dll", "lib/netcoreapp3.1/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.6.0.8.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.8": {"sha512": "YJRpO+3wXQyWuwRUCVJj/Rsn46sY0bZ6uCGOEFApiRe0ZYJ6N6TxZUWKbTNJYjesickcLGzynOerpSbDJX1AYg==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/6.0.8", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "npgsql.entityframeworkcore.postgresql.6.0.8.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "NPOI/2.6.0": {"sha512": "Pwjo65CUH3MiRnBEbVo8ff31ZrDGdGyyFJyAEncmbTQ0/gYgDkBUnGKm20aLpdwCpPNLzvapZm8v5tx4S6qAWg==", "type": "package", "path": "npoi/2.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "Read Me.txt", "lib/net472/NPOI.OOXML.XML", "lib/net472/NPOI.OOXML.dll", "lib/net472/NPOI.OOXML.pdb", "lib/net472/NPOI.OpenXml4Net.XML", "lib/net472/NPOI.OpenXml4Net.dll", "lib/net472/NPOI.OpenXml4Net.pdb", "lib/net472/NPOI.OpenXmlFormats.dll", "lib/net472/NPOI.OpenXmlFormats.dll.config", "lib/net472/NPOI.OpenXmlFormats.pdb", "lib/net472/NPOI.XML", "lib/net472/NPOI.dll", "lib/net472/NPOI.pdb", "lib/net6.0/NPOI.OOXML.deps.json", "lib/net6.0/NPOI.OOXML.dll", "lib/net6.0/NPOI.OOXML.pdb", "lib/net6.0/NPOI.OOXML.xml", "lib/net6.0/NPOI.OpenXml4Net.dll", "lib/net6.0/NPOI.OpenXml4Net.pdb", "lib/net6.0/NPOI.OpenXmlFormats.dll", "lib/net6.0/NPOI.OpenXmlFormats.dll.config", "lib/net6.0/NPOI.OpenXmlFormats.pdb", "lib/net6.0/NPOI.dll", "lib/net6.0/NPOI.pdb", "lib/net6.0/NPOI.xml", "lib/netstandard2.0/NPOI.OOXML.deps.json", "lib/netstandard2.0/NPOI.OOXML.dll", "lib/netstandard2.0/NPOI.OOXML.pdb", "lib/netstandard2.0/NPOI.OOXML.xml", "lib/netstandard2.0/NPOI.OpenXml4Net.dll", "lib/netstandard2.0/NPOI.OpenXml4Net.pdb", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll.config", "lib/netstandard2.0/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.0/NPOI.dll", "lib/netstandard2.0/NPOI.pdb", "lib/netstandard2.0/NPOI.xml", "lib/netstandard2.1/NPOI.OOXML.deps.json", "lib/netstandard2.1/NPOI.OOXML.dll", "lib/netstandard2.1/NPOI.OOXML.pdb", "lib/netstandard2.1/NPOI.OOXML.xml", "lib/netstandard2.1/NPOI.OpenXml4Net.dll", "lib/netstandard2.1/NPOI.OpenXml4Net.pdb", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll.config", "lib/netstandard2.1/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.1/NPOI.dll", "lib/netstandard2.1/NPOI.pdb", "lib/netstandard2.1/NPOI.xml", "logo/120_120.jpg", "logo/240_240.png", "logo/32_32.jpg", "logo/60_60.jpg", "npoi.2.6.0.nupkg.sha512", "npoi.nuspec"]}, "Npoi.Mapper/6.0.0": {"sha512": "czueOx+9qMSi8zBogXpKufSVNUeDZ/vGcMJbXgSEe01EGX+iE64fvXeIR7waCcTgh5ecI2/Lwbbek3DcvgahNA==", "type": "package", "path": "npoi.mapper/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Npoi.Mapper.dll", "npoi.mapper.6.0.0.nupkg.sha512", "npoi.mapper.nuspec"]}, "Oracle.EntityFrameworkCore/6.21.61": {"sha512": "X8vCHqyexPekPyoiUkoQMVxXzsB8QBKM8C1noFrTEev5/AgfnyGDgzfoF9Y8MDUS4jSiVzJafqc/olk7TlBWoA==", "type": "package", "path": "oracle.entityframeworkcore/6.21.61", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Oracle.EntityFrameworkCore.dll", "oracle.entityframeworkcore.6.21.61.nupkg.sha512", "oracle.entityframeworkcore.nuspec", "readme.txt"]}, "Oracle.ManagedDataAccess.Core/3.21.140": {"sha512": "F1c4adtCGe6C+h04fryfAeRC3XsTG73Xd6vYhpD/6ABfdWo4IOTLy8EZs4vhya2+OR0BME/751Y3BCZcWdNYiQ==", "type": "package", "path": "oracle.manageddataaccess.core/3.21.140", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PerfCounters/register_odpc_perfmon_counters.ps1", "PerfCounters/unregister_odpc_perfmon_counters.ps1", "info.txt", "lib/netstandard2.1/Oracle.ManagedDataAccess.dll", "oracle.manageddataaccess.core.3.21.140.nupkg.sha512", "oracle.manageddataaccess.core.nuspec", "readme.txt"]}, "Oscar.Data.SqlClient/4.0.4": {"sha512": "VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "type": "package", "path": "oscar.data.sqlclient/4.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Oscar.Data.SqlClient.dll", "oscar.data.sqlclient.4.0.4.nupkg.sha512", "oscar.data.sqlclient.nuspec"]}, "Pipelines.Sockets.Unofficial/2.2.8": {"sha512": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "Polly/7.2.3": {"sha512": "DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "type": "package", "path": "polly/7.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Polly.png", "lib/net461/Polly.dll", "lib/net461/Polly.pdb", "lib/net461/Polly.xml", "lib/net472/Polly.dll", "lib/net472/Polly.pdb", "lib/net472/Polly.xml", "lib/netstandard1.1/Polly.dll", "lib/netstandard1.1/Polly.pdb", "lib/netstandard1.1/Polly.xml", "lib/netstandard2.0/Polly.dll", "lib/netstandard2.0/Polly.pdb", "lib/netstandard2.0/Polly.xml", "polly.7.2.3.nupkg.sha512", "polly.nuspec"]}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"sha512": "KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "type": "package", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll", "lib/net6.0/Pomelo.EntityFrameworkCore.MySql.xml", "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512", "pomelo.entityframeworkcore.mysql.nuspec"]}, "Portable.BouncyCastle/1.9.0": {"sha512": "eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "type": "package", "path": "portable.bouncycastle/1.9.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/BouncyCastle.Crypto.dll", "lib/net40/BouncyCastle.Crypto.xml", "lib/netstandard2.0/BouncyCastle.Crypto.dll", "lib/netstandard2.0/BouncyCastle.Crypto.xml", "portable.bouncycastle.1.9.0.nupkg.sha512", "portable.bouncycastle.nuspec"]}, "protobuf-net/3.1.0": {"sha512": "dZjLX2TzcEkPy/oZfCB/GE32+oJlX58w/zNMkJ1JPdMeO1oTSfiXVxgFagya8xblBhHpr1v4PBbt3Q13Yvvtwg==", "type": "package", "path": "protobuf-net/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/protobuf-net.dll", "lib/net461/protobuf-net.xml", "lib/net5.0/protobuf-net.dll", "lib/net5.0/protobuf-net.xml", "lib/netcoreapp3.1/protobuf-net.dll", "lib/netcoreapp3.1/protobuf-net.xml", "lib/netstandard2.0/protobuf-net.dll", "lib/netstandard2.0/protobuf-net.xml", "lib/netstandard2.1/protobuf-net.dll", "lib/netstandard2.1/protobuf-net.xml", "protobuf-net.3.1.0.nupkg.sha512", "protobuf-net.nuspec", "protobuf-net.png"]}, "protobuf-net.Core/3.1.22": {"sha512": "82iIFBuQW2K8kixHQ1Eo6qWfnztNYRGU6OGqGmq7c0r8T9X9y9j79SHQSeI/hK3gK6EvHbYMfeGhpRJzA/zv1Q==", "type": "package", "path": "protobuf-net.core/3.1.22", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/protobuf-net.Core.dll", "lib/net462/protobuf-net.Core.xml", "lib/net6.0/protobuf-net.Core.dll", "lib/net6.0/protobuf-net.Core.xml", "lib/netcoreapp3.1/protobuf-net.Core.dll", "lib/netcoreapp3.1/protobuf-net.Core.xml", "lib/netstandard2.0/protobuf-net.Core.dll", "lib/netstandard2.0/protobuf-net.Core.xml", "lib/netstandard2.1/protobuf-net.Core.dll", "lib/netstandard2.1/protobuf-net.Core.xml", "protobuf-net.core.3.1.22.nupkg.sha512", "protobuf-net.core.nuspec", "protobuf-net.png"]}, "RestSharp/112.1.0": {"sha512": "bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "type": "package", "path": "restsharp/112.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net471/RestSharp.dll", "lib/net471/RestSharp.xml", "lib/net48/RestSharp.dll", "lib/net48/RestSharp.xml", "lib/net6.0/RestSharp.dll", "lib/net6.0/RestSharp.xml", "lib/net7.0/RestSharp.dll", "lib/net7.0/RestSharp.xml", "lib/net8.0/RestSharp.dll", "lib/net8.0/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.112.1.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Scrutor/3.3.0": {"sha512": "BwqCnFzp2/Z+pq17iztxlIkR/ZANyPRR4PdE57WL1w/JW4AM/2imoxBWTL3+G+YXA46ce4s9OUgwWqTXYrtI8A==", "type": "package", "path": "scrutor/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Scrutor.dll", "lib/net461/Scrutor.pdb", "lib/net461/Scrutor.xml", "lib/netcoreapp3.1/Scrutor.dll", "lib/netcoreapp3.1/Scrutor.pdb", "lib/netcoreapp3.1/Scrutor.xml", "lib/netstandard2.0/Scrutor.dll", "lib/netstandard2.0/Scrutor.pdb", "lib/netstandard2.0/Scrutor.xml", "scrutor.3.3.0.nupkg.sha512", "scrutor.nuspec"]}, "Serilog/3.1.1": {"sha512": "P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "type": "package", "path": "serilog/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net5.0/Serilog.dll", "lib/net5.0/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net7.0/Serilog.dll", "lib/net7.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.3.1.1.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/6.1.0": {"sha512": "iMwFUJDN+/yWIPz4TKCliagJ1Yn//SceCYCzgdPwe/ECYUwb5/WUL8cTzRKV+tFwxGjLEV/xpm0GupS5RwbhSQ==", "type": "package", "path": "serilog.aspnetcore/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net5.0/Serilog.AspNetCore.dll", "lib/net5.0/Serilog.AspNetCore.xml", "lib/netcoreapp3.1/Serilog.AspNetCore.dll", "lib/netcoreapp3.1/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.6.1.0.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Enrichers.Environment/2.3.0": {"sha512": "AdZXURQ0dQCCjst3Jn3lwFtGicWjGE4wov9E5BPc4N5cruGmd2y9wprCYEjFteU84QMbxk35fpeTuHs6M4VGYw==", "type": "package", "path": "serilog.enrichers.environment/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Enrichers.Environment.dll", "lib/netstandard1.3/Serilog.Enrichers.Environment.dll", "lib/netstandard1.5/Serilog.Enrichers.Environment.dll", "lib/netstandard2.0/Serilog.Enrichers.Environment.dll", "serilog.enrichers.environment.2.3.0.nupkg.sha512", "serilog.enrichers.environment.nuspec"]}, "Serilog.Extensions.Hosting/5.0.1": {"sha512": "o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "type": "package", "path": "serilog.extensions.hosting/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.5.0.1.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/3.1.0": {"sha512": "IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "type": "package", "path": "serilog.extensions.logging/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.3.1.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/1.1.0": {"sha512": "pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "type": "package", "path": "serilog.formatting.compact/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/Serilog.Formatting.Compact.dll", "lib/net452/Serilog.Formatting.Compact.xml", "lib/netstandard1.1/Serilog.Formatting.Compact.dll", "lib/netstandard1.1/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog.formatting.compact.1.1.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Formatting.Elasticsearch/10.0.0": {"sha512": "N9FsqRnl+rQOD2ldJMbEgPySwFDkUB8s6PCj5e92ryektgyZNFze3R2c1nwGOz6jhJ2aIzBIBNDM69CCzdHO0w==", "type": "package", "path": "serilog.formatting.elasticsearch/10.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll", "lib/netstandard2.0/Serilog.Formatting.Elasticsearch.xml", "serilog-sink-nuget.png", "serilog.formatting.elasticsearch.10.0.0.nupkg.sha512", "serilog.formatting.elasticsearch.nuspec"]}, "Serilog.Settings.Configuration/7.0.1": {"sha512": "FpUWtc0YUQvCfrKRI73KbmpWK3RvWTQr9gMDfTPEtmVI6f7KkY8Egj6r1BQA1/4oyTjxRbTn5yKX+2+zaWTwrg==", "type": "package", "path": "serilog.settings.configuration/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net6.0/Serilog.Settings.Configuration.dll", "lib/net6.0/Serilog.Settings.Configuration.xml", "lib/net7.0/Serilog.Settings.Configuration.dll", "lib/net7.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.7.0.1.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Async/1.5.0": {"sha512": "csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "type": "package", "path": "serilog.sinks.async/1.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Sinks.Async.dll", "lib/net45/Serilog.Sinks.Async.xml", "lib/net461/Serilog.Sinks.Async.dll", "lib/net461/Serilog.Sinks.Async.xml", "lib/netstandard1.1/Serilog.Sinks.Async.dll", "lib/netstandard1.1/Serilog.Sinks.Async.xml", "lib/netstandard2.0/Serilog.Sinks.Async.dll", "lib/netstandard2.0/Serilog.Sinks.Async.xml", "serilog-sink-nuget.png", "serilog.sinks.async.1.5.0.nupkg.sha512", "serilog.sinks.async.nuspec"]}, "Serilog.Sinks.Console/4.1.0": {"sha512": "K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "type": "package", "path": "serilog.sinks.console/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Console.dll", "lib/net45/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/netstandard1.3/Serilog.Sinks.Console.dll", "lib/netstandard1.3/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.4.1.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/2.0.0": {"sha512": "Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "type": "package", "path": "serilog.sinks.debug/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Debug.dll", "lib/net45/Serilog.Sinks.Debug.xml", "lib/net46/Serilog.Sinks.Debug.dll", "lib/net46/Serilog.Sinks.Debug.xml", "lib/netstandard1.0/Serilog.Sinks.Debug.dll", "lib/netstandard1.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.1/Serilog.Sinks.Debug.dll", "lib/netstandard2.1/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.2.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.FastConsole/2.2.0": {"sha512": "2PMJe5DQbUf//WWSCGRt4EOrJYb53ILuB3vUY6T3ZL3HlX3YjiDsMCirmpLUp/SwG+Cp1Kt6sSc/QOsRkdGb4Q==", "type": "package", "path": "serilog.sinks.fastconsole/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Serilog.Sinks.FastConsole.dll", "lib/net6.0/Serilog.Sinks.FastConsole.dll", "lib/netstandard2.0/Serilog.Sinks.FastConsole.dll", "lib/netstandard2.1/Serilog.Sinks.FastConsole.dll", "serilog-sink-nuget.png", "serilog.sinks.fastconsole.2.2.0.nupkg.sha512", "serilog.sinks.fastconsole.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.SpectreConsole/0.3.3": {"sha512": "Lw3CBuJQ+HY4Pso3km7isrDXdmLpwZ5sgt1SXr5v6UEkO2hsSP4iHIXvLKbCgJyvUyxikVn0iFQMj4eGfl4/TA==", "type": "package", "path": "serilog.sinks.spectreconsole/0.3.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/netstandard2.0/Serilog.Sinks.SpectreConsole.dll", "serilog.sinks.spectreconsole.0.3.3.nupkg.sha512", "serilog.sinks.spectreconsole.nuspec"]}, "SharpZipLib/1.3.3": {"sha512": "N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "type": "package", "path": "sharpziplib/1.3.3", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net45/ICSharpCode.SharpZipLib.dll", "lib/net45/ICSharpCode.SharpZipLib.pdb", "lib/net45/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.3.3.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.Fonts/1.0.0-beta18": {"sha512": "evykNmy/kEE9EAEKgZm3MNUYXuMHFfmcLUNPw7Ho5q7OI96GFkkIxBm+QaKOTPBKw+L0AjKOs+ArVg8P40Ac9g==", "type": "package", "path": "sixlabors.fonts/1.0.0-beta18", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0-beta18.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/2.1.3": {"sha512": "8yonNRWX3vUE9k29ta0Hbfa0AEc0hbDjSH/nZ3vOTJEXmY6hLnGsjDKoz96Z+AgOsrdkAu6PdL/Ebaf70aitzw==", "type": "package", "path": "sixlabors.imagesharp/2.1.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/SixLabors.ImageSharp.dll", "lib/net472/SixLabors.ImageSharp.xml", "lib/netcoreapp2.1/SixLabors.ImageSharp.dll", "lib/netcoreapp2.1/SixLabors.ImageSharp.xml", "lib/netcoreapp3.1/SixLabors.ImageSharp.dll", "lib/netcoreapp3.1/SixLabors.ImageSharp.xml", "lib/netstandard2.0/SixLabors.ImageSharp.dll", "lib/netstandard2.0/SixLabors.ImageSharp.xml", "lib/netstandard2.1/SixLabors.ImageSharp.dll", "lib/netstandard2.1/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.2.1.3.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SkiaSharp/2.88.9": {"sha512": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "type": "package", "path": "skiasharp/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.9.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"sha512": "/DoKtdyvRgCC5GR/SH+ps3ZiOjmf0BYpAyrhWQELFOO1hdcqddrDVJjDNCOJ41vV+NlS5b3kcDoZZ7jLhFjyXg==", "type": "package", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.NoDependencies.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.NoDependencies.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.nodependencies.2.88.8.nupkg.sha512", "skiasharp.nativeassets.linux.nodependencies.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"sha512": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"sha512": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "Snowflake.Core/2.0.0": {"sha512": "YB4SqYbTgG6UvwFHFZOWny/pE415KNjRbbiGwfPOUDWUCEjYLB9P11EiIHQ7y6LlkjejVAAIi8Wt3WM7Ys9qyw==", "type": "package", "path": "snowflake.core/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Snowflake.Core.dll", "snowflake.core.2.0.0.nupkg.sha512", "snowflake.core.nuspec"]}, "Spectre.Console/0.45.0": {"sha512": "e//13o8/BCrWmwN26eJ4zCzD2iq7iUlqQd+nDI9nJUdnJ/rYAanYiNFZZ7YHwlv48IKuKtRYYP6/wPt1DG67ww==", "type": "package", "path": "spectre.console/0.45.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Spectre.Console.dll", "lib/net6.0/Spectre.Console.xml", "lib/netstandard2.0/Spectre.Console.dll", "lib/netstandard2.0/Spectre.Console.xml", "small-logo.png", "spectre.console.0.45.0.nupkg.sha512", "spectre.console.nuspec"]}, "Spire.Officefor.NETStandard/9.2.1": {"sha512": "qTd+Zr308FMM+568vuQxZpxHQ97jJzqGEEsxlXPQP2B+j+RMega3hSg5qiADyS6KMecA9AzckLsUhS0f2AZZsQ==", "type": "package", "path": "spire.officefor.netstandard/9.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Spire.Barcode.dll", "lib/netstandard2.0/Spire.Barcode.xml", "lib/netstandard2.0/Spire.Doc.dll", "lib/netstandard2.0/Spire.Doc.xml", "lib/netstandard2.0/Spire.Email.dll", "lib/netstandard2.0/Spire.Email.xml", "lib/netstandard2.0/Spire.Pdf.dll", "lib/netstandard2.0/Spire.Pdf.xml", "lib/netstandard2.0/Spire.Presentation.dll", "lib/netstandard2.0/Spire.Presentation.xml", "lib/netstandard2.0/Spire.XLS.dll", "lib/netstandard2.0/Spire.XLS.xml", "spire.officefor.netstandard.9.2.1.nupkg.sha512", "spire.officefor.netstandard.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"sha512": "BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.6": {"sha512": "wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "type": "package", "path": "sqlitepclraw.core/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.6.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"sha512": "2ObJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"sha512": "PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "SqlSugarCore/*********": {"sha512": "ubV7ls5zrgP5r+hAy622etjhTmrgKiK9wCX/5P3kHGfySHV1JLhsGxI4+Pc4JDCsIpveaspip+uzaKrK4/xGxQ==", "type": "package", "path": "sqlsugarcore/*********", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/SqlSugar.dll", "sqlsugarcore.*********.nupkg.sha512", "sqlsugarcore.nuspec"]}, "SqlSugarCore.Dm/8.6.0": {"sha512": "Q0NAjF9hvkxLbNedIrCqKd3uru0enzZ49GaQtenvsLDQ29aHwlSqg1mRkVYxZ/85UYJFgXh+XHqABSrMgun4aw==", "type": "package", "path": "sqlsugarcore.dm/8.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/DM.DmProvider.dll", "sqlsugarcore.dm.8.6.0.nupkg.sha512", "sqlsugarcore.dm.nuspec"]}, "SqlSugarCore.Kdbndp/9.3.6.801": {"sha512": "FM8e18+lVENoG1qK1sK0TuIuCL1ZT/4dWF0wVvh3q03WU5Eu8l+7YP/qJvg9nRP034U1shgJAETpoHq2uXAHmQ==", "type": "package", "path": "sqlsugarcore.kdbndp/9.3.6.801", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/Kdbndp.dll", "sqlsugarcore.kdbndp.9.3.6.801.nupkg.sha512", "sqlsugarcore.kdbndp.nuspec"]}, "StackExchange.Redis/2.8.31": {"sha512": "RCHVQa9Zke8k0oBgJn1Yl6BuYy8i6kv+sdMObiH60nOwD6QvWAjxdDwOm+LO78E8WsGiPqgOuItkz98fPS6haQ==", "type": "package", "path": "stackexchange.redis/2.8.31", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net6.0/StackExchange.Redis.dll", "lib/net6.0/StackExchange.Redis.xml", "lib/net8.0/StackExchange.Redis.dll", "lib/net8.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.8.31.nupkg.sha512", "stackexchange.redis.nuspec"]}, "StackExchange.Redis.Extensions.AspNetCore/10.2.0": {"sha512": "kEOveCZy04/nWLQmC0EnLk2a+aBKQs+qUFO+6o7cdOaZMvcbBVExkytwaCS1J/ddr5buOO5Gn8k3SZbkfFixIA==", "type": "package", "path": "stackexchange.redis.extensions.aspnetcore/10.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/.xml", "lib/net6.0/StackExchange.Redis.Extensions.AspNetCore.dll", "lib/net7.0/.xml", "lib/net7.0/StackExchange.Redis.Extensions.AspNetCore.dll", "lib/net8.0/.xml", "lib/net8.0/StackExchange.Redis.Extensions.AspNetCore.dll", "lib/netstandard2.1/.xml", "lib/netstandard2.1/StackExchange.Redis.Extensions.AspNetCore.dll", "stackexchange.redis.extensions.aspnetcore.10.2.0.nupkg.sha512", "stackexchange.redis.extensions.aspnetcore.nuspec"]}, "StackExchange.Redis.Extensions.Core/10.2.0": {"sha512": "wpgpVdZIm7sw5rx19SGXbPWeVBjDFVjxDa3GWUMZRcrbmOMCNNA54+7xPQP70pUZmDYuEYU1fHyb/HU0X1+Mqg==", "type": "package", "path": "stackexchange.redis.extensions.core/10.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/.xml", "lib/net6.0/StackExchange.Redis.Extensions.Core.dll", "lib/net7.0/.xml", "lib/net7.0/StackExchange.Redis.Extensions.Core.dll", "lib/net8.0/.xml", "lib/net8.0/StackExchange.Redis.Extensions.Core.dll", "lib/netstandard2.1/.xml", "lib/netstandard2.1/StackExchange.Redis.Extensions.Core.dll", "stackexchange.redis.extensions.core.10.2.0.nupkg.sha512", "stackexchange.redis.extensions.core.nuspec"]}, "StackExchange.Redis.Extensions.MsgPack/10.2.0": {"sha512": "bOwEOyK2O1HNZWH+ZjD46CkOChywspRqHVuadOvaBL6As4urYn9iVmkbP68TdTOVAcf4GcjnvPBb34cJ/7MmPg==", "type": "package", "path": "stackexchange.redis.extensions.msgpack/10.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/.xml", "lib/net6.0/StackExchange.Redis.Extensions.MsgPack.dll", "lib/net7.0/.xml", "lib/net7.0/StackExchange.Redis.Extensions.MsgPack.dll", "lib/net8.0/.xml", "lib/net8.0/StackExchange.Redis.Extensions.MsgPack.dll", "lib/netstandard2.1/.xml", "lib/netstandard2.1/StackExchange.Redis.Extensions.MsgPack.dll", "stackexchange.redis.extensions.msgpack.10.2.0.nupkg.sha512", "stackexchange.redis.extensions.msgpack.nuspec"]}, "StackExchange.Redis.Extensions.Newtonsoft/10.2.0": {"sha512": "BXguD4Ul/HtxP57Z0O1fQTaMD0uzg79hPCyZDOIf9CnhJkwcC9HUpTybQSFh0E64k0EIeyyikFSVV7uPbzHahQ==", "type": "package", "path": "stackexchange.redis.extensions.newtonsoft/10.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/.xml", "lib/net6.0/StackExchange.Redis.Extensions.Newtonsoft.dll", "lib/net7.0/.xml", "lib/net7.0/StackExchange.Redis.Extensions.Newtonsoft.dll", "lib/net8.0/.xml", "lib/net8.0/StackExchange.Redis.Extensions.Newtonsoft.dll", "lib/netstandard2.1/.xml", "lib/netstandard2.1/StackExchange.Redis.Extensions.Newtonsoft.dll", "stackexchange.redis.extensions.newtonsoft.10.2.0.nupkg.sha512", "stackexchange.redis.extensions.newtonsoft.nuspec"]}, "Swashbuckle.AspNetCore/6.2.3": {"sha512": "cnzQDn0Le+hInsw2SYwlOhOCPXpYi/szcvnyqZJ12v+QyrLBwAmWXBg6RIyHB18s/mLeywC+Rg2O9ndz0IUNYQ==", "type": "package", "path": "swashbuckle.aspnetcore/6.2.3", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.2.3.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"sha512": "AVvG44LqDjB765pVldSbRPpH6iTIo6xmyoZE9aP8FcREMMUj7WB1cSMF+bWtUCkeEVwVTr8iGDxLfrCS03uIuQ==", "type": "package", "path": "swashbuckle.aspnetcore.filters/7.0.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Filters.dll", "lib/netcoreapp3.1/Swashbuckle.AspNetCore.Filters.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll", "swashbuckle.aspnetcore.filters.7.0.5.nupkg.sha512", "swashbuckle.aspnetcore.filters.nuspec"]}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"sha512": "iY3FK5URcNqdESPFDWL0YAe7X2sxZJj7YM5gTCIHvtu4d/h9NurhBLII96+obQghbJ7i45TVlPeYo4jb9WmVGg==", "type": "package", "path": "swashbuckle.aspnetcore.filters.abstractions/7.0.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll", "lib/netcoreapp3.1/Swashbuckle.AspNetCore.Filters.Abstractions.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll", "swashbuckle.aspnetcore.filters.abstractions.7.0.5.nupkg.sha512", "swashbuckle.aspnetcore.filters.abstractions.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"sha512": "qOF7j1sL0bWm8g/qqHVPCvkO3JlVvUIB8WfC98kSh6BT5y5DAnBNctfac7XR5EZf+eD7/WasvANncTqwZYfmWQ==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.2.3.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"sha512": "+Xq7WdMCCfcXlnbLJVFNgY8ITdP2TRYIlpbt6IKzDw5FwFxdi9lBfNDtcT+/wkKwX70iBBFmXldnnd02/VO72A==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.2.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"sha512": "bCRI87uKJVb4G+KURWm8LQrL64St04dEFZcF6gIM67Zc0Sr/N47EO83ybLMYOvfNdO1DCv8xwPcrz9J/VEhQ5g==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.2.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/6.0.0": {"sha512": "7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "type": "package", "path": "system.configuration.configurationmanager/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.Common/4.3.0": {"sha512": "lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "type": "package", "path": "system.data.common/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.Common.dll", "lib/netstandard1.2/System.Data.Common.dll", "lib/portable-net451+win8+wp8+wpa81/System.Data.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.Common.dll", "ref/netstandard1.2/System.Data.Common.dll", "ref/netstandard1.2/System.Data.Common.xml", "ref/netstandard1.2/de/System.Data.Common.xml", "ref/netstandard1.2/es/System.Data.Common.xml", "ref/netstandard1.2/fr/System.Data.Common.xml", "ref/netstandard1.2/it/System.Data.Common.xml", "ref/netstandard1.2/ja/System.Data.Common.xml", "ref/netstandard1.2/ko/System.Data.Common.xml", "ref/netstandard1.2/ru/System.Data.Common.xml", "ref/netstandard1.2/zh-hans/System.Data.Common.xml", "ref/netstandard1.2/zh-hant/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/System.Data.Common.dll", "ref/portable-net451+win8+wp8+wpa81/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/de/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/es/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/fr/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/it/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ja/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ko/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ru/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/zh-hans/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/zh-hant/System.Data.Common.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.data.common.4.3.0.nupkg.sha512", "system.data.common.nuspec"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/6.0.0": {"sha512": "lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "type": "package", "path": "system.diagnostics.eventlog/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.EventLog.dll", "lib/net461/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.6.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/6.0.1": {"sha512": "dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "type": "package", "path": "system.diagnostics.performancecounter/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.PerformanceCounter.dll", "lib/net461/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll", "lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.6.0.1.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/6.0.1": {"sha512": "935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "type": "package", "path": "system.directoryservices/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/_._", "lib/net6.0/System.DirectoryServices.dll", "lib/net6.0/System.DirectoryServices.xml", "lib/netcoreapp3.1/System.DirectoryServices.dll", "lib/netcoreapp3.1/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.xml", "runtimes/win/lib/netcoreapp3.1/System.DirectoryServices.dll", "runtimes/win/lib/netcoreapp3.1/System.DirectoryServices.xml", "system.directoryservices.6.0.1.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/6.0.2": {"sha512": "vDDPWwHn3/DNZ+kPkdXHoada+tKPEC9bVqDOr4hK6HBSP7hGCUTA0Zw6WU5qpGaqa5M1/V+axHMIv+DNEbIf6g==", "type": "package", "path": "system.directoryservices.protocols/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/_._", "lib/net6.0/System.DirectoryServices.Protocols.dll", "lib/net6.0/System.DirectoryServices.Protocols.xml", "lib/netcoreapp3.1/System.DirectoryServices.Protocols.dll", "lib/netcoreapp3.1/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/netcoreapp3.1/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/netcoreapp3.1/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/netcoreapp3.1/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/netcoreapp3.1/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/netcoreapp3.1/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/netcoreapp3.1/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.6.0.2.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/6.0.0": {"sha512": "T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "type": "package", "path": "system.formats.asn1/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.6.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"sha512": "C+Q5ORsFycRkRuvy/Xd0Pv5xVpmWSAvQYZAGs7VQogmkqlLhvfZXTgBIlHqC3cxkstSoLJAYx6xZB7foQ2y5eg==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.10.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Hashing/7.0.0": {"sha512": "sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "type": "package", "path": "system.io.hashing/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Hashing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Hashing.targets", "lib/net462/System.IO.Hashing.dll", "lib/net462/System.IO.Hashing.xml", "lib/net6.0/System.IO.Hashing.dll", "lib/net6.0/System.IO.Hashing.xml", "lib/net7.0/System.IO.Hashing.dll", "lib/net7.0/System.IO.Hashing.xml", "lib/netstandard2.0/System.IO.Hashing.dll", "lib/netstandard2.0/System.IO.Hashing.xml", "system.io.hashing.7.0.0.nupkg.sha512", "system.io.hashing.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/5.0.1": {"sha512": "qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "type": "package", "path": "system.io.pipelines/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.1.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Linq.Async/5.1.0": {"sha512": "TkDTElj3vvizUnhS4mj0oZW8kX6KOBcsUkj79w/q6IUI7nsW+bXmWZfixknClug/IA+8vTWcArXSjIxn9hIWxQ==", "type": "package", "path": "system.linq.async/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.Linq.Async.dll", "lib/net461/System.Linq.Async.xml", "lib/netcoreapp3.1/System.Linq.Async.dll", "lib/netcoreapp3.1/System.Linq.Async.xml", "lib/netstandard2.0/System.Linq.Async.dll", "lib/netstandard2.0/System.Linq.Async.xml", "lib/netstandard2.1/System.Linq.Async.dll", "lib/netstandard2.1/System.Linq.Async.xml", "ref/net461/System.Linq.Async.dll", "ref/net461/System.Linq.Async.xml", "ref/netcoreapp3.1/System.Linq.Async.dll", "ref/netcoreapp3.1/System.Linq.Async.xml", "ref/netstandard2.0/System.Linq.Async.dll", "ref/netstandard2.0/System.Linq.Async.xml", "ref/netstandard2.1/System.Linq.Async.dll", "ref/netstandard2.1/System.Linq.Async.xml", "system.linq.async.5.1.0.nupkg.sha512", "system.linq.async.nuspec"]}, "System.Linq.Dynamic.Core/1.3.3": {"sha512": "o+6pt8HrLDamzudt4Zs9nEpjgoVFthzW73c2Izu64OVGJ38fpkebPgWuZ3wl4oaaUhlg8dlJCeiJoKNlKnvH7Q==", "type": "package", "path": "system.linq.dynamic.core/1.3.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/net7.0/System.Linq.Dynamic.Core.dll", "lib/net7.0/System.Linq.Dynamic.Core.pdb", "lib/net7.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "logo.png", "system.linq.dynamic.core.1.3.3.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Private.ServiceModel/4.10.0": {"sha512": "dB4hD50X7FaCCPoMJ+TShvSVXEHWBD/GKEd494N4a3V+avJmNFmKK7bM40J1zsj+QWt66DG2YkwWlRf/OHx8zw==", "type": "package", "path": "system.private.servicemodel/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/System.Private.ServiceModel.dll", "lib/netstandard2.0/System.Private.ServiceModel.pdb", "lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll", "ref/netstandard2.0/_._", "system.private.servicemodel.4.10.0.nupkg.sha512", "system.private.servicemodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.DispatchProxy/4.7.1": {"sha512": "C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "type": "package", "path": "system.reflection.dispatchproxy/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Reflection.DispatchProxy.dll", "lib/net461/System.Reflection.DispatchProxy.xml", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.xml", "lib/netstandard1.3/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Reflection.DispatchProxy.dll", "ref/net461/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/System.Reflection.DispatchProxy.dll", "ref/netstandard1.3/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/de/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/es/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/fr/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/it/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ja/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ko/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ru/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hans/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hant/System.Reflection.DispatchProxy.xml", "ref/netstandard2.0/System.Reflection.DispatchProxy.dll", "ref/netstandard2.0/System.Reflection.DispatchProxy.xml", "ref/uap10.0.16299/System.Reflection.DispatchProxy.dll", "ref/uap10.0.16299/System.Reflection.DispatchProxy.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.DispatchProxy.dll", "runtimes/win-aot/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "runtimes/win/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "system.reflection.dispatchproxy.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit/4.6.0": {"sha512": "qAo4jyXtC9i71iElngX7P2r+zLaiHzxKwf66sc3X91tL5Ks6fnQ1vxL04o7ZSm3sYfLExySL7GN8aTpNYpU1qw==", "type": "package", "path": "system.reflection.emit/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/monoandroid10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinmac20/_._", "ref/monoandroid10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinmac20/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.6.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.6.0": {"sha512": "j/V5HVvxvBQ7uubYD0PptQW2KGsi1Pc2kZ9yfwLixv3ADdjL/4M78KyC5e+ymW612DY8ZE4PFoZmWpoNmN2mqg==", "type": "package", "path": "system.reflection.emit.lightweight/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.6.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.1": {"sha512": "abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "type": "package", "path": "system.runtime/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.1.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/4.7.0": {"sha512": "NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "type": "package", "path": "system.runtime.caching/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard2.0/System.Runtime.Caching.dll", "ref/netstandard2.0/System.Runtime.Caching.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net45/_._", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.4.7.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/6.0.4": {"sha512": "LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "type": "package", "path": "system.security.cryptography.pkcs/6.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.6.0.4.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Duplex/4.8.1": {"sha512": "htdZ/71tGUl+Yj/URS2dzK7v4hRR/o1/pH2Vnse9vlqZnChJTz7dV3Wk6GsHDUOuYzcufyCMuM79W6aHmL9v8w==", "type": "package", "path": "system.servicemodel.duplex/4.8.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Duplex.dll", "lib/net461/System.ServiceModel.Duplex.pdb", "lib/netcore50/System.ServiceModel.Duplex.dll", "lib/netstandard1.3/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Duplex.dll", "ref/netcore50/System.ServiceModel.Duplex.dll", "ref/netstandard1.1/System.ServiceModel.Duplex.dll", "ref/netstandard2.0/System.ServiceModel.Duplex.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.duplex.4.8.1.nupkg.sha512", "system.servicemodel.duplex.nuspec"]}, "System.ServiceModel.Http/4.10.0": {"sha512": "/PbmNSEwTQ7Vizor3F/Zp8bzR6L9YZNGIwGr1Tyc//ZZuAYDhiwiMbNpX3EnPZM63qD2bJmR/FWH9S5Ffp8K6g==", "type": "package", "path": "system.servicemodel.http/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.pdb", "lib/net6.0/System.ServiceModel.Http.dll", "lib/net6.0/System.ServiceModel.Http.pdb", "lib/netcore50/System.ServiceModel.Http.dll", "lib/netstandard1.3/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Http.dll", "ref/net461/System.ServiceModel.Http.dll", "ref/net6.0/System.ServiceModel.Http.dll", "ref/netcore50/System.ServiceModel.Http.dll", "ref/netstandard1.0/System.ServiceModel.Http.dll", "ref/netstandard1.1/System.ServiceModel.Http.dll", "ref/netstandard1.3/System.ServiceModel.Http.dll", "ref/netstandard2.0/System.ServiceModel.Http.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.http.4.10.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetTcp/4.8.1": {"sha512": "2h2Wq1wr/aBj8BwydO8h8AV5HvE7lEhgRyE6sDsftGTOr1rg5us9zxcJGT+WMlJ7pSTPXSTYCOEM/FfqHe0SEQ==", "type": "package", "path": "system.servicemodel.nettcp/4.8.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.pdb", "lib/netcore50/System.ServiceModel.NetTcp.dll", "lib/netstandard1.3/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.NetTcp.dll", "ref/net461/System.ServiceModel.NetTcp.dll", "ref/netcore50/System.ServiceModel.NetTcp.dll", "ref/netstandard1.1/System.ServiceModel.NetTcp.dll", "ref/netstandard1.3/System.ServiceModel.NetTcp.dll", "ref/netstandard2.0/System.ServiceModel.NetTcp.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.nettcp.4.8.1.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/4.10.0": {"sha512": "BtrvvpgU2HolcC0tUf1g+n4Fk5kLhfbIBgRibcGe7TDHXcy6zTfkyXxR88rl2tO4KEPLkJXxWf/HW/LJmsI0Ew==", "type": "package", "path": "system.servicemodel.primitives/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.Primitives.dll", "lib/net6.0/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.dll", "lib/netcore50/System.ServiceModel.Primitives.dll", "lib/netcoreapp3.1/System.ServiceModel.Primitives.dll", "lib/netcoreapp3.1/System.ServiceModel.Primitives.pdb", "lib/netcoreapp3.1/System.ServiceModel.dll", "lib/netstandard1.3/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.pdb", "lib/netstandard2.0/System.ServiceModel.dll", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Primitives.dll", "ref/net461/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.dll", "ref/netcore50/System.ServiceModel.Primitives.dll", "ref/netcoreapp3.1/System.ServiceModel.Primitives.dll", "ref/netcoreapp3.1/System.ServiceModel.dll", "ref/netstandard1.0/System.ServiceModel.Primitives.dll", "ref/netstandard1.1/System.ServiceModel.Primitives.dll", "ref/netstandard1.3/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.primitives.4.10.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.ServiceModel.Security/4.8.1": {"sha512": "vCBnopz067lS+7Tn+dvpWPH9Yn5CYvSMd5StRzqa7cFDX4O7jkChRSoFWeMaNNqJCwQjafphaQn9IViwqufHZQ==", "type": "package", "path": "system.servicemodel.security/4.8.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Security.dll", "lib/net461/System.ServiceModel.Security.pdb", "lib/netcore50/System.ServiceModel.Security.dll", "lib/netstandard1.3/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Security.dll", "ref/netcore50/System.ServiceModel.Security.dll", "ref/netstandard1.0/System.ServiceModel.Security.dll", "ref/netstandard1.1/System.ServiceModel.Security.dll", "ref/netstandard2.0/System.ServiceModel.Security.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.security.4.8.1.nupkg.sha512", "system.servicemodel.security.nuspec"]}, "System.ServiceProcess.ServiceController/6.0.0": {"sha512": "qMBvG8ZFbkXoe0Z5/D7FAAadfPkH2v7vSuh2xsLf3U6jNoejpIdeV18A0htiASsLK1CCAc/p59kaLXlt2yB1gw==", "type": "package", "path": "system.serviceprocess.servicecontroller/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.ServiceProcess.ServiceController.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.ServiceProcess.ServiceController.dll", "lib/net461/System.ServiceProcess.ServiceController.xml", "lib/net6.0/System.ServiceProcess.ServiceController.dll", "lib/net6.0/System.ServiceProcess.ServiceController.xml", "lib/netcoreapp3.1/System.ServiceProcess.ServiceController.dll", "lib/netcoreapp3.1/System.ServiceProcess.ServiceController.xml", "lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net461/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net461/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/netcoreapp3.1/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/netcoreapp3.1/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "system.serviceprocess.servicecontroller.6.0.0.nupkg.sha512", "system.serviceprocess.servicecontroller.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.4": {"sha512": "bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "type": "package", "path": "system.text.json/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.4.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.RegularExpressions/4.3.1": {"sha512": "N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "type": "package", "path": "system.text.regularexpressions/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.1.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading.Channels/6.0.0": {"sha512": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "type": "package", "path": "system.threading.channels/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Threading.Channels.dll", "lib/net461/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/netcoreapp3.1/System.Threading.Channels.dll", "lib/netcoreapp3.1/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.6.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.3": {"sha512": "+MvhNtcvIbqmhANyKu91jQnvIRVSTiaOiFNfKWwXGHG48YAb4I/TyH8spsySiPYla7gKal5ZnF3teJqZAximyQ==", "type": "package", "path": "system.threading.tasks.extensions/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.3.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "Unchase.Swashbuckle.AspNetCore.Extensions/2.6.12": {"sha512": "na7B3cmNK8L8YuxVl/W9YeZn+jrDz9LlmZa1ZcosKtHQYFOWAnPta2njwa91UyeJzMGjdHoIVE74Q1TGxuUHug==", "type": "package", "path": "unchase.swashbuckle.aspnetcore.extensions/2.6.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Unchase.Swashbuckle.AspNetCore.Extensions.dll", "lib/netstandard2.0/Unchase.Swashbuckle.AspNetCore.Extensions.xml", "unchase.swashbuckle.aspnetcore.extensions.2.6.12.nupkg.sha512", "unchase.swashbuckle.aspnetcore.extensions.nuspec"]}, "XH.LAB.UTILS/6.25.301.13": {"sha512": "R2ssTspbs6+2aqGu60cExG8TgBNJDGHQofa6AVSbQ6zHyyLI2Y7Eqz1rXigzhlxdKbp/p653UMQxBv6rIMvxFQ==", "type": "package", "path": "xh.lab.utils/6.25.301.13", "files": [".nupkg.metadata", ".signature.p7s", "VersionPuhlish.md", "lib/net6.0/XH.LAB.UTILS.dll", "xh.lab.utils.6.25.301.13.nupkg.sha512", "xh.lab.utils.nuspec"]}, "Xinghe.Utility/6.25.206": {"sha512": "cr7vLHdBO+CSgscmucSFNLA/g5njDkHT/SFCb9Z8j/hpVbtUzafR85vCCrZK0AEh8vfXTEurDZocTTJvoOYaQA==", "type": "package", "path": "xinghe.utility/6.25.206", "files": [".nupkg.metadata", ".signature.p7s", "content/info.html", "contentFiles/any/net6.0/info.html", "icon.png", "lib/net6.0/Xinghe.Utility.dll", "lib/net6/ClickHouse.Client.dll", "xinghe.utility.6.25.206.nupkg.sha512", "xinghe.utility.nuspec"]}, "Yarp.ReverseProxy/2.1.0": {"sha512": "VgRuCBxmh5ND4VuFhvIN3AAeoxFhYkS2hNINk6AVCrOVTlpk7OwdrTXi8NHACfqfhDL+/oYCZrF9RxN+yiYnEg==", "type": "package", "path": "yarp.reverseproxy/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Yarp.ReverseProxy.dll", "lib/net6.0/Yarp.ReverseProxy.xml", "lib/net7.0/Yarp.ReverseProxy.dll", "lib/net7.0/Yarp.ReverseProxy.xml", "lib/net8.0/Yarp.ReverseProxy.dll", "lib/net8.0/Yarp.ReverseProxy.xml", "yarp.reverseproxy.2.1.0.nupkg.sha512", "yarp.reverseproxy.nuspec"]}, "XH.H82.Models/1.0.0": {"type": "project", "path": "../XH.H82.Models/XH.H82.Models.csproj", "msbuildProject": "../XH.H82.Models/XH.H82.Models.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["EPPlus >= 6.2.6", "EasyCaching.Serialization.Protobuf >= 1.7.0", "FireflySoft.RateLimit.AspNetCore >= 3.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer >= 6.0.15", "RestSharp >= 112.1.0", "Serilog.Sinks.FastConsole >= 2.2.0", "Serilog.Sinks.SpectreConsole >= 0.3.3", "SkiaSharp >= 2.88.9", "Swashbuckle.AspNetCore >= 6.2.3", "Swashbuckle.AspNetCore.Filters >= 7.0.5", "Swashbuckle.AspNetCore.Filters.Abstractions >= 7.0.5", "Unchase.Swashbuckle.AspNetCore.Extensions >= 2.6.12", "XH.H82.Models >= 1.0.0", "iTextSharp >= 5.5.13.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj", "projectName": "XH.H82.Base", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "EasyCaching.Serialization.Protobuf": {"target": "Package", "version": "[1.7.0, )"}, "FireflySoft.RateLimit.AspNetCore": {"target": "Package", "version": "[3.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.15, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Serilog.Sinks.FastConsole": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Sinks.SpectreConsole": {"target": "Package", "version": "[0.3.3, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.9, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.2.3, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[7.0.5, )"}, "Swashbuckle.AspNetCore.Filters.Abstractions": {"target": "Package", "version": "[7.0.5, )"}, "Unchase.Swashbuckle.AspNetCore.Extensions": {"target": "Package", "version": "[2.6.12, )"}, "iTextSharp": {"target": "Package", "version": "[5.5.13.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'BouncyCastle 1.8.9' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0'. This package may not be fully compatible with your project.", "libraryId": "BouncyCastle", "targetGraphs": ["net6.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'iTextSharp 5.5.13.3' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0'. This package may not be fully compatible with your project.", "libraryId": "iTextSharp", "targetGraphs": ["net6.0"]}]}