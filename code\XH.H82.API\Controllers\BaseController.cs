﻿using System.ComponentModel.DataAnnotations;
using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using RestSharp;
using XH.H82.API.Extensions;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.Entities;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class BaseController : ControllerBase
    {
        private readonly IFileManageService _fileManageService;
        private readonly IBaseService _baseService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _cacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        private readonly string ModuleId;
        //upload路径
        string uploadPath = Path.Combine(AppContext.BaseDirectory, "uploads");
        private readonly string FileSavePath = "";

        private readonly RestClient _clientS13;
        public BaseController(IBaseService baseService, IModuleLabGroupService imodulelabgroupservice, IMemoryCache cache
            , IConfiguration configuration, IHostingEnvironment hostingEnvironment,  IFileManageService fileManageService, IMemoryCache cacheMemory)
        {
            _baseService = baseService;
            _IModuleLabGroupService = imodulelabgroupservice;
            _hostingEnvironment = hostingEnvironment;
            _fileManageService = fileManageService;
            _cacheMemory = cacheMemory;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            ModuleId = _configuration["ModuleId"];
            RedisModule = _configuration["RedisModule"];
            //FileSavePath = Path.Combine(AppContext.BaseDirectory, configuration["ConnectionStrings:FileSavePath"]);

            var addressS13 = configuration["S16"];
            if (addressS13.IsNotNullOrEmpty())
            {
                _clientS13 = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (Sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressS13),
                    ThrowOnAnyError = true
                });
            }
        }
        

        /// <summary>
        /// 获取用户科室列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUserLabList()
        {
            var claims = User.ToClaimsDto();
            var res = _baseService.GetUserLabList(claims.USER_NO);
            return Ok(res);
        }

        /// <summary>
        /// 获取科室ID
        /// </summary>
        /// <param name="labId">科室ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetGlobalLabId([BindRequired] string labId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            _cacheMemory.Set(currentLabKey, labId, TimeSpan.FromDays(1));
            return Ok(labId.ToResultDto());
        }
        /// <summary>
        /// 专业组树结构
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetMgroupList(string areaId, string? pGroupId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _cacheMemory.Get<string>(currentLabKey);
            var res = _baseService.GetMgroupList(claims.USER_NO, claims.HOSPITAL_ID, labId, areaId, pGroupId);
            return Ok(res);
        }

        /// <summary>
        /// 获取院区下拉
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetAreaPullList(string? labId)
        {
            if (labId.IsNullOrEmpty())
            {
                var claims = User.ToClaimsDto();
                var clientMac = GetLocalMac.GetMac();
                currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
                var macLabId = _cacheMemory.Get<string>(currentLabKey);
                var res = _baseService.GetAreaPullList(claims.USER_NO, macLabId, claims.HOSPITAL_ID);
                return Ok(res);
            }
            else
            {
                var claims = User.ToClaimsDto();
                var res = _baseService.GetAreaPullList(claims.USER_NO, labId, claims.HOSPITAL_ID);
                return Ok(res);

            }

        }

        /// <summary>
        ///   获取附件
        /// </summary>
        /// <param name="DOC_CLASS">附件分类（如：申购信息）</param>
        /// <param name="DOC_INFO_ID">附件关联ID（如：申购ID）</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEnclosureInfo([BindRequired] string DOC_CLASS, string DOC_INFO_ID)
        {
            if (DOC_CLASS == "SOP档案")
            {
                return Ok(_fileManageService.GetEquipmentSopFils(DOC_INFO_ID).ToResultDto());
            }
            else
            {
                return Ok(_baseService.GetEnclosureInfo(DOC_CLASS, DOC_INFO_ID, file_preview_address).ToResultDto());
            }
        }


        /// <summary>
        ///   删除附件信息
        /// </summary>
        /// <param name="doc_id"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteEnclosure([BindRequired] string doc_id)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var resultRow = _baseService.DeleteEnclosureInfo(doc_id, userName);
            return Ok(resultRow);
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadEnclosure([FromForm] UploadFileDto uploadFiles)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var resultRow = _baseService.UploadEnclosureInfo(uploadFiles, userName, claims.HOSPITAL_ID);
            return Ok(new ResultDto { success = resultRow.success, msg = resultRow.msg });
        }


        ///// <summary>
        ///// 上传附件
        ///// </summary>
        ///// <param name="obj">{"DOC_CLASS":"如：申购信息","DOC_INFO_ID":"如：申购ID","DOC_NAME":"文件名称","DOC_SUFFIX":"文件后缀","FILEBASE64":"文件Base64"}</param>
        ///// <returns></returns>
        //[HttpPost]
        //public IActionResult UploadEnclosure(dynamic obj)
        //{
        //    var claims = this.User.ToClaimsDto();
        //    dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
        //    EMS_DOC_INFO doc_info = new EMS_DOC_INFO();
        //    doc_info.DOC_CLASS = json["DOC_CLASS"].ToString();
        //    doc_info.DOC_INFO_ID = json["DOC_INFO_ID"].ToString();
        //    doc_info.DOC_NAME = json["DOC_NAME"].ToString();
        //    doc_info.DOC_SUFFIX = json["DOC_SUFFIX"].ToString();
        //    doc_info.FILEBASE64 = json["FILEBASE64"].ToString().Split(',')[1];
        //    doc_info.HOSPITAL_ID = claims.HOSPITAL_ID;
        //    doc_info.FIRST_RPERSON = claims.HIS_NAME;
        //    doc_info.FIRST_RTIME = DateTime.Now;
        //    doc_info.LAST_MPERSON = claims.HIS_NAME;
        //    doc_info.LAST_MTIME = DateTime.Now;
        //    var resultRow = _baseService.UploadEnclosureInfo(doc_info, file_upload_address);
        //    //var client = new HttpClient();
        //    //client.Dispose();
        //    return Ok(new ResultDto { success = resultRow.success, msg = resultRow.msg });
        //}

        /// <summary>
        ///   修改附件信息
        /// </summary>
        /// <param name="doc_id"></param>
        /// <param name="doc_name"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EditEnclosureInfo([BindRequired] string doc_id, string doc_name, string remark)
        {
            var claims = this.User.ToClaimsDto();

            EMS_DOC_INFO doc_file = new EMS_DOC_INFO();
            doc_file.DOC_ID = doc_id;
            doc_file.DOC_NAME = doc_name;
            doc_file.REMARK = remark;
            doc_file.LAST_MPERSON = claims.HIS_NAME;
            doc_file.LAST_MTIME = DateTime.Now;
            var res = _baseService.EditEnclosureInfo(doc_file);
            return Ok(res);
        }

        /// <summary>
        ///   批量修改附件信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchEditEnclosureInfo([FromBody] List<EMS_DOC_INFO> record)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _baseService.BatchEditEnclosureInfo(record, userName);
            return Ok(res);
        }

        /// <summary>
        ///  复制操作批量添加附件信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchInsertEnclosureInfo([FromBody] List<EMS_DOC_INFO> record)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _baseService.BatchInsetEnclosureInfo(record, userName);
            return Ok(res);
        }


        /// <summary>
        ///   获取不良事件信息
        /// </summary>
        /// <param name="equipment_id">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetAdverseEventInfo([BindRequired] string equipment_id)
        {
            var claims = this.User.ToClaimsDto();
            string file_preview_address = "https://" + _IModuleLabGroupService.GetModuleInfoByModuleId(claims.HOSPITAL_ID, "S54") + "/";
            ResultDto Result = _baseService.GetAdverseEventInfo(equipment_id, file_preview_address);
            return Ok(Result);
        }

        /// <summary>
        /// 获取菜单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetMenuInfo()
        {
            var claims = User.ToClaimsDto();
            var res = _baseService.GetMenuInfo( claims.HOSPITAL_ID, ModuleId,claims.USER_NO);
            //var res = _baseService.GetMenuInfoByPermission();
            return Ok(res);
        }

        /// <summary>
        /// 获取表格表单设置
        /// </summary>
        /// <param name="menuId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetFuncDictInfo(string menuId)
        {
            var claims = User.ToClaimsDto();
            var res = _baseService.GetFuncDictInfo(menuId, ModuleId, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取供应商类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCompanyClassList()
        {
            var res = _baseService.GetCompanyClassList();
            return Ok(res.ToResultDto());
        }



        /// <summary>
        /// 上传暂存区文件
        /// </summary>
        /// <param name="uploadFile"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadStagingAreaFile([FromForm] UploadStagingAreaFileDto uploadFile)
        {
            _baseService.AddStagingAreaFiles(uploadFile);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 查询暂存区文件
        /// </summary>
        /// <param name="docClass"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<EMS_DOC_INFO>))]
        public IActionResult GetStagingAreaFiles([Required] string docClass)
        {
            var result = _baseService.GetStagingAreaFiles(docClass);
            return Ok(result.ToResultDto());
        }
        /// <summary>
        /// 使用暂存区文件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UseStagingAreaFile([FromBody] AreaFileDto input)
        {
            if (input.docId.IsNullOrEmpty() || input.docInfoId.IsNullOrEmpty())
            {
                var error = "";
                if (input.docId.IsNullOrEmpty())
                {
                    error += "docId不能为空；";
                }
                if (input.docInfoId.IsNullOrEmpty())
                {
                    error += "docInfoId为记录ID，不能为空";
                }
                throw new BizException($"{error}");
            }
            _baseService.UseStagingAreaFile(input.docId, input.docInfoId);
            return Ok(true.ToResultDto());
        }
        /// <summary>
        /// 已使用文件回归暂存区
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        [HttpPut("{docId}")]
        public IActionResult UnUserStagingAreaFile([Required] string docId)
        {
            _baseService.UnUseStagingAreaFile(docId);
            return Ok(true.ToResultDto());
        }

    }
}
