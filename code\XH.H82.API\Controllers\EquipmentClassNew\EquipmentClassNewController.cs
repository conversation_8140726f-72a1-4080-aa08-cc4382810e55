﻿using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.API.Controllers.EquipmentClassNew;

/// <summary>
/// 设备类型新版本
/// </summary>
public class EquipmentClassNewController : SuperController
{
    
    private readonly IEquipmentClassService _equipmentClassNewService;

    public EquipmentClassNewController(IEquipmentClassService equipmentClassNewService) 
    {
        _equipmentClassNewService = equipmentClassNewService;
    }


    /// <summary>
    /// 查询设备类字典树
    /// </summary>
    /// <param name="hasAll">是否需要“全部设备节点” 默认需要</param>
    /// <param name="isEdit">是否编辑</param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetEquipmentClassDictTree(bool hasAll = true , bool isEdit = false)
    {
        var result = _equipmentClassNewService.GetEquipmentClassDictTree(hasAll,isEdit);
        

        return Ok(result.Success());
    }

    

    /// <summary>
    /// 添加细分类设备类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddEquipmentClassDict(AddEquipmentClassDto input)
    {
        var  result = _equipmentClassNewService.AddClassDict(input);
        return Ok(result.Success());
    }


    /// <summary>
    /// 更新细分类设备类型
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("{classId}")]
    public IActionResult UpdateEquipmentClassDict(string classId,AddEquipmentClassDto input)
    {
        var  result = _equipmentClassNewService.UpdateClassDict(classId,input);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 停用/启用设备类型
    /// </summary>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult EnableOrDisableEquipmentClassDict(string classId)
    {
        _equipmentClassNewService.DisableOrEnableClassDict(classId);
        return Ok(true.Success());
    }
    
    
    /// <summary>
    /// 删除设备类型字典
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult DeleteEquipmentCodeCustomDict(string classId)
    {
        _equipmentClassNewService.DeleteClassDict(classId);
        return Ok(true.Success());
    }

    /// <summary>
    /// 查询设备类型字典已关联的档案
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [HttpGet("{classId}")]
    [CustomResponseType(typeof(List<ClassLinkArchivesDto>))]
    public IActionResult GetEquipmentArchivesLinkByClassId(string classId)
    {
        var  archives = _equipmentClassNewService.GetEquipmentArchivesLinkByClassId(classId);
        var  result = archives.Select(x => new ClassLinkArchivesDto(x));
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 查询设备类型字典未关联的档案
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [HttpGet("{classId}")]
    [CustomResponseType(typeof(List<ClassLinkArchivesDto>))]
    public IActionResult GetEquipmentArchivesNotLinkByClassId(string classId)
    {
        var  archives = _equipmentClassNewService.GetEquipmentArchivesNotLinkByClassId(classId);
        var  result = archives.Select(x => new ClassLinkArchivesDto(x));
        return Ok(result.Success());
    }

    
    /// <summary>
    /// 建立关联设备类型和档案记录
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="archivesIds">档案记录ids</param>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult LinkArchivesByClassId(string classId , string[] archivesIds)
    {
        var  result = _equipmentClassNewService.LinkClassAndArchives(classId ,archivesIds);
        return Ok(result.Success());
    }
    
    
    /// <summary>
    /// 取消建立关联设备类型和档案记录
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="archivesIds">档案记录ids</param>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult UnLinkArchivesByClassId(string classId , string[] archivesIds)
    {
        var  result = _equipmentClassNewService.UnLinkClassAndArchives(classId ,archivesIds);
        return Ok(result.Success());
    }
}
