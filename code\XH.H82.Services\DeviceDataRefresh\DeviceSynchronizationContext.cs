﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Serilog;
using System.Security.Cryptography;
using System.Text;
using XH.H82.IServices;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;
using static iTextSharp.text.pdf.AcroFields;

namespace XH.H82.Services.DeviceDataRefresh
{


    public class DeviceSynchronizationContext
    {
        private IBaseDataServices _basedataService { get; set; }

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        EquipmentContext equipmentContext;

        private string _dateSeed { get; set; } = Guid.NewGuid().ToString();

        public DeviceSynchronizationContext(ISqlSugarUow<SugarDbContext_Master> dbContext)
        {
            _dbContext = dbContext;
            equipmentContext = new EquipmentContext(_dbContext);
        }

        public void SetSeed(string seed)
        {
            _dateSeed = seed;
        }
        
        public void SetBaseService(IBaseDataServices  services)
        {
            _basedataService = services;
        }

        public void Synchronization(string labId, string hospitalId)
        {
            
            //检测仪器同步
            var equipment_insert = new List<EMS_EQUIPMENT_INFO>();
            var equipment_update = new List<EMS_EQUIPMENT_INFO>();
            var workPlan_insert = new List<EMS_WORK_PLAN>();
            //检测仪器列表
            var instruments = _dbContext.Db.Queryable<LIS6_INSTRUMENT_INFO>()
                .Where(a => a.INSTRUMENT_STATE == "1" && a.LAB_ID == labId)
                .ToList();
            var instrumentsModels = _dbContext.Db.Queryable<LIS6_SINSTRUMENT_INFO>().ToList();
            
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().ToList();
            
            var pipeliningList = _dbContext.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            
            //检验单元
            var groupInfo = _dbContext.Db.Queryable<LIS6_INSPECTION_GROUP>().ToList();
            
            //基础数据
            var baseData = _dbContext.Db.Queryable<SYS6_BASE_DATA>().Where(p => p.DATA_STATE == "1" && (p.CLASS_ID == "设备分类" || p.CLASS_ID == "设备类型")).ToList();
            
            var equipemntClass = baseData.Where(p => p.DATA_CNAME == "常规检测设备" && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_ID;
            
            foreach (var instrument in instruments)
            {
                //若系统管理配置了设备型号的自定义型号则取自定义型号 
                var model = instrumentsModels.Where(x => x.SINSTRUMENT_ID == instrument.SINSTRUMENT_ID).FirstOrDefault();
                var groupId = groupInfo.Where(p => p.GROUP_ID == instrument.GROUP_ID).FirstOrDefault()?.PGROUP_ID; 
                if (model is null)
                {
                    model = new();
                }
                
                if (instrument.EQUIPMENT_ID.IsNullOrEmpty() & instrument.EQUIPMENT_ID == "0")
                {
                    var id = GetEquipmentId("");
                    var conut = 0;
                    while (equipments.Count(x => x.EQUIPMENT_ID == id) > 1)
                    {
                        id = GetEquipmentId(id, conut++);
                    }
                    if (equipemntClass != null)
                    {
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        
                        equipment_insert.Add(new EMS_EQUIPMENT_INFO
                        {
                            EQUIPMENT_ID = id,
                            HOSPITAL_ID = instrument.HOSPITAL_ID,
                            UNIT_ID = groupId,
                            LAB_ID = instrument.LAB_ID,
                            EQUIPMENT_CLASS = equipemntClass,
                            INSTRUMENT_ID = instrument.INSTRUMENT_ID,
                            EQUIPMENT_NAME = instrument.INSTRUMENT_NAME,
                            EQUIPMENT_MODEL = model.SINSTRUMENT_MODE,
                            EQUIPMENT_NUM = instrument.INSTRUMENT_SNUM,
                            EQUIPMENT_CODE = instrument.INSTRUMENT_CNAME,
                            EQUIPMENT_TYPE = instrument.INSTRUMENT_TYPE,
                            MANUFACTURER_ID = instrument.INSTRUMENT_FACTORY,
                            EQUIPMENT_STATE = instrument.INSTRUMENT_STATE ?? "1",
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now,
                            EQUIPMENT_UCODE = instrument.INSTRUMENT_CNAME,
                        });
                        workPlan_insert.Add(new EMS_WORK_PLAN
                        {
                            WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString(),
                            WORK_PLAN_STATE = "1",
                            HOSPITAL_ID = instrument.HOSPITAL_ID,
                            EQUIPMENT_ID = id,
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now

                        });
                        _dbContext.Db.Updateable<LIS6_INSTRUMENT_INFO>().SetColumns(p => new LIS6_INSTRUMENT_INFO
                        {
                            EQUIPMENT_ID = id
                        }).Where(p => p.INSTRUMENT_ID == instrument.INSTRUMENT_ID).ExecuteCommand();
                    }
                }
                else
                {
                    var equipmentUpdate = equipments.Where(p => p.EQUIPMENT_ID == instrument.EQUIPMENT_ID).FirstOrDefault();
                    if (equipmentUpdate is not null)
                    {
                        var pipeId = pipeliningList.Where(p => p.INSTRUMENT_ID == equipmentUpdate.INSTRUMENT_ID).FirstOrDefault()?.PIPELINING_ID;
                        if (pipeId.IsNullOrEmpty())
                        {
                            equipmentUpdate.VEST_PIPELINE = null;
                        }
                        else
                        {
                            var vestPipeId = equipments.Where(p => p.INSTRUMENT_ID == pipeId).FirstOrDefault()?.EQUIPMENT_ID;
                            if (vestPipeId.IsNotNullOrEmpty())
                            {
                                equipmentUpdate.VEST_PIPELINE = vestPipeId;
                            }
                        }
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        equipmentUpdate.UNIT_ID = groupId;
                        equipmentUpdate.LAB_ID = instrument.LAB_ID;
                        equipmentUpdate.EQUIPMENT_MODEL = model.SINSTRUMENT_MODE;
                        equipmentUpdate.EQUIPMENT_NUM = instrument.INSTRUMENT_SNUM;
                        equipmentUpdate.EQUIPMENT_CODE = instrument.INSTRUMENT_CNAME;
                        equipmentUpdate.EQUIPMENT_TYPE = instrument.INSTRUMENT_TYPE;
                        equipmentUpdate.EQUIPMENT_CLASS = equipemntClass;
                        equipmentUpdate.LAST_MTIME = DateTime.Now;
                        equipmentUpdate.EQUIPMENT_UCODE = equipmentUpdate.EQUIPMENT_UCODE.IsNullOrEmpty()
                            ? equipmentUpdate.EQUIPMENT_CODE
                            : equipmentUpdate.EQUIPMENT_UCODE;
                        equipment_update.Add(equipmentUpdate);
                    }
                }
            }

            _dbContext.Db.Insertable(equipment_insert).ExecuteCommand();
            _dbContext.Db.Insertable(workPlan_insert).ExecuteCommand();
            _dbContext.Db.Updateable(equipment_update).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
        }
        public string GetEquipmentId(string id = "", int number = 0)
        {
            _dateSeed = Guid.NewGuid().ToString();
            if (number > 3)
            {
                return GenerateRandom(_dateSeed);
            }

            if (_basedataService is null)
            {
                return GenerateRandom(_dateSeed);
            }

            if (id.IsNullOrEmpty())
            {
                return _basedataService.GetTableMax("EMS_EQUIPMENT_INFO", "EQUIPMENT_ID", 1, 1).data.ToString();
            }
            else
            {
                id = _basedataService.GetTableMax("EMS_EQUIPMENT_INFO", "EQUIPMENT_ID", 1, 1).data.ToString();
                return id;
            }
        }
        public int GenerateRandomNumber(string seed)
        {
            // 计算种子的哈希值
            byte[] seedBytes = Encoding.UTF8.GetBytes(seed);
            byte[] hashBytes;
            using (SHA256 sha256 = SHA256.Create())
            {
                hashBytes = sha256.ComputeHash(seedBytes);
            }
            // 将哈希值转换为整数
            int result = 0;
            for (int i = 0; i < 5; i++)
            {
                result = result * 10 + (hashBytes[i] % 10); // 取哈希值中的部分字节作为随机数的每一位
            }
            return result;
        }
        public string GenerateRandom(string seed)
        {
            var seedInt = GenerateRandomNumber(seed);
            Random random = new Random(seedInt); // 使用不同的种子值生成不同的随机数序列
            long result = 0;
            for (int i = 0; i < 18; i++)
            {
                result = result * 10 + random.Next(0, 10); // 生成每一位随机数
            }
            return $"{result}";
        }


        private bool NeedSynchronizationUnitId(string id)
        {
            const string KEY = "H0701003";
            var result = false;

            var SetUpValue1 = _dbContext.Db.Queryable<SYS6_SETUP>().Where(x => x.UNIT_ID == id && x.SETUP_NO == KEY).First();
            var SetUpValue2 = _dbContext.Db.Queryable<SYS6_SETUP_DICT>().Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1").First();
            if (SetUpValue1 is not null)
            {
                result = SetUpValue1.SETUP_VALUE == "1";
            }
            if (SetUpValue2 is not null)
            {
                result = SetUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }
    }
}
