using H.BASE.SqlSugarInfra.Uow;
using XH.H82.API.Extensions;
using XH.H82.Models.SugarDbContext;

internal static class DateCheckContextHelpers
{

    public static void UseDateCheck(this IApplicationBuilder app)
    {
        if (app == null)
        {
            throw new ArgumentNullException("app");
        }
        var dbcontext = app.ApplicationServices.GetService<ISqlSugarUow<SugarDbContext_Master>>();
        
        
        
        if (dbcontext is null)
        {
            return;
        }
        var dateCheckContext = new DateCheckContext(dbcontext);
        dateCheckContext.CheckEquipments();
    }
}