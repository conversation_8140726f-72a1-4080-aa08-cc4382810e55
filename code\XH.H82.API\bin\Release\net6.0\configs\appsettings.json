{
  //公共基础配置服务地址
  "UrlModuleS01": "https://localhost:18801",
  //"UrlModuleS01": "https://************:18801",
  //"UrlModuleS01": "https://*************:18801", // 外网SQLSERVER
  //"UrlModuleS01": "https://************:18801", // 118人大金仓
  //注意确保模块ID为你当前项目的模块ID
  "ModuleId": "H82",
  "ReportService": "http://localhost:16702/",
  //端口信息
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://*:18482",
        "Certificate": {
          "Path": "xhdevcert.pfx",
          "Password": "7540000E"
        },
        //增加.只支持tsl1.2及tsl1.3
        "SslProtocols": [ "Tls12", "Tls13" ],
        //增加 只支持以下加密套件
        "CipherSuites": "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
      }
    }
  },
  
  
  "RedisModule": "S02",
  //是否开启Swagger 开发环境自动开启,生产环境根据此项配置开启,(按等保要求,生产环境不允许暴露接口信息),除调试需求外,此项务必保持关
  "OpenSwagger": "0",
  "AllowedHosts": "*",
  //默认启动线程数很少,高并发场景时每0.5秒增加一个线程,导致压力测试会有很高的错误率
  //为了增加高并发场景性能,建设设大 最小启动线程数(酌情)
  "MinThreads": 1000,
  //本地日志文件保留时间(天) 0=无限期不清理 默认清理15天
  "LogKeepDays": "15",
  //是否禁用拦截器缓存,仅在调试时使用
  "DisableCacheAOP": "0",
  //全局禁用角色权限验证(仅保留基本token验证),即关闭垂直越权管理
  "GlobalDisableRoleAuthorize": "1",
  //全局禁用token是否存在验证(不判断token是否在token池里是否存在)
  "GlobalDisableTokenValidate": "0",
  "DisableRateLimit": "0",
  //IP限流配置
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": false,
    "StackBlockedRequests": false,
    //请注意此场景,被调方位于WAF防火墙下,调用方位于WAF外,X-Real-IP 可能指向WAF地址,需要改成X-Forward-IP
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    //白名单 
    //"IpWhitelist": [ "127.0.0.1", "::1/10", "***********/24" ],
    "EndpointWhitelist": [],
    "ClientWhitelist": [],
    "GeneralRules": [
      {
        "Endpoint": "*",
        "Period": "1s",
        //注意,公共服务,ip来源可能来自某单个服务器的项目,请谨慎设置限流值,或设置白名单或关闭限流
        "Limit": 100
      }
    ]
  },
  "IsSmbl": "0",
  //0关闭自动刷新墨水屏服务
  //1开始自动刷新墨水屏服务
  "IsAutoRefresh": "0",
  //生安监测服务
  "IsUseSmblSync": 0,
  //电信采集监控数据时间间隔
  "IoTSyncTime": 15,
  //所需软件模块,用','号分开
  "ExtraRegistrationModules": "J02,S16,H57-03,H91,S54,H115,H92,H91-15,H57-02,H88,H120,H05",
  
  //省人电信生安眼相关url
  //"IoTHttp": "http://*************:7070"
  //"IoTHttp":"http://************",
  //访问导出设备维护详情excel文件IP白名单
  "AllowedIps": [
    "0.0.0.0"
  ]
}
