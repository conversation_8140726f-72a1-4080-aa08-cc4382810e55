﻿using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text;
using Dm;
using Elastic.Clients.Elasticsearch;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using Org.BouncyCastle.Utilities;
using RestSharp;
using Spire.Doc;
using Spire.Doc.License;
using Spire.Pdf.General.Paper.Uof;
using SqlSugar;
using XH.H82.Base.Helper;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class BaseService : IBaseService
    {
        private readonly IBaseDataServices _baseDataServices;
        private readonly IConfiguration _configuration;
        private readonly RestClient _clientH04;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IAuthorityService2 _authorityService;

        private readonly ILogger<BaseService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public BaseService(IBaseDataServices baseDataServices, IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IAuthorityService2 authorityService, ILogger<BaseService> logger)
        {
            _baseDataServices = baseDataServices;
            _configuration = configuration;
            _httpContext = httpContext;
            var addressH04 = _configuration["H04-13"];

            _dbContext = dbContext;
            _dbContext.SetCreateTimeAndCreatePersonData(_httpContext.HttpContext.User.ToClaimsDto());
            _authorityService = authorityService;
            _logger = logger;

            if (addressH04.IsNotNullOrEmpty())
            {
                _clientH04 = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (Sender, certificate, chain, SslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH04),
                    ThrowOnAnyError = true
                });
            }
            //  ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }
        public ResultDto GetUserLabList(string userNo)
        {
            var res = _authorityService.GetLogonLabList(_dbContext, AppSettingsProvider.CurrModuleId);
            return new ResultDto { success = true, data = res.DistinctBy(i => i.LAB_ID).ToList() };
        }


        public List<NewOATreeDto> GetAllHospitalMgroups(string userNo, string labId)
        {
            var authContext = new AuthorityContext(_dbContext, _authorityService);
            var user = _httpContext.HttpContext.User.ToClaimsDto();

            authContext.SetUser(user,labId);

            var groups = authContext.GetAccessibleProfessionalGroups(labId, null);
            var areaIds = groups.Select(x => x.AREA_ID).Distinct();
            var areas = _dbContext.Db.Queryable<SYS6_INSPECTION_AREA>()
                .Where(x => areaIds.Contains(x.AREA_ID))
                .Where(p => p.STATE_FLAG == "1")
                .ToList();
            if (areas.Count() == 0)
            {
                throw new BizException("未找到该院区信息");
            }

            var result = new List<NewOATreeDto>();

            foreach (var area in areas)
            {
                List<string> MGroupIds = new List<string>();
                foreach (var group in groups)
                {
                    if (group.AREA_ID == area.AREA_ID)
                    {
                        if (group.MGROUP_ID.IsNotNullOrEmpty())
                        {
                            MGroupIds.Add(group.MGROUP_ID);
                        }
                    }
                }
                MGroupIds = MGroupIds.Distinct().ToList();

                var MGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                        .Where(p => MGroupIds.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                        .ToList();

                var oaTree = new NewOATreeDto();
                oaTree.AREA_ID = area.AREA_ID;
                oaTree.AREA_NAME = area.AREA_NAME;
                oaTree.SecondStageTree = new List<NewMgroupTreeDto>();
                foreach (var MGroup in MGroups)
                {
                    //三级树
                    var thirdStage = new List<NewPgroupTreeDto>();
                    var pgroups = groups.Where(p => p.MGROUP_ID == MGroup.MGROUP_ID && p.AREA_ID == area.AREA_ID).ToList();
                    foreach (var pgroup in pgroups)
                    {
                        thirdStage.Add(new NewPgroupTreeDto
                        {
                            PGROUP_ID = pgroup.PGROUP_ID,
                            PGROUP_NAME = pgroup.PGROUP_NAME,
                        });
                    }
                    oaTree.SecondStageTree.Add(new NewMgroupTreeDto
                    {
                        MGROUP_ID = MGroup.MGROUP_ID,
                        MGROUP_NAME = MGroup.MGROUP_NAME,
                        ThirdStageTree = thirdStage
                    });
                }
                result.Add(oaTree);
            }
            return result;
        }


        public ResultDto GetMgroupList(string userNo, string hospitalId, string labId, string areaId, string? pGroupId)
        {
            var authorityContext = new AuthorityContext(_dbContext, _authorityService);
            authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(), labId, areaId);
            var result = new ResultDto();
            try
            {
                var groupList = authorityContext.GetAccessibleProfessionalGroups(labId, areaId).WhereIF(pGroupId.IsNotNullOrEmpty(), x => x.PGROUP_ID == pGroupId).ToList();
                List<string> arrPgroupList = new List<string>();
                groupList.ForEach(p =>
                {
                    if (p.MGROUP_ID.IsNotNullOrEmpty())
                    {
                        arrPgroupList.Add(p.MGROUP_ID);
                    }
                });
                var mgroupList = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                    .Where(p => arrPgroupList.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                    .ToList();
                arrPgroupList = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                    .Where(p => arrPgroupList.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                    .Select(i => i.MGROUP_ID)
                    .ToList();
                var oaTree = new NewOATreeDto();


                //一级树
                var areaInfo = _dbContext.Db.Queryable<SYS6_INSPECTION_AREA>()
                    .OrderBy(p=>p.AREA_ID)
                    .WhereIF(areaId.IsNotNullOrEmpty(), p => p.AREA_ID == areaId)
                    .First();
                if (areaInfo == null)
                {
                    return new ResultDto { success = false, msg = "未找到该院区信息" };
                }
                oaTree.AREA_ID = areaInfo.AREA_ID;
                oaTree.AREA_NAME = areaInfo.AREA_NAME;
                //当前院区下所有申购信息
                var subscribeList = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                    .InnerJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.MGROUP_ID == b.PGROUP_ID)
                    .Where((a, b) => groupList.Select(i => i.PGROUP_ID).Contains(b.PGROUP_ID))
                    .Select((a, b) => new
                    {
                        MGROUP_ID = b.MGROUP_ID,
                        PGROUP_ID = a.MGROUP_ID
                    }).ToList();
                oaTree.TOTAL_SUBSCRIBE_AMOUNT = subscribeList.Count();
                //当前院区下所有设备信息
                var equipmentList = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .InnerJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.UNIT_ID == b.PGROUP_ID)
                    .Where((a, b) => groupList.Select(i => i.PGROUP_ID).Contains(b.PGROUP_ID))
                    .Select((a, b) => new
                    {
                        MGROUP_ID = b.MGROUP_ID,
                        PGROUP_ID = a.UNIT_ID
                    }).ToList();
                oaTree.TOTAL_EQUIPMENT_AMOUNT = equipmentList.Count();
                //当前院区下所有报废停用信息
                var scrapStopList = _dbContext.Db.Queryable<EMS_SCRAP_INFO>()
                    .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                    .InnerJoin<SYS6_INSPECTION_PGROUP>((a, b, c) => b.UNIT_ID == c.PGROUP_ID && a.SCRAP_STATE == "1")
                    .Where((a, b, c) => groupList.Select(i => i.PGROUP_ID).Contains(c.PGROUP_ID))
                    .Select((a, b, c) => new
                    {
                        APPLY_STATE = a.APPLY_STATE,
                        OPER_PERSON_ID = a.OPER_PERSON_ID,
                        EXAMINE_PERSON_ID = a.EXAMINE_PERSON_ID,
                        APPROVE_PERSON_ID = a.APPROVE_PERSON_ID,
                        UNIT_ID = b.UNIT_ID,
                        MGROUP_ID = c.MGROUP_ID,
                    }).ToList();
                oaTree.TOTAL_SCRAP_AMOUNT = scrapStopList.Count();
                //本人待处理
                var q = new List<ScrapStopListDto>();
                scrapStopList.ForEach(item =>
                {
                    var dealPersonId = "";
                    if (item.APPLY_STATE == "0" || item.APPLY_STATE == "4" || item.APPLY_STATE == "5")
                    {
                        dealPersonId = item.OPER_PERSON_ID;
                    }
                    if (item.APPLY_STATE == "1")
                    {
                        dealPersonId = item.EXAMINE_PERSON_ID;
                    }
                    if (item.APPLY_STATE == "2")
                    {
                        dealPersonId = item.APPROVE_PERSON_ID;
                    }
                    q.Add(new ScrapStopListDto
                    {
                        MGROUP_ID = item.UNIT_ID,
                        DEAL_PERSON_ID = dealPersonId,
                        STATE = item.APPLY_STATE
                    });
                });
                var userPgroup = _dbContext.Db.Queryable<SYS6_USER>().Where(p => p.USER_NO == userNo).First().DEPT_CODE;
                var res = q.ToList();
                res.ForEach(item =>
                {
                    if (item.DEAL_PERSON_ID == userNo)
                    {
                        oaTree.SELF_PEND += 1;
                    }
                });
                oaTree.SELF_PGROUP_PEND = res.Where(p => (p.STATE == "0" || p.STATE == "1" || p.STATE == "2") && p.MGROUP_ID == userPgroup).Count();


                //二级树
                var secondStage = new List<NewMgroupTreeDto>();
                mgroupList.ForEach(item =>
                {
                    //三级树
                    var thirdStage = new List<NewPgroupTreeDto>();
                    var pgroupList = groupList.Where(p => p.MGROUP_ID == item.MGROUP_ID).ToList();
                    pgroupList.ForEach(i =>
                    {
                        thirdStage.Add(new NewPgroupTreeDto
                        {
                            PGROUP_ID = i.PGROUP_ID,
                            PGROUP_NAME = i.PGROUP_NAME,
                            PGROUP_SUBSCRIBE_AMOUNT = subscribeList.Where(p => p.PGROUP_ID == i.PGROUP_ID).Count(),
                            PGROUP_EQUIPMENT_AMOUNT = equipmentList.Where(p => p.PGROUP_ID == i.PGROUP_ID).Count(),
                            PGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.UNIT_ID == i.PGROUP_ID).Count()
                        });
                    });
                    secondStage.Add(new NewMgroupTreeDto
                    {
                        MGROUP_ID = item.MGROUP_ID,
                        MGROUP_NAME = item.MGROUP_NAME,
                        MGROUP_SUBSCRIBE_AMOUNT = subscribeList.Where(p => p.MGROUP_ID == item.MGROUP_ID).Count(),
                        MGROUP_EQUIPMENT_AMOUNT = equipmentList.Where(p => p.MGROUP_ID == item.MGROUP_ID).Count(),
                        MGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.MGROUP_ID == item.MGROUP_ID).Count(),
                        ThirdStageTree = thirdStage
                    });
                });
                oaTree.SecondStageTree = secondStage;
                result.data = oaTree;
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "获取树结构失败";
                _logger.LogError("获取树结构失败:\n" + ex.Message);
            }

            return result;
        }

        public List<EMS_DOC_INFO> GetEnclosureInfo(string DOC_CLASS, string DOC_INFO_ID, string file_preview_address)
        {
            var fileQuery = _dbContext.Db.Queryable<EMS_DOC_INFO>()
              .Where(p => p.DOC_CLASS == DOC_CLASS)
              .Where(p => p.DOC_STATE == "1");

            if (DOC_CLASS == "SOP档案" || DOC_CLASS == "设备说明书")
            {
                fileQuery.Where(x => x.EQUIPMENT_ID == DOC_INFO_ID || x.DOC_INFO_ID == DOC_INFO_ID);
            }
            else
            {
                fileQuery.Where(x => x.DOC_INFO_ID == DOC_INFO_ID);
            }
            var result = fileQuery
                .OrderByDescending(i => i.LAST_MTIME != null ? i.LAST_MTIME : i.FIRST_RTIME)
                .ToList();
            if (DOC_CLASS == "SOP档案" || DOC_CLASS == "设备说明书")
            {
                foreach (var item in result)
                {
                    if (item.DOC_INFO_ID is not null)
                    {
                        UpdateFileCaseH91File(item);
                    }
                }
            }
            return result;
        }

        public List<EMS_DOC_INFO> GetEquipmentDocs(string equipmentId, string docInfoId, string docClass)
        {
            var result = new List<EMS_DOC_INFO>();
            var fileQuery = _dbContext.Db.Queryable<EMS_DOC_INFO>()
              .Where(p => p.DOC_CLASS == docClass)
              .Where(p => p.DOC_STATE == "1");

            if (docClass == "SOP档案" || docClass == "设备说明书")
            {
                fileQuery.Where(x => x.EQUIPMENT_ID == equipmentId || x.DOC_INFO_ID == docInfoId);
            }
            else
            {
                fileQuery.Where(x => x.DOC_INFO_ID == equipmentId);
            }
            fileQuery
            .OrderByDescending(i => i.LAST_MTIME != null ? i.LAST_MTIME : i.FIRST_RTIME)
            .ForEach(x => result.Add(x));

            foreach (var item in result)
            {
                if (item.EQUIPMENT_ID is not null)
                {
                    UpdateFileCaseH91File(item);
                }
            }
            return result;

        }

        /// <summary>
        /// 暂存区文件列表
        /// </summary>
        /// <param name="docClass">文件类型</param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetStagingAreaFiles(string docClass)
        {
            var result = _dbContext.Db.Queryable<EMS_DOC_INFO>()
              .Where(p => p.DOC_CLASS == docClass)
              .Where(p => p.DOC_INFO_ID == null)
              .Where(p => p.EQUIPMENT_ID == null)
              .Where(p => p.DOC_STATE == "1")
              .OrderByDescending(i => i.LAST_MTIME != null ? i.LAST_MTIME : i.FIRST_RTIME)
              .ToList();
            return result;
        }

        /// <summary>
        /// 添加暂存区文件
        /// </summary>
        /// <param name="uploadFile"></param>
        public void AddStagingAreaFiles(UploadStagingAreaFileDto uploadFile)
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            const string doc_folder = "EMS/datafile";
            const string if_cover = "true";
            var dateTime = DateTime.Now;
            var guid = Guid.NewGuid().ToString("N");

            var file = new UploadFileDto()
            {

                DOC_CLASS = uploadFile.DOC_CLASS,
                DOC_INFO_ID = null,
                DOC_NAME = uploadFile.DOC_NAME,
                DOC_SUFFIX = uploadFile.DOC_SUFFIX,
                FILE = uploadFile.FILE
            };

            (string uploadFileNmae, string uploadFileType, byte[] uploadFileByte) uploadFileInfo = GetUploadFileInfo(file, guid);
            var docInfo = new EMS_DOC_INFO
            {
                DOC_ID = IDGenHelper.CreateGuid().ToString(),
                HOSPITAL_ID = user.HOSPITAL_ID,
                DOC_CLASS = uploadFile.DOC_CLASS,
                DOC_NAME = uploadFile.DOC_NAME,
                DOC_TYPE = uploadFileInfo.uploadFileType,
                UPLOAD_PERSON = user.HIS_NAME,
                UPLOAD_TIME = dateTime,
                DOC_STATE = "1",
                FIRST_RPERSON = user.HIS_NAME,
                FIRST_RTIME = dateTime,
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = dateTime,
                DOC_PATH = "",
                DOC_SUFFIX = uploadFile.DOC_SUFFIX,
                PDF_PREVIEW_PATH = ""
            };

            docInfo.DOC_PATH = _baseDataServices.UploadToS28(doc_folder, if_cover, uploadFileInfo.uploadFileNmae, uploadFileInfo.uploadFileByte);
            if (uploadFileInfo.uploadFileType != "IMG")
            {
                var bytes = PdfPreview.PDFPreview_Byte(uploadFileInfo.uploadFileByte);
                var doc_preview_name = $"{uploadFile.DOC_NAME}_{guid}_预览.jpg";
                docInfo.PDF_PREVIEW_PATH = _baseDataServices.UploadToS28(doc_folder, if_cover, doc_preview_name, bytes);
            }
            else
            {
                docInfo.PDF_PREVIEW_PATH = docInfo.DOC_PATH;
            }
            _dbContext.Db.Insertable(docInfo).ExecuteCommand();
        }

        /// <summary>
        /// 使用暂存区的文件
        /// </summary>
        /// <param name="docId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="docInfoId"></param>
        public void UseStagingAreaFile(string docId, string docInfoId)
        {
            var fileInfo = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(x => x.DOC_ID == docId).First();
            if (fileInfo is not null)
            {
                fileInfo.DOC_INFO_ID = docInfoId;
                _dbContext.Db.Updateable(fileInfo).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            }
        }

        /// <summary>
        /// 不使用暂存区的文件
        /// </summary>
        /// <param name="docId"></param>
        public void UnUseStagingAreaFile(string docId)
        {
            var fileInfo = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(x => x.DOC_ID == docId).First();
            if (fileInfo is not null)
            {
                fileInfo.EQUIPMENT_ID = "";
                fileInfo.DOC_INFO_ID = "";
                _dbContext.Db.Updateable(fileInfo).ExecuteCommand();
            }
        }

        /// <summary>
        /// 当前文件为dmis表的文件时，则更新当前文件
        /// </summary>
        /// <param name="fileInfo"> Ems的文件</param>
        private void UpdateFileCaseH91File(EMS_DOC_INFO fileInfo)
        {
            var h91Clitne = new H91Client(_configuration["H91"], _httpContext);
            var dmisFile = h91Clitne.GetSysDocFileDropDownByUsing(fileInfo.DOC_INFO_ID);
            if (dmisFile is null)
            {
                _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(x => new EMS_DOC_INFO()
                {
                    DOC_STATE = "2"
                }).Where(x => x.DOC_INFO_ID == fileInfo.DOC_ID).ExecuteCommand();
            }
            else
            {
                var docPath = dmisFile.FILE_PATH.Replace("/H91pdf/api/", "/");
                _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(x => new EMS_DOC_INFO()
                {
                    DOC_NAME = dmisFile.FILE_NAME,
                    DOC_PATH = docPath
                }).Where(x => x.DOC_INFO_ID == fileInfo.DOC_ID).ExecuteCommand();
            }
        }

        public ResultDto EditEnclosureInfo(EMS_DOC_INFO doc_file)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(p => new EMS_DOC_INFO
                {
                    DOC_NAME = doc_file.DOC_NAME,
                    REMARK = doc_file.REMARK,
                    LAST_MPERSON = doc_file.LAST_MPERSON,
                    LAST_MTIME = doc_file.LAST_MTIME,
                }).Where(p => p.DOC_ID == doc_file.DOC_ID).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "修改附件信息失败";
                _logger.LogError("修改附件信息失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto BatchEditEnclosureInfo(List<EMS_DOC_INFO> record, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                record.ForEach(item =>
                {
                    _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(p => new EMS_DOC_INFO
                    {
                        DOC_INFO_ID = item.DOC_INFO_ID,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now
                    }).Where(p => p.DOC_ID == item.DOC_ID).ExecuteCommand();
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "批量修改附件信息失败";
                _logger.LogError("批量修改附件信息失败:\n" + ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 附件批量复制
        /// </summary>
        /// <param name="record"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto BatchInsetEnclosureInfo(List<EMS_DOC_INFO> record, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                record.ForEach(item =>
                {
                    item.DOC_ID = IDGenHelper.CreateGuid().ToString();
                    item.LAST_MPERSON = userName;
                    item.LAST_MTIME = DateTime.Now;
                    _dbContext.Db.Insertable<EMS_DOC_INFO>(item)
                    .ExecuteCommand();
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "批量修改附件信息失败";
                _logger.LogError("批量修改附件信息失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto UploadEnclosureInfo(UploadFileDto uploadFile, string userName, string hospitalId)
        {
            LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");

            const string doc_folder = "EMS/datafile";
            const string if_cover = "true";
            var guid = Guid.NewGuid().ToString("N");

            byte[] bytes = null;
            using (var stream = uploadFile.FILE.OpenReadStream())
            {
                bytes = new byte[stream.Length];
                stream.Read(bytes, 0, bytes.Length);
                stream.Seek(0, SeekOrigin.Begin);
            }

            //过滤特殊字符
            uploadFile.DOC_NAME = uploadFile.DOC_NAME.Replace('+', '_').Replace('@', '_').Replace(' ', '_').Replace(uploadFile.DOC_SUFFIX, "");

            var doc_type = FileTypeUnit.ReturnFileType(uploadFile.DOC_SUFFIX, new List<string>() { ".xlsx", ".xls" });
            var operTime = DateTime.Now;
            var doc_name = $"{uploadFile.DOC_NAME}_{guid}{uploadFile.DOC_SUFFIX}";

            if (uploadFile.DOC_SUFFIX == ".docx" || uploadFile.DOC_SUFFIX == ".doc")
            {

                if (uploadFile.DOC_SUFFIX == ".docx")
                {
                    using (MemoryStream pdfStream = new MemoryStream())
                    {
                        using ( var document = new Document(new MemoryStream(bytes), FileFormat.Docx))
                        {
                            document.SaveToStream(pdfStream, FileFormat.PDF);
                            bytes = pdfStream.ToArray();
                        }
                    }
                }
                if (uploadFile.DOC_SUFFIX == ".doc")
                {
                    using (MemoryStream pdfStream = new MemoryStream())
                    {
                        using (var document = new Document(new MemoryStream(bytes), FileFormat.Doc))
                        {
                            document.SaveToStream(pdfStream, FileFormat.PDF);
                            bytes = pdfStream.ToArray();
                        }
                    }
                }
                doc_type = "PDF";
                uploadFile.DOC_SUFFIX = ".pdf";
                doc_name = $"{uploadFile.DOC_NAME}_{guid}{uploadFile.DOC_SUFFIX}";
            }

            var docInfo = new EMS_DOC_INFO
            {
                DOC_ID = IDGenHelper.CreateGuid().ToString(),
                HOSPITAL_ID = hospitalId,
                DOC_CLASS = uploadFile.DOC_CLASS,
                DOC_INFO_ID = uploadFile.DOC_INFO_ID,
                DOC_NAME = uploadFile.DOC_NAME,
                DOC_TYPE = doc_type,
                UPLOAD_PERSON = userName,
                UPLOAD_TIME = operTime,
                DOC_STATE = "1",
                FIRST_RPERSON = userName,
                FIRST_RTIME = operTime,
                LAST_MPERSON = userName,
                LAST_MTIME = operTime,
                DOC_PATH = "",
                DOC_SUFFIX = uploadFile.DOC_SUFFIX,
                PDF_PREVIEW_PATH = ""
            };

            var upLoadResult = _baseDataServices.UploadPathFormDataFile(doc_folder, if_cover, doc_name, bytes);

            if (upLoadResult != null && upLoadResult.success)
            {
                var jarray = JArray.Parse(upLoadResult.data.ToString());
                foreach (var item in jarray)
                {
                    docInfo.DOC_PATH = item["UploadPath"].ToString();
                }
            }
            else
            {
                return upLoadResult;
            }
            if (doc_type != "IMG")
            {
                bytes = PdfPreview.PDFPreview_Byte(bytes);
            }
            var doc_preview_name = $"{uploadFile.DOC_NAME}_{guid}_预览.jpg";
            var upLoadImgResult = _baseDataServices.UploadPathFormDataFile(doc_folder, if_cover, doc_preview_name, bytes);
            if (upLoadImgResult != null && upLoadImgResult.success)
            {
                var jarray1 = JArray.Parse(upLoadImgResult.data.ToString()); // 将JSON字符串转换为JArray对象
                foreach (var item in jarray1)
                {
                    docInfo.PDF_PREVIEW_PATH = item["UploadPath"].ToString();
                }
            }
            else
            {
                return upLoadImgResult;
            }
            _dbContext.Db.Insertable(docInfo).ExecuteCommand();
            bytes = null;
            return new ResultDto()
            {
                msg = "上传成功",
                success = true,
            };


        }
        public ResultDto UploadEnclosureInfo(EMS_DOC_INFO doc_file, string file_upload_address)
        {
            ResultDto result = new ResultDto();
            int countRow = 0;
            DateTime operateTime = DateTime.Now;
            string doc_id = string.Empty;
            string file_id = string.Empty;
            string file_suffix = doc_file.DOC_SUFFIX.ToLower();
            string file_type = string.Empty;
            string strBase = doc_file.FILEBASE64;
            LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            if (file_suffix == ".docx" || file_suffix == ".doc")
            {
                file_type = "WORD";
                byte[] bytes = Convert.FromBase64String(strBase);
                string strUploadPath = AppDomain.CurrentDomain.BaseDirectory + "uploads\\";
                if (!Directory.Exists(strUploadPath))
                    Directory.CreateDirectory(strUploadPath);
                string filePath = strUploadPath + IDGenHelper.CreateGuid().ToString() + file_suffix;//所要保存的相对路径及名字 

                FileStream stream = new FileStream(filePath, FileMode.CreateNew);
                BinaryWriter writer = new BinaryWriter(stream);
                writer.Write(bytes, 0, bytes.Length);
                writer.Close();

                string pdfName = $"{IDGenHelper.CreateGuid().ToString()}.pdf";
                string pdfPath = strUploadPath + pdfName;


                //保存文档
                Document document = new Document();
                document.LoadFromFile(filePath);
                document.SaveToFile(pdfPath, FileFormat.PDF);
                FileStream pdfFs = new FileStream(pdfPath, FileMode.Open);
                byte[] bt = new byte[pdfFs.Length];
                pdfFs.Read(bt, 0, bt.Length);
                pdfFs.Close();
                strBase = Convert.ToBase64String(bt);
            }
            else if (file_suffix == ".pdf")
            {
                file_type = "PDF";
            }
            else if (file_suffix == ".xlsx" || file_suffix == ".xls")
            {
                return new ResultDto { success = false, msg = "暂不支持excel类型" };
            }
            else if (file_suffix == ".ppt" || file_suffix == ".pttx")
            {
                return new ResultDto { success = false, msg = "暂不支持ppt类型" };
            }
            else
            {
                file_type = "IMG";
            }
            string splice_suffix = Guid.NewGuid().ToString("N"); ;
            //文件转为Base6
            // string filename = uploadfiledto.FILE_NAME + splice_suffix + file_suffix;
            string filename = doc_file.DOC_INFO_ID + "_" + splice_suffix;

            //ResultDto result1 = UploadFileOperate(filename, strBase, file_upload_address, file_suffix);
            var obj = new
            {
                fileName = filename + doc_file.DOC_SUFFIX,//文件名称
                src = strBase,//baes64字符串
                folderName = @"EMS/datafile",//文件夹路径
                ifCover = true,
            };
            var jsonStr = JsonConvert.SerializeObject(obj);

            ResultDto result1 = _baseDataServices.UploadPathFile(jsonStr, "");
            if (result1.success == true)
            {
                string pdf_preview = "";
                if (file_type != "IMG")
                {
                    strBase = PdfPreview.PDFPreview(strBase);
                }
                var objPreview = new
                {
                    fileName = filename + "预览" + ".jpg",//文件名称
                    src = strBase,//baes64字符串
                    folderName = @"EMS/datafile",//文件夹路径
                    ifCover = true,
                };
                var jsonStrPreview = JsonConvert.SerializeObject(objPreview);
                ResultDto result2 = _baseDataServices.UploadPathFile(jsonStrPreview, "");
                pdf_preview = result2.data.ToString();

                string file_path = result1.data.ToString();
                //添加文件
                EMS_DOC_INFO doc_insert = new EMS_DOC_INFO();
                string MaxId = IDGenHelper.CreateGuid().ToString();
                doc_insert.DOC_ID = MaxId;
                doc_insert.HOSPITAL_ID = doc_file.HOSPITAL_ID;//医疗机构ID
                doc_insert.DOC_INFO_ID = doc_file.DOC_INFO_ID;//关联ID
                doc_insert.DOC_CLASS = doc_file.DOC_CLASS;//分类ID
                doc_insert.DOC_TYPE = file_type;
                doc_insert.DOC_NAME = doc_file.DOC_NAME;//文件别名
                doc_insert.DOC_SUFFIX = file_suffix;//文件名称
                doc_insert.DOC_PATH = file_path;//文件路径
                doc_insert.DOC_STATE = "1";//文件状态
                doc_insert.PDF_PREVIEW_PATH = pdf_preview;//pdf预览
                doc_insert.FIRST_RPERSON = doc_file.FIRST_RPERSON;  //首次登记人
                doc_insert.FIRST_RTIME = doc_file.FIRST_RTIME;//首次登记时间
                doc_insert.LAST_MPERSON = doc_file.LAST_MPERSON;    //最后修改人员
                doc_insert.LAST_MTIME = doc_file.LAST_MTIME;  //最后修改时间
                _dbContext.Db.Insertable(doc_insert).ExecuteCommand();
                //string sql = "INSERT INTO EMS_DOC_INFO (DOC_ID,HOSPITAL_ID,DOC_INFO_ID,DOC_CLASS,DOC_TYPE,DOC_NAME,DOC_SUFFIX,DOC_PATH,DOC_STATE,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,BASE64CONTENT) VALUES " +
                //        "('" + MaxId + "','" + doc_file.HOSPITAL_ID + "','" + doc_file.DOC_INFO_ID + "','" + doc_file.DOC_CLASS + "','" +
                //        file_type + "','" + doc_file.DOC_NAME + "','" + file_suffix + "','" + file_path + "','" + '1'
                //        + "','" + doc_file.FIRST_RPERSON + "',to_date('" + doc_file.FIRST_RTIME + "','yyyy-mm-dd hh24:mi:ss'),'" + doc_file.LAST_MPERSON + "',to_date('" + doc_file.LAST_MTIME + "','yyyy-mm-dd hh24:mi:ss'),:CLOB)";
                //string MAIN_DB = "Rows Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=orcl)));User Id=XH_OA;Password=********;";
                ////string sql = "UPDATE EMS_DOC_INFO SET BASE64CONTENT = :CLOB WHERE DOC_ID = '"+ MaxId +"'";
                //OracleConnection conn = new OracleConnection(MAIN_DB);
                //conn.Open();
                //OracleCommand cmd = new OracleCommand(sql, conn);
                //OracleParameter op = new OracleParameter("clob", OracleDbType.Clob);
                //op.Value = filebase64;
                //cmd.Parameters.Add(op);
                //int i = cmd.ExecuteNonQuery();
                result.msg = "上传文件成功！";
                result.success = true;
            }
            else
            {
                result.msg = result1.msg;
                result.success = false;
            }
            return result;
        }

        public ResultDto UploadFileOperate(string fileName, string base64String, string file_upload_address, string file_suffix)
        {
            ResultDto result = new ResultDto();
            string guidefile = @"EMS/datafile";

            string jsonStr = string.Empty;
            string serviceUrl = string.Empty;
            if (file_suffix == "" || file_suffix == ".pdf" || file_suffix.ToLower() == ".jpg" || file_suffix.ToLower() == ".png" || file_suffix.ToLower() == ".bmp" || file_suffix.ToLower() == ".jpeg" || file_suffix.ToLower() == ".xlsx")
            {
                jsonStr = "{\"name\":\"" + fileName + "\",\"src\":\"data:application/pdf;base64," + base64String + "\",\"fileName\": \"" + guidefile + "\",\"ifCover\":\"true\"}";
                serviceUrl = file_upload_address + "/img/Common/SetUploadPdf";
            }
            else if (file_suffix != "" && file_suffix != ".pdf")
            {
                //添加水印
                //OfficeHelper tt = new OfficeHelper();
                ////获取程序所在目录  
                //string contentRootPath = _hostingEnvironment.ContentRootPath;
                //string folderPath = "ExampleFile\\";
                //string sourcePath = contentRootPath + folderPath;
                //string sourceName = fileName;
                //string strBase = tt.WordAddWatermark(base64String, sourcePath, sourceName);
                //if (strBase != null)
                //{
                file_suffix = file_suffix.Remove(0, 1);
                jsonStr = "{\"FileName\":\"" + fileName + "\",\"Base64Str\":\"" + base64String + "\",\"FilePath\": \"" + guidefile + "\",\"ifCover\":true,\"FileSuffix\":\"" + file_suffix + "\"}";
                serviceUrl = file_upload_address + "/api/UploadWordToPdf/UploadWordToPdf";
                //}
                //else
                //{
                //    result.success = false;
                //}
            }
            try
            {
                byte[] fileBytes = Encoding.UTF8.GetBytes(jsonStr);
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(serviceUrl);
                request.ContentType = "application/json";
                request.Method = "POST";
                request.Timeout = 300000;
                request.ContentLength = fileBytes.Length;
                Stream writer = request.GetRequestStream();
                writer.Write(fileBytes, 0, fileBytes.Length);
                writer.Close();

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.UTF8);
                result = JsonHelper.FromJson<ResultDto>(reader.ReadToEnd());
                response.Close();
            }
            catch (Exception)
            {
                result.success = false;
            }
            return result;
        }

        public ResultDto DeleteEnclosureInfo(string doc_id, string userName)
        {
            ResultDto result = new ResultDto();
            bool ifTrue = true;

            EMS_DOC_INFO doc_info = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(p => p.DOC_ID == doc_id).First();
            if (doc_info != null)
            {
                string file_path = doc_info.DOC_PATH.Replace("\\", "/");
                string file_suffix = doc_info.DOC_SUFFIX;
                string file_preview_path = doc_info.PDF_PREVIEW_PATH;
                _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(predicate => new EMS_DOC_INFO
                {
                    DOC_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(p => p.DOC_ID == doc_id).ExecuteCommand();
                ifTrue = _dbContext.SaveChanges();
                if (file_path.Contains("EMS/datafile"))
                {
                    ResultDto result1 = DeleteEnclosureFile(file_path);
                    if (file_preview_path.IsNotNullOrEmpty())
                    {
                        ResultDto result2 = DeleteEnclosureFile(file_preview_path.Replace("\\", "/"));
                    }
                }
            }
            if (ifTrue == true)
            {
                result.success = true;
                result.msg = "体系文件删除成功！";
            }
            else
            {
                result.success = true;
                result.msg = "体系文件删除失败！";
            }
            return result;
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="file_path"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        public ResultDto DeleteEnclosureFile(string file_path)
        {
            ResultDto result = new ResultDto();
            string guidefile = string.Empty;
            string jsonStr = string.Empty;
            string serviceUrl = string.Empty;
            jsonStr = "[{\"path\":\"" + file_path + "\"}]";
            serviceUrl = "/api/Common/DeleteFile";
            try
            {
                _baseDataServices.UploadPathFile(jsonStr, serviceUrl);
            }
            catch (Exception)
            {
                result.success = false;
            }
            return result;
        }
        public ResultDto GetAdverseEventInfo(string equipment_id, string file_preview_address)
        {
            ResultDto result = new ResultDto();
            //var res = (from a in _uow.GetRepository<AER_EVENT_SUBJECT>().Find(p => p.SUBJECT_CORR_ID == equipment_id)
            //            join b in _uow.GetRepository<AER_ADVERSE_EVENT>().DbSet()
            //            on a.EVENT_ID equals b.EVENT_ID
            //            select new
            //            {
            //                b.EVENT_ID,
            //                EQUIPMENT_ID = a.SUBJECT_CORR_ID,
            //                b.EVENT_TIME,
            //                ISSUED_FILE = b.ISSUED_FILE,
            //                b.EVENT_NAME
            //            }
            //           ).OrderBy(p => p.EVENT_TIME).ToList();
            var res = _dbContext.Db.Queryable<AER_EVENT_SUBJECT>()
                .LeftJoin<AER_ADVERSE_EVENT>((a, b) => a.EVENT_ID == b.EVENT_ID)
                .Where((a, b) => a.SUBJECT_CORR_ID == equipment_id)
                .Select((a, b) => new
                {
                    b.EVENT_ID,
                    EQUIPMENT_ID = a.SUBJECT_CORR_ID,
                    b.EVENT_TIME,
                    ISSUED_FILE = b.ISSUED_FILE,
                    b.EVENT_NAME
                }).ToList();
            result.data = res.OrderBy(p => p.EVENT_TIME).ToList();
            return result;
        }


        /// <summary>
        /// 获取菜单信息
        /// </summary>
        /// <param name="userNo">用户id</param>
        /// <param name="moduleId">模块id</param>
        /// <param name="hospitalId">医疗机构</param>
        /// <returns></returns>
        public ResultDto GetMenuInfo(string hospitalId, string moduleId, string userNo)
        {
            ResultDto rDto = new ResultDto();
            try
            {
                var claim = _httpContext.HttpContext.User.ToClaimsDto();
                List<navMenus> firstMenusDto = new List<navMenus>();
                //从工具箱读取
                var form_json = _dbContext
                    .GetRepository<SYS6_MODULE_FUNC_DICT>()
                    .GetFirstOrDefault(p => p.MODULE_ID == "H82" && p.HOSPITAL_ID == claim.HOSPITAL_ID && p.SETUP_NAME == "导航栏")
                    .FORM_JSON; 
                List<SYS6_MENU> sys_menu = JsonHelper.FromJson<List<SYS6_MENU>>(form_json);
                var rootNode = sys_menu.FirstOrDefault(p => p.PARENT_CODE == "root");
                if (!claim.USER_NAME.Contains("虚拟账户"))
                {
                    //读取岗位角色权限配置中授权的菜单ID
                    List<string> userMenuIDs = _authorityService.GetUserMenuList(_dbContext, AppSettingsProvider.CurrModuleId, MenuClassEnum.ALL)
                        .Select(a => a.MENU_ID).ToList();
                    sys_menu = sys_menu.Where(a => userMenuIDs.Contains(a.MENU_ID)).ToList();
                }
                else
                {
                    sys_menu = sys_menu.Where(a => a.MENU_ID.Contains("H82")).ToList();
                }
                sys_menu.Add(rootNode);
                var memuCode = sys_menu.FirstOrDefault(o => o.PARENT_CODE == "root")?.MENU_ID;
                List<SYS6_MENU> sys_menu_first = sys_menu.Where(w => w.PARENT_CODE == memuCode || w.PARENT_CODE.IsNullOrEmpty()).OrderBy(o => o.MENU_SORT).ToList();
                if (sys_menu_first.Count() > 0)
                {
                    for (int i = 0; i < sys_menu_first.Count(); i++)
                    {
                        navMenus firstDto = new navMenus
                        {
                            key = sys_menu_first[i].MENU_ID,
                            name = sys_menu_first[i].MENU_NAME,
                            customName = sys_menu_first[i].MENU_NAME,
                            hidden = false,
                            icon = sys_menu_first[i].MENU_ICON,
                            menLevel = sys_menu_first[i].MENU_LEVEL,
                            parentCode = sys_menu_first[i].PARENT_CODE,
                            sort = sys_menu_first[i].MENU_SORT,
                            menuUrl = sys_menu_first[i].MENU_URL,
                        };
                        //查询一级菜单下的二级菜单
                        var second = sys_menu.Where(w => w.PARENT_CODE == sys_menu_first[i].MENU_ID).OrderBy(o => o.MENU_SORT).ToList();
                        if (second.Count() > 0)
                        {
                            List<navMenus> secondDto = new List<navMenus>();
                            for (int j = 0; j < second.Count(); j++)
                            {
                                secondDto.Add(new navMenus
                                {
                                    key = second[j].MENU_ID,
                                    name = second[j].MENU_NAME,
                                    customName = second[j].MENU_NAME,
                                    hidden = false,
                                    icon = second[j].MENU_ICON,
                                    menLevel = second[j].MENU_LEVEL,
                                    parentCode = second[j].PARENT_CODE,
                                    sort = second[j].MENU_SORT,
                                    menuUrl = second[j].MENU_URL,
                                });
                            }
                            firstDto.children = secondDto;
                        }
                        firstMenusDto.Add(firstDto);
                    }
                }
                var jsonData = new
                {
                    collapased = false,
                    navMenus = firstMenusDto
                };
                rDto.data = jsonData;
            }
            catch (Exception ex)
            {
                rDto.msg = ex.Message;
                rDto.success = false;
                _logger.LogError($"GetMenuInfo:{ex.ToString()}");
            }

            return rDto;
           
        }

        public MenuInfoDto GetMenuInfoByPermission()
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            var result = new MenuInfoDto();
            var menus = new List<SYS6_MENU>();
            result.collapased = false;
            result.navMenus = new List<navMenus>();
            var rootMenu = new navMenus();
            var rootMenuId = _dbContext
                .Db.Queryable<SYS6_MODULE_FUNC_DICT>()
                .Where(p => p.MODULE_ID == AppSettingsProvider.CurrModuleId && p.HOSPITAL_ID == user.HOSPITAL_ID &&
                            p.SETUP_NAME == "导航栏")
                .Select(a => a.FUNC_ID)
                .First();
            rootMenu.key = rootMenuId;
            if (user.USER_NAME.Contains("虚拟账户"))
            {
                var menuJson = _dbContext
                    .Db.Queryable<SYS6_MODULE_FUNC_DICT>()
                    .Where(p => p.MODULE_ID == AppSettingsProvider.CurrModuleId && p.HOSPITAL_ID == user.HOSPITAL_ID &&
                                p.SETUP_NAME == "导航栏")
                    .Select(a => a.FORM_JSON)
                    .First(); 
                menus= JsonHelper.FromJson<List<SYS6_MENU>>(menuJson);
            }
            else
            {
                menus = _authorityService.GetUserMenuList(_dbContext, AppSettingsProvider.CurrModuleId, MenuClassEnum.ALL).ToList();
            }
            var nodeMap = new Dictionary<string, navMenus>();
            nodeMap.Add(rootMenuId, rootMenu);
            foreach (var menu in menus)
            {
               // 深拷贝节点并创建 Children 属性
               var newNode = new navMenus
               {
                   key = menu.MENU_ID,
                   name = menu.MENU_NAME,
                   customName = menu.MENU_NAME,
                   hidden = false,
                   icon = menu.MENU_ICON,
                   menLevel = menu.MENU_LEVEL,
                   parentCode = menu.PARENT_CODE,
                   sort = menu.MENU_SORT,
                   menuUrl = menu.MENU_URL,
               };
               nodeMap[newNode.key] = newNode;
            }
            foreach (var menu in menus)
            {
               var currentNode = nodeMap[menu.MENU_ID];
               // 根据 ParentId 找到父节点，将当前节点添加到父节点的 Children 中
               if (nodeMap.TryGetValue(menu.PARENT_CODE, out var parentNode))
               {
                   parentNode.children.Add(currentNode);
               }
            }
            result.navMenus.AddRange(rootMenu.children);
            return result;
        }

        public ResultDto GetFuncDictInfo(string menuId, string moduleId, string hospitalId)
        {
            ResultDto rDto = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization;
            string url = $"/externalapi/External/GetSetItemsByPageId?hospitalId={hospitalId}&moduleId={moduleId}&pageId={menuId}&qunitId=";
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Authorization", "Bearer " + token);
            request.AddHeader("Authorization", token);
            rDto = _clientH04.ExecuteGet<ResultDto>(request).Data;
            return rDto;
        }
        public List<SYS6_BASE_DATA> GetCompanyClassList()
        {
            var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "公司类型" && p.DATA_STATE == "1")
                .ToList();
            return res;
        }
        public ResultDto GetAreaPullList(string userNo, string labId, string hospitalId)
        {
            var authorityContext = new AuthorityContext(_dbContext, _authorityService);
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            authorityContext.SetUser(user,labId);
            authorityContext.CheckedUserAuth(labId);
            var areas = authorityContext.GetAccessibleAreas(labId).Select(area => new
            {
                area.AREA_ID,
                area.AREA_NAME,
                area.AREA_SORT
            }).Distinct().OrderBy(area => area.AREA_SORT).ToList();


            return new ResultDto { data = areas, success = true };
        }

        public (string uploadFileNmae, string uploadFileTYPE, byte[] uploadFileByte) GetUploadFileInfo(UploadFileDto uploadFile, string? fileMinName)
        {

            LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            byte[] bytes = null;
            using (var stream = uploadFile.FILE.OpenReadStream())
            {
                bytes = new byte[stream.Length];
                stream.Read(bytes, 0, bytes.Length);
                stream.Seek(0, SeekOrigin.Begin);
            }
            //过滤特殊字符
            uploadFile.DOC_NAME = uploadFile.DOC_NAME.Replace('+', '_').Replace('@', '_').Replace(' ', '_').Replace(uploadFile.DOC_SUFFIX, "");
            var doc_type = FileTypeUnit.ReturnFileType(uploadFile.DOC_SUFFIX, new List<string>() { ".xlsx", ".xls" });
            var operTime = DateTime.Now;
            var doc_name = $"{uploadFile.DOC_NAME}_{fileMinName}{uploadFile.DOC_SUFFIX}";
            if (uploadFile.DOC_SUFFIX == ".docx" || uploadFile.DOC_SUFFIX == ".doc")
            {
                if (uploadFile.DOC_SUFFIX == ".docx")
                {
                    using (MemoryStream pdfStream = new MemoryStream())
                    {
                        using (var document = new Document(new MemoryStream(bytes), FileFormat.Docx))
                        {
                            document.SaveToStream(pdfStream, FileFormat.PDF);
                            bytes = pdfStream.ToArray();
                        }
                    }
                }
                if (uploadFile.DOC_SUFFIX == ".doc")
                {
                    using (MemoryStream pdfStream = new MemoryStream())
                    {
                        using (var document = new Document(new MemoryStream(bytes), FileFormat.Doc))
                        {
                            document.SaveToStream(pdfStream, FileFormat.PDF);
                            bytes = pdfStream.ToArray();
                        }
                    }
                }
                doc_type = "PDF";
                uploadFile.DOC_SUFFIX = ".pdf";
                doc_name = $"{uploadFile.DOC_NAME}_{fileMinName}{uploadFile.DOC_SUFFIX}";
                return (doc_name, doc_type, bytes);
            }
            else if (doc_type == "PDF")
            {
                doc_name = $"{uploadFile.DOC_NAME}_{fileMinName}{uploadFile.DOC_SUFFIX}";
                return (doc_name, doc_type, bytes);
            }
            else if (doc_type == "IMG")
            {
                doc_name = $"{uploadFile.DOC_NAME}_{fileMinName}{uploadFile.DOC_SUFFIX}";
                return (doc_name, doc_type, bytes);
            }
            else
            {
                throw new BizException($"当前不支持类型{uploadFile.DOC_SUFFIX}.文件");
            }
        }


    }
}
