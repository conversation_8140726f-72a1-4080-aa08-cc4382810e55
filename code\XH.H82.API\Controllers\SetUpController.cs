﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.LAB.UTILS.Models;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class SetUpController : ControllerBase
    {
        private readonly ISetUpService _setUpService;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;

        public SetUpController(
            ISetUpService setUpService,
            IConfiguration configuration,
            IMemoryCache easyCahceFactory
        )
        {
            _setUpService = setUpService;
            _configuration = configuration;
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;
        }

        /// <summary>
        ///   获取系统设置列表
        /// </summary>
        /// <param name="mgroupId">管理专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSysSetUpList([BindRequired] string mgroupId)
        {
            var claims = User.ToClaimsDto();
            var res = _setUpService.GetSysSetUp(mgroupId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改系统设置信息
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public IActionResult UpdateSysSetUpInfo([FromBody] SYS6_SETUP_DICT record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.USER_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _setUpService.UpdateSysSetUpInfo(record);
            return Ok(res);
        }

        /// <summary>
        ///   系统设置应用全部
        /// </summary>
        /// <param name="choiceValue">选择值</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ApplyAllSetUpInfo(string choiceValue)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _setUpService.ApplyAllSetUpInfo(
                claims.USER_NO,
                claims.HOSPITAL_ID,
                claims.USER_NAME,
                choiceValue,
                labId
            );
            return Ok(res);
        }

        /// <summary>
        /// 查询工作计划是否需要审核流程   返回结果的data：true   需要，false不需要
        /// 不需要审核流程时,设备工作计划表格需隐藏“状态”列和“提交”,“审核”，“撤销”按钮
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult IsNeedReviewProcess()
        {
            var user = User.ToClaimsDto();
            return Ok(_setUpService.NeedReviewProcess(user.HOSPITAL_ID).ToResultDto());
        }

        /// <summary>
        /// 检查当前机构是否需要输入密码验证才能编辑设备数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult IsNeedCheckPasswordByEditEquipment()
        {
            var user = User.ToClaimsDto();
            return Ok(
                _setUpService.NeedNeedCheckPasswordByEditEquipment(user.HOSPITAL_ID).ToResultDto()
            );
        }

        /// <summary>
        /// 查询房间位置列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPositionInfo()
        {
            var positions = _setUpService.GetPositions();
            var result = new List<PositionDto>();
            foreach (var position in positions)
            {
                result.Add(
                    new PositionDto() { Id = position.POSITION_ID, Name = position.POSITION_NAME }
                );
            }
            return Ok(result.ToResultDto());
        }
    }
}
