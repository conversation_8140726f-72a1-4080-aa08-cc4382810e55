﻿using H.IRepository;
using H.Utility;
using H.Utility.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting.Internal;

using Spire.Pdf;
using Spire.Pdf.Bookmarks;
using System;
using System.IO;
using NPOI.SS.Formula.Functions;
using System.Data;
using XH.H82.Models;
using Base64Helper = H.Utility.Helper.Base64Helper;
using Spire.Doc.Documents;
using Spire.Doc;
using Newtonsoft.Json;
using NPOI.HPSF;
using H.BASE.SqlSugarInfra.Uow;
using Microsoft.Extensions.Logging;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;
using System.Reflection;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Services.DeactivationScrapping;
using SqlSugar;
using Microsoft.AspNetCore.Http;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Interface;

namespace XH.H82.Services
{
    public class ScrapStopService : IScrapStopService
    {
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IBaseDataServices _baseDataServices;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly ILogger<OperationRecordService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IAuthorityService2 _authorityService;

        public ScrapStopService(IHostingEnvironment hostingEnvironment, IBaseDataServices baseDataServices, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, ILogger<OperationRecordService> logger, IHttpContextAccessor httpContext, IAuthorityService2 authorityService)
        {
            _hostingEnvironment = hostingEnvironment;
            _baseDataServices = baseDataServices;
            _sqlSugarUow = sqlSugarUow;
            _logger = logger;
            _httpContext = httpContext;
            _authorityService = authorityService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public List<ScrapStopListDto> GetScrapStopList(DateTime startTime, DateTime endTime, string hospitalId,
             string mgroupId, string equipmentClass, string state, string applyClass, string equipmentKey,
             string personKey, string file_preview_address, string userNo,
             string person_select, string mgroup_select, string selfMgroupId, string labId, string areaId, string pgroupId)
        {

            var authorityContext = new AuthorityContext(_sqlSugarUow,_authorityService);
            authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),  labId, areaId);
            authorityContext.CheckedUserAuth(labId, areaId, mgroupId, pgroupId);

            //专业组列表
            var groupList = authorityContext.GetAccessibleProfessionalGroups(labId,areaId);
            //设备停用列表
            var q = _sqlSugarUow.Db.Queryable<EMS_SCRAP_INFO>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .Where((a, b) => a.HOSPITAL_ID == hospitalId && a.SCRAP_STATE == "1" && a.OPER_TIME <= endTime && a.OPER_TIME >= startTime && groupList.Select(i => i.PGROUP_ID).Contains(b.UNIT_ID))
                .WhereIF(pgroupId.IsNotNullOrEmpty(), (a, b) => b.UNIT_ID == pgroupId)
                .WhereIF(equipmentClass.IsNotNullOrEmpty(), (a, b) => b.EQUIPMENT_CLASS == equipmentClass)
                .WhereIF(applyClass.IsNotNullOrEmpty(), (a, b) => a.APPLY_TYPE == applyClass)
                .WhereIF(equipmentKey.IsNotNullOrEmpty(), (a, b) => b.EQUIPMENT_ENAME.ToLower().Contains(equipmentKey.ToLower())
                || b.EQUIPMENT_MODEL.ToLower().Contains(equipmentKey.ToLower())
                || b.EQUIPMENT_CODE.ToLower().Contains(equipmentKey.ToLower()))
                .WhereIF(personKey.IsNotNullOrEmpty(), (a, b) => a.OPER_PERSON.ToLower().Contains(personKey.ToLower())
                || a.APPROVE_PERSON.ToLower().Contains(personKey.ToLower())
                || a.EXAMINE_PERSON.ToLower().Contains(personKey.ToLower()))
                .Select((a, b) => new
                {
                    a.SCRAP_ID,
                    a.APPLY_STATE,
                    a.APPLY_TYPE,
                    b.UNIT_ID,
                    b.EQUIPMENT_ID,
                    b.EQUIPMENT_NAME,
                    b.EQUIPMENT_MODEL,
                    b.EQUIPMENT_CODE,
                    b.EQUIPMENT_CLASS,
                    b.MANUFACTURER,
                    b.DEALER,
                    a.OPER_PERSON,
                    a.OPER_PERSON_ID,
                    a.OPER_TIME,
                    a.SCRAP_CAUSE,
                    a.EXAMINE_PERSON,
                    a.EXAMINE_PERSON_ID,
                    a.EXAMINE_DATE,
                    a.EXAMINE_OPINION,
                    a.APPROVE_PERSON,
                    a.APPROVE_PERSON_ID,
                    a.APPROVE_OPINION,
                    a.APPROVE_DATE,
                    a.IF_RE_APPLY,
                    b.EQUIPMENT_STATE,
                    a.FILE_PATH,
                    a.SCRAP_DATE,
                    b.SMBL_LAB_ID,
                    b.SMBL_FLAG
                }).MergeTable();
            if (mgroupId.IsNotNullOrEmpty())
            {
                var pList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .Where(p => p.MGROUP_ID == mgroupId && p.PGROUP_STATE == "1")
                    .Select(i => i.PGROUP_ID).ToList();
                q = q.Where(a => pList.Contains(a.UNIT_ID));
            }
            //若申请状态不为空
            if (state.IsNotNullOrEmpty())
            {
                if (state == "4")
                {
                    q = q.Where(p => p.APPLY_STATE == "4" || p.APPLY_STATE == "5");
                }
                else
                {
                    q = q.Where(p => p.APPLY_STATE == state);
                }
            }
            //本人待处理
            if (person_select == "1")
            {
                q = q.Where(a => userNo == ((a.APPLY_STATE == "0" || a.APPLY_STATE == "4" || a.APPLY_STATE == "5")
                ? a.OPER_PERSON_ID : a.APPLY_STATE == "1"
                ? a.EXAMINE_PERSON_ID : a.APPLY_STATE == "2"
                ? a.APPROVE_PERSON_ID : null));
            }
            //实验室待处理
            if (mgroup_select == "1")
            {
                q = q.Where(a => a.APPLY_STATE != "3" && a.UNIT_ID == selfMgroupId);
            }
            //存储报废停用列表dto
            var scrapStopList = new List<ScrapStopListDto>();
            //关联专业组表
            var res = q.ToList();
            var res_pgroup = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => p.PGROUP_STATE == "1").ToList();
            var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
                .ToList();
            res.ForEach(item =>
            {
      
                scrapStopList.Add(new ScrapStopListDto
                {
                    ScrapStop_ID = item.SCRAP_ID,
                    STATE = item.APPLY_STATE,
                    APPLY_CLASS = item.APPLY_TYPE,
                    MGROUP_ID = item.UNIT_ID,
                    MGROUP_NAME = res_pgroup.Where(p => p.PGROUP_ID == item.UNIT_ID).FirstOrDefault()?.PGROUP_NAME,
                    EQUIPMENT_CLASS = baseData.Where(p => p.DATA_ID == item.EQUIPMENT_CLASS).FirstOrDefault()?.DATA_CNAME,
                    EQUIPMENT_ID = item.EQUIPMENT_ID,
                    EQUIPMENT_NAME = item.EQUIPMENT_NAME,
                    EQUIPMENT_MODAL = item.EQUIPMENT_MODEL,
                    EQUIPMENT_CODE = item.EQUIPMENT_CODE,
                    MANUFACTURER = item.MANUFACTURER,
                    DEALER = item.DEALER,
                    APPLY_PERSON = item.OPER_PERSON,
                    APPLY_PERSON_ID = item.OPER_PERSON_ID,
                    APPLY_DATE = item.OPER_TIME,
                    APPLY_REASON = item.SCRAP_CAUSE,
                    EXAMINE_PERSON = item.EXAMINE_PERSON,
                    EXAMINE_PERSON_ID = item.EXAMINE_PERSON_ID,
                    EXAMINE_DATE = item.EXAMINE_DATE,
                    EXAMINE_OPINION = item.EXAMINE_OPINION,
                    APPROVE_PERSON = item.APPROVE_PERSON,
                    APPROVE_PERSON_ID = item.APPROVE_PERSON_ID,
                    APPROVE_OPINION = item.APPROVE_OPINION,
                    APPROVE_DATE = item.APPROVE_DATE,
                    IF_RE_APPLY = item.IF_RE_APPLY,
                    DEAL_PERSON_ID = item.APPLY_STATE == "0" ? item.OPER_PERSON_ID :
                    item.APPLY_STATE == "1" ? item.EXAMINE_PERSON_ID :
                    item.APPLY_STATE == "2" ? item.APPROVE_PERSON_ID :
                    (item.APPLY_STATE == "4" || item.APPLY_STATE == "5") ?
                    item.OPER_PERSON_ID : "",
                    REVOKE_PERSON_ID = item.APPLY_STATE == "1" ? item.OPER_PERSON_ID :
                    (item.APPLY_STATE == "2" || item.APPLY_STATE == "4") ? item.EXAMINE_PERSON_ID :
                    (item.APPLY_STATE == "3" || item.APPLY_STATE == "5") ? item.APPROVE_PERSON_ID :
                    "",
                    EQUIPMENT_STATE = item.EQUIPMENT_STATE,
                    FILE_PATH = item.FILE_PATH + ".pdf",
                    SCRAP_DATE = item.SCRAP_DATE,
                    SMBL_FLAG = item.SMBL_FLAG =="1" ? "1" : "0",
                    SMBL_LAB_ID = item.SMBL_LAB_ID
                });
            });
            return scrapStopList.OrderByDescending(P => P.APPLY_DATE).ToList();
        }

        public List<EMS_EQUIPMENT_INFO> GetEquipmentApplyList(string userNo, string hospitalId, string mgroupId, string equipmentClass, string keyword, string labId, string areaId)
        {
            var groupList =
                _authorityService.GetUserPermissionPgroup(_sqlSugarUow,new OrgParams()
                {
                     hospital_id = hospitalId,
                     lab_id =  labId,
                     area_id = areaId
                },"H82");
            var pgids = groupList.Select(x => x.PGROUP_ID);

            var equipmentContext = new EquipmentContext(_sqlSugarUow);

            var deactivationScrappingContext = new DeactivationScrappingContext(_sqlSugarUow);
            deactivationScrappingContext.AsEquipment();
            var equipments = deactivationScrappingContext.GetEquipments();

            equipments = equipments
                .Where(x => pgids.Contains(x.UNIT_ID))
                 .WhereIF(equipmentClass.IsNotNullOrEmpty(), x => x.EQUIPMENT_CLASS == equipmentClass)
                 .Where(x => x.eMS_SCRAP_INFO.Count() == 0 || x.eMS_SCRAP_INFO.Count(x => x.APPLY_STATE == "3") > 0)
                .ToList();

            foreach (var equipment in equipments)
            {
                equipment.EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE);
                equipment.MGROUP_NAME = equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
            }

            if (keyword.IsNotNullOrEmpty())
            {
                equipments = equipments
                .Where(x => x.EQUIPMENT_NAME is not null)
                .Where(x => x.EQUIPMENT_MODEL is not null)
                .Where(x => x.EQUIPMENT_CODE is not null)
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.EQUIPMENT_NAME.ToLower().Contains(keyword.ToLower())
                || p.EQUIPMENT_MODEL.ToLower().Contains(keyword.ToLower())
                || p.EQUIPMENT_CODE.ToLower().Contains(keyword.ToLower()))
                .ToList();
            }

            return equipments;

        }



        public ResultDto DeleteAwaitSubmit(List<ScrapStopListDto> record)
        {
            ResultDto result = new ResultDto();
            try
            {
                record.ForEach(item =>
                {
                    _sqlSugarUow.Db.Deleteable<EMS_SCRAP_INFO>().Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                    _sqlSugarUow.Db.Deleteable<EMS_SCRAP_LOG>().Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除申请信息失败";
                _logger.LogError("删除申请信息失败:\n" + ex.Message);
            }
            return result;
        }


        public List<EMS_SCRAP_LOG> ScrapStopProcess(string scrapId)
        {
            var res = _sqlSugarUow.Db.Queryable<EMS_SCRAP_LOG>()
                .Where(p => p.SCRAP_ID == scrapId && p.PROCESS_STATE == "1")
                .OrderBy(i => i.OPER_TIME)
                .ToList();
            return res;
        }
    }
}