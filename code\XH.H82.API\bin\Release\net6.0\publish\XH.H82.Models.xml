<?xml version="1.0"?>
<doc>
    <assembly>
        <name>XH.H82.Models</name>
    </assembly>
    <members>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules">
            <summary>
            传感器告警规则
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules.Up">
            <summary>
            上限
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules.Down">
            <summary>
            下限
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules.IsNeedAlarm">
            <summary>
            是否需要告警 1需要 0 不需要
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules.BioAlarmType">
            <summary>
            监测告警类型
            100开关使用状态、101电压、102电流、103功率、104累计电量、200温度、201湿度、202噪声、203气压、205紫外线强度、206水压
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.AlarmRules.unit">
            <summary>
            数值单位
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.BioAlarmTypeEnum">
            <summary>
            环境一体机监测类型
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.BioAlarmTypeEnum.Temperature">
            <summary>
            温度
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.BioAlarmTypeEnum.Humidity">
            <summary>
            湿度
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.BioAlarmTypeEnum.Noise">
            <summary>
            噪声
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.BioAlarmTypeEnum.AirPressure">
            <summary>
            气压
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord">
            <summary>
            实时监测记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.Id">
            <summary>
            电信设备唯一标识
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.Name">
            <summary>
            测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.Sn">
            <summary>
            设备sn
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.LabId">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.RoomId">
            <summary>
            房间id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.CheckpointId">
            <summary>
            测点id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceMonitorRecord.Monitors">
            <summary>
            检测项数据集合
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DevicesStatusValue">
            <summary>
            状态值计算模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DevicesStatusValue.UseFunc">
            <summary>
            是否使用中
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DevicesStatusValue.ShutDownFunc">
            <summary>
            是否关机
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DevicesStatusValue.StandByFunc">
            <summary>
            是否待机
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceType">
            <summary>
            医疗设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceType.Type">
            <summary>
            医疗类型枚举
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceType.Name">
            <summary>
            医疗类型枚举
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DeviceTypeAndSensorTypeDto">
            <summary>
            医疗设备下的传感器类型关系
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Id">
            <summary>
            电信设备唯一标识
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Name">
            <summary>
            测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Sn">
            <summary>
            设备sn
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.LabId">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.RoomId">
            <summary>
            房间id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.CheckpointId">
            <summary>
            测点id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Data">
            <summary>
            物理机json数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.BioAlarmType">
            <summary>
            监测类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Status">
            <summary>
            指标是否正常   0异常  1正常
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.EnvironmentDevicesDto.Value">
            <summary>
            监测值
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Id">
            <summary>
            id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Sn">
            <summary>
            设备sn
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Model">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Type">
            <summary>
            701智能开关，702生物安全柜，703紫外灯，704高压灭菌柜
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Ip">
            <summary>
            ip
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.OpenStatus">
            <summary>
            通电状态：0未通电 1通电
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.RoomId">
            <summary>
            房间id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.RoomName">
            <summary>
            房间名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.CheckpointId">
            <summary>
            测点id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.CheckpointName">
            <summary>
            测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.LabId">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.LabName">
            <summary>
            实验室名
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.IsOnline">
            <summary>
            是否在线：0不在线1在线
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.SwitchStatus">
            <summary>
            设备开关状态：0关机，1待机 ,2运行中, 3过载   201消毒 
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Voltage">
            <summary>
            当前电压，单位V，保留三位小数
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Current">
            <summary>
            当前电流，单位A，保留三位小数
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Power">
            <summary>
            当前功率，单位W，保留三位小数
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.Energy">
            <summary>
            累计用电量，单位KW*H，保留三位小数，
            断电后不会归零，重置后会归零
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.IoTDevicesDto.PowerSyncTime">
            <summary>
            功率同步时间
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorData">
            <summary>
            传感器监测项的监测数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorData.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorData.BioAlarmType">
            <summary>
            监测类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorData.Status">
            <summary>
            异常状态 1正常  0 异常
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorType">
            <summary>
            监测类型项
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorType.BioAlarmType">
            <summary>
            监测枚举
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.MonitorType.Name">
            <summary>
            监测项名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Room.Id">
            <summary>
            房间三方主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Room.Name">
            <summary>
            房间名字
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Room.LabId">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Room.LabName">
            <summary>
            实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Room.ParentId">
            <summary>
            父级房间id
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor">
            <summary>
            传感器
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.Id">
            <summary>
            传感器ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.Name">
            <summary>
            传感器名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.Sn">
            <summary>
            SN码 唯一
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.Model">
            <summary>
            传感器型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.SensorType">
            <summary>
            传感器类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.RoomId">
            <summary>
            传感器所在房间id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.RoomName">
            <summary>
            传感器所在房间名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.CheckpointId">
            <summary>
            传感器所在房间监测点id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.CheckpointName">
            <summary>
            传感器所在监测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.LabId">
            <summary>
            传感器所属实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.LabName">
            <summary>
            传感器所属实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.IsOnline">
            <summary>
            传感器是否在线  1在线  0离线
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.Sensor.SwitchStatus">
            <summary>
            医疗设备开关状态 0离线/关机 1待机,2运行,201生物安全柜紫外灯运行,3过载
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorsType">
            <summary>
            传感器类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorsType.SensorType">
            <summary>
            类型枚举
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorsType.Name">
            <summary>
            传感器类型名称
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorTypeAndMonitorTypeDto">
            <summary>
            传感器类型下的监测类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorTypeAndMonitorTypeDto.Key">
            <summary>
            医疗设备类型枚举
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.SensorTypeAndMonitorTypeDto.Value">
            <summary>
            医疗设备类型名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.Name">
            <summary>
            医疗设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.Sn">
            <summary>
            SN码 唯一
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.Model">
            <summary>
            设备序列号
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.Type">
            <summary>
            医疗设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.RoomId">
            <summary>
            医疗设备所在房间id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.RoomName">
            <summary>
            医疗设备所在房间名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.CheckpointId">
            <summary>
            医疗设备所在房间监测点id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.CheckpointName">
            <summary>
            医疗设备所在监测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.LabId">
            <summary>
            医疗设备所属实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.LabName">
            <summary>
            医疗设备所属实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.IsOnline">
            <summary>
            医疗设备是否在线 1在线  0离线
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.ThirdPartyDevice.SwitchStatus">
            <summary>
            医疗设备开关状态 0离线/关机 1待机,2运行,201生物安全柜紫外灯运行,3过载
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.UltravioletLampDto">
            <summary>
            紫外灯模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.UltravioletLampDto.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.UltravioletLampDto.SwitchStatus">
            <summary>
            在线状态
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.WaterPressureDto">
            <summary>
            水压传感器数据模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.WaterPressureDto.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.WaterPressureDto.SwitchStatus">
            <summary>
            在线状态
            </summary>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.H115Client.GetGenerateImgBase64(XH.H82.Models.Card.EInkChangeDisplayDto)">
            <summary>
            生成图片base64
            </summary>
            <param name="body"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.H115Client.GetGenerateImgStream(XH.H82.Models.Card.EInkChangeDisplayDto)">
            <summary>
            生成图片流
            </summary>
            <param name="body"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.H115Client.QueryDevice">
            <summary>
            查询设备电量
            </summary>
            <returns></returns>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkResponse`1.code">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkResponse`1.data">
            <summary>
            EInkData或者EInkMsgData或者EInkTokenData
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkData.items">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkData.total">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkLedColor.red">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkLedColor.green">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkLedColor.blue">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkLed.color">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkStation.ssid">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkStation.password">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkStation.rssi">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkMqtt.broker">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkMqtt.port">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkMqtt.username">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkMqtt.password">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkDisplay.fail">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkDisplay.success">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkNetwork.conn">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkNetwork.conn_fail">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkNetwork.discon">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkSms.display">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkSms.network">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkSms.reboot">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EinkImages.binFile">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EinkImages.imageFile">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkDevtype._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkDevtype.type">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkDevtype.name">
            <summary>
            WiFi门牌
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkCreatedBy._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkCreatedBy.username">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkProduct._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkProduct.name">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkProduct.apiKey">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkProduct.createdBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype.type">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype.name">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype.width">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype.height">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkScreentype.color">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkAlgorithm._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkAlgorithm.name">
            <summary>
            二值化算法
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkAlgorithm.algorithm">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.led">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.station">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.mqtt">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.sms">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.images">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem._id">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.mac">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.__v">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.alias">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.createdAt">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.createdBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.devId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.devtype">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.hw">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.ip">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.product">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.screentype">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.sn">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.status">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.sw">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.updatedAt">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.usbState">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.voltage">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.H115Client.EInkItemsItem.algorithm">
            <summary>
            
            </summary>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.H91Client.GetSysDocFileDropDownByUsing(System.String)">
            <summary>
            获取文件正在使用的版本
            </summary>
            <param name="docId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.H92Client.GetCompanyCerlist(H.BASE.SqlSugarInfra.Uow.ISqlSugarUow{XH.H82.Models.SugarDbContext.SugarDbContext_Master})">
            <summary>
            获取供应商的证书列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.CertificatDto.GetFileRecords">
            <summary>
            获取供应商证书附件
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.FileRecordInfo">
            <summary>
            供应商多附件记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.CertificatStatusEnum">
            <summary>
            证书类型
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.CertificatStatusEnum.Active">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.CertificatStatusEnum.PreExpire">
            <summary>
            临近过期
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.CertificatStatusEnum.Expire">
            <summary>
            已过期
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.CertificatStatusEnum.Invalid">
            <summary>
            作废
            </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.STATE_CER.ACTIVE">
             <summary>
            正常
             </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.STATE_CER.PRE_EXPIRE">
             <summary>
            临近过期
             </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.STATE_CER.EXPIRE">
             <summary>
            过期
             </summary>
        </member>
        <member name="F:XH.H82.Models.BusinessModuleClient.STATE_CER.INVALID">
             <summary>
            作废
             </summary>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetToken(System.String)">
            <summary>
            加密
            </summary>
            <param name="sn">设备sn</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.Encrypt(System.String,System.String)">
            <summary>
            AES加密
            </summary>
            <param name="plainText"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.Decrypt(System.String,System.String)">
            <summary>
            解密
            </summary>
            <param name="content">待解密内容</param>
            <param name="password">解密密钥</param>
            <returns>解密后的字符串</returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetAesKey">
            <summary>
            获取最终key
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetKey(System.String)">
            <summary>
            生成密钥
            </summary>
            <param name="strKey">密钥字符串</param>
            <returns>生成的密钥</returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetIoTDeviceInfo(System.String,System.String)">
            <summary>
            实时获取开关物联设备功率
            </summary>
            <param name="sn">设备sn码</param>
            <param name="model">设备型号</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetEnvironmentDevicesInfo(System.String,System.String)">
            <summary>
            获取环境一体机的实时信息
            </summary>
            <param name="sn"></param>
            <param name="model"></param>
            <returns></returns>
            <exception cref="T:H.Utility.BizException"></exception>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetGetUltravioletLampDeviceInfo(System.String,System.String)">
            <summary>
            获取紫外灯实时监数据
            </summary>
            <param name="sn"></param>
            <param name="model"></param>
            <returns></returns>
            <exception cref="T:H.Utility.BizException"></exception>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetGetWaterPressureDeviceInfo(System.String,System.String)">
            <summary>
            获取水压实时监数据
            </summary>
            <param name="sn"></param>
            <param name="model"></param>
            <returns></returns>
            <exception cref="T:H.Utility.BizException"></exception>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetRooms">
            <summary>
            获取房间信息
            </summary>
            <returns></returns>
            <exception cref="T:H.Utility.BizException"></exception>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetDeviceTypeAndSensorTypes">
            <summary>
            医疗设备类型与传感器类型字典查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.BusinessModuleClient.IoTDevices.IoTDevicesClient.GetSensorTypeAndMonitorTypes">
            <summary>
            传感器类型与监测类型字典查询
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA">
            <summary>
            实验室管理基础数据表
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.DATA_ID">
            <summary>
            基础数据ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.FATHER_ID">
            <summary>
            父级ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.CLASS_ID">
            <summary>
            分类ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.DATA_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.DATA_NAME">
            <summary>
            中文名
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.DATA_SNAME">
            <summary>
            简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.DATA_ENAME">
            <summary>
            英文名
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.STANDART_ID">
            <summary>
            标准代码
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.CUSTOM_CODE">
            <summary>
            自定义码
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.SPELL_CODE">
            <summary>
            拼音码
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.STATE_FLAG">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_BASE_DATA.ADDN_CONFIG_JSON">
            <summary>
            自定义扩展配置
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE">
            <summary>
            上传文件记录表
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_ID">
            <summary>
            上传文件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.PGROUP_ID">
            <summary>
            专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_MODULE_ID">
            <summary>
            文件服务器模块ID;LIS6_INSPECT_GRAPH
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.DATA_CLASS">
            <summary>
            来源类型;(规则：模块ID.编号) H92.1-供应商资质  H92.7-评价表样式  H92.8-评价表模板
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.DATA_ID">
            <summary>
            来源数据ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_URL">
            <summary>
            文档附件源文件地址;S28返回的json地址，含文件名和后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_NAME">
            <summary>
            文档附件源文件名(含后缀);含后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_TYPE">
            <summary>
            文档附件源文件分类;按类型填入：PDF/WORD/EXCEL   /PIC
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_SUFFIX">
            <summary>
            转换后的文件后缀（含.）
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.ORIGIN_FILE_URL">
            <summary>
            原始文件预览地址
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_SIZE">
            <summary>
            原始文件大小(K)
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.ORIGIN_FILE_SIZE">
            <summary>
            原始文件大小(K)
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.PREVIEW_FILE_URL">
            <summary>
            文档附件预览地址;S28返回的json地址，含文件名和后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.PREVIEW_FILE_NAME">
            <summary>
            文档附件文件名(含后缀);含后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FILE_STATE">
            <summary>
            状态;0禁用1启用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.ORIGIN_FILE_NAME">
            <summary>
            原始文件名（不含后缀）
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.ORIGIN_FILE_TYPE">
            <summary>
            原始文件类型（不含.）
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.OA_UPLOAD_FILE.ORIGIN_FILE_SUFFIX">
            <summary>
            原始文件后缀（含.）
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.Platform.PlatformClient">
            <summary>
            统一平台客户端
            </summary>
        </member>
        <member name="T:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE">
            <summary>
            供应商资料表
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_ID">
            <summary>
            资料文件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.COMPANY_ID">
            <summary>
            供应商ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_TYPE">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_NAME">
            <summary>
            资料姓名
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_PATH">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_EXPIRY_DATE">
            <summary>
            有效期
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_STATE">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.LAST_MPERSON">
            <summary>
            最后修改人
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.CER_DATA">
            <summary>
            发证日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.CER_WARN_DATE">
            <summary>
            校期提醒日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.CER_WARN_CONTENT">
            <summary>
            校期提醒日期文本
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.UPLOAD_FILE_ID">
            <summary>
            上传文件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.BusinessModuleClient.SMS_COMPANY_FILE.FILE_RECORD">
            <summary>
            附件
            </summary>
        </member>
        <member name="T:XH.H82.Models.Card.EInkChangeDisplayDto">
            <summary>
            水墨屏展示效果
            </summary>
        </member>
        <member name="P:XH.H82.Models.Card.EInkChangeDisplayDto.recordUUID">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Card.EInkChangeDisplayDto.mac">
            <summary>
            mac地址
            </summary>
        </member>
        <member name="P:XH.H82.Models.Card.EInkChangeDisplayDto.isConfigErrorLight">
            <summary>
            是否亮灯
            </summary>
        </member>
        <member name="P:XH.H82.Models.Card.EInkChangeDisplayDto.info">
            <summary>
            数据信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Card.EInkChangeDisplayDto.qrCodeImg">
             <summary>
            二维码  64*64px的
             </summary>
        </member>
        <member name="T:XH.H82.Models.Card.InkScreenDevice">
            <summary>
            水墨屏信息模型
            </summary>
            <param name="Mac">mac地址</param>
            <param name="InkScreenName">水墨屏名称</param>
        </member>
        <member name="M:XH.H82.Models.Card.InkScreenDevice.#ctor(System.String,System.String)">
            <summary>
            水墨屏信息模型
            </summary>
            <param name="Mac">mac地址</param>
            <param name="InkScreenName">水墨屏名称</param>
        </member>
        <member name="P:XH.H82.Models.Card.InkScreenDevice.Mac">
            <summary>mac地址</summary>
        </member>
        <member name="P:XH.H82.Models.Card.InkScreenDevice.InkScreenName">
            <summary>水墨屏名称</summary>
        </member>
        <member name="T:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput">
            <summary>
            提交输入
            </summary>
            <param name="applyType"></param>
            <param name="applyReason"></param>
            <param name="examinePerson"></param>
            <param name="examinePersonId"></param>
            <param name="scrapDate"></param>
        </member>
        <member name="M:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.#ctor(System.String,System.String,System.String,System.String,System.DateTime)">
            <summary>
            提交输入
            </summary>
            <param name="applyType"></param>
            <param name="applyReason"></param>
            <param name="examinePerson"></param>
            <param name="examinePersonId"></param>
            <param name="scrapDate"></param>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.applyType">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.applyReason">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.examinePerson">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.examinePersonId">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitScapOrStopInput.scrapDate">
            <summary></summary>
        </member>
        <member name="T:XH.H82.Models.DeactivationScrapping.SubmitPassInput">
            <summary>
            提交通过通过输入
            </summary>
            <param name="auditPerson"></param>
            <param name="auditPersonId"></param>
        </member>
        <member name="M:XH.H82.Models.DeactivationScrapping.SubmitPassInput.#ctor(System.String,System.String)">
            <summary>
            提交通过通过输入
            </summary>
            <param name="auditPerson"></param>
            <param name="auditPersonId"></param>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitPassInput.auditPerson">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.SubmitPassInput.auditPersonId">
            <summary></summary>
        </member>
        <member name="T:XH.H82.Models.DeactivationScrapping.AuditPassInput">
            <summary>
            审核通过输入
            </summary>
            <param name="approvalPerson"></param>
            <param name="approvalPersonId"></param>
        </member>
        <member name="M:XH.H82.Models.DeactivationScrapping.AuditPassInput.#ctor(System.String,System.String)">
            <summary>
            审核通过输入
            </summary>
            <param name="approvalPerson"></param>
            <param name="approvalPersonId"></param>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.AuditPassInput.approvalPerson">
            <summary></summary>
        </member>
        <member name="P:XH.H82.Models.DeactivationScrapping.AuditPassInput.approvalPersonId">
            <summary></summary>
        </member>
        <member name="T:XH.H82.Models.Demo.UserLearningRecords">
            <summary>
            用户学习记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.UserLearningRecords.PostName">
            <summary>
            岗位名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.UserLearningRecords.PostId">
            <summary>
            岗位Id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.UserLearningRecords.Tasks">
            <summary>
            学习任务列表
            </summary>
        </member>
        <member name="T:XH.H82.Models.Demo.LearningTask">
            <summary>
            学习任务
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.LearningTask.TaskName">
            <summary>
            任务名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.LearningTask.TaskContents">
            <summary>
            任务内容列表
            </summary>
        </member>
        <member name="T:XH.H82.Models.Demo.TaskContent">
            <summary>
            任务内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.TaskContent.State">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.TaskContent.FileName">
            <summary>
            学习文件名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.TaskContent.LearnedTime">
            <summary>
            已学习的时长
            </summary>
        </member>
        <member name="P:XH.H82.Models.Demo.TaskContent.TotalTime">
            <summary>
            总时长
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto">
            <summary>
            预警信息模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto.FaultRecords">
            <summary>
            设备故障列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto.MaintenanceRecords">
            <summary>
            需要提醒的保养设备列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto.CorrectRecords">
            <summary>
             需要提醒的校准设备列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto.ComparisonRecords">
            <summary>
            需要提醒的比对设备列表
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary">
            <summary>
            提醒信息摘要
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.Summary">
            <summary>
            设备当前预警状态(0故障、1需要保养、2需要校准、需要比对)
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.EquipmentId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.EquipmentName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.EquipmentCode">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.EquipmentModel">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentStatusSummary.GroupId">
            <summary>
            专业组Id
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput">
            <summary>
            新增墨水屏信息
            </summary>
            <param name="Mac">mac地址</param>
            <param name="InkScreenName">墨水屏名称</param>
            <param name="hosptialId">医疗机构id</param>
            <param name="Remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            新增墨水屏信息
            </summary>
            <param name="Mac">mac地址</param>
            <param name="InkScreenName">墨水屏名称</param>
            <param name="hosptialId">医疗机构id</param>
            <param name="Remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput.Mac">
            <summary>mac地址</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput.InkScreenName">
            <summary>墨水屏名称</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput.hosptialId">
            <summary>医疗机构id</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput.Remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.UpdateInkScreenInput">
            <summary>
            更新墨水屏信息
            </summary>
            <param name="Remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.UpdateInkScreenInput.#ctor(System.String)">
            <summary>
            更新墨水屏信息
            </summary>
            <param name="Remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.UpdateInkScreenInput.Remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto">
            <summary>
            墨水屏展示模型
            </summary>
            <param name="InkScreenId">墨水屏Id</param>
            <param name="LightState">亮灯状态</param>
            <param name="LightStateValue">亮灯状态中文</param>
            <param name="Mac">MAC地址</param>
            <param name="LineState">在线状态</param>
            <param name="LineStateValue">在线状态中文</param>
            <param name="Power">电量</param>
            <param name="LastPowerCheckPerson">最近一次检测人</param>
            <param name="LastPowerCheckDate">最近一次检时间</param>
            <param name="EquipmentState">设备状态</param>
            <param name="EquipmentStateValue">设备状态中文</param>
            <param name="EquipmentmCode">设备代号</param>
            <param name="GroupName">专业组</param>
            <param name="WarnMsg">报警信息</param>
            <param name="WarnTime">报警开始时间</param>
            <param name="WarnDuration">报警时长</param>
            <param name="Manufacturer">制造商</param>
            <param name="Dealer">供应商</param>
            <param name="InstallLocatin">安装位置</param>
            <param name="EquipmentResponsible">设备负责人</param>
            <param name="DeliveryPath">墨水屏下发时的实际样式</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.#ctor(System.String,XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum,System.String,System.String,XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum,System.String,System.String,System.Int32,System.String,System.Nullable{System.DateTime},System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.DateTime},System.Int32,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            墨水屏展示模型
            </summary>
            <param name="InkScreenId">墨水屏Id</param>
            <param name="LightState">亮灯状态</param>
            <param name="LightStateValue">亮灯状态中文</param>
            <param name="Mac">MAC地址</param>
            <param name="LineState">在线状态</param>
            <param name="LineStateValue">在线状态中文</param>
            <param name="Power">电量</param>
            <param name="LastPowerCheckPerson">最近一次检测人</param>
            <param name="LastPowerCheckDate">最近一次检时间</param>
            <param name="EquipmentState">设备状态</param>
            <param name="EquipmentStateValue">设备状态中文</param>
            <param name="EquipmentmCode">设备代号</param>
            <param name="GroupName">专业组</param>
            <param name="WarnMsg">报警信息</param>
            <param name="WarnTime">报警开始时间</param>
            <param name="WarnDuration">报警时长</param>
            <param name="Manufacturer">制造商</param>
            <param name="Dealer">供应商</param>
            <param name="InstallLocatin">安装位置</param>
            <param name="EquipmentResponsible">设备负责人</param>
            <param name="DeliveryPath">墨水屏下发时的实际样式</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.InkScreenId">
            <summary>墨水屏Id</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LightState">
            <summary>亮灯状态</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LightStateValue">
            <summary>亮灯状态中文</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.Mac">
            <summary>MAC地址</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LineState">
            <summary>在线状态</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LineStateValue">
            <summary>在线状态中文</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.Power">
            <summary>电量</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LastPowerCheckPerson">
            <summary>最近一次检测人</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.LastPowerCheckDate">
            <summary>最近一次检时间</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.EquipmentState">
            <summary>设备状态</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.EquipmentStateValue">
            <summary>设备状态中文</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.EquipmentmCode">
            <summary>设备代号</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.GroupName">
            <summary>专业组</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.WarnMsg">
            <summary>报警信息</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.WarnTime">
            <summary>报警开始时间</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.WarnDuration">
            <summary>报警时长</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.Manufacturer">
            <summary>制造商</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.Dealer">
            <summary>供应商</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.InstallLocatin">
            <summary>安装位置</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.EquipmentResponsible">
            <summary>设备负责人</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto.DeliveryPath">
            <summary>墨水屏下发时的实际样式</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto">
            <summary>
            墨水屏-设备列表
            </summary>
            <param name="EquipmentId"> 设备id </param>
            <param name="EquipmentName"> 设备名称 </param>
            <param name="EquipmentCode"> 设备代号 </param>
            <param name="EquipmentModel"> 设备型号 </param>
            <param name="GroupName"> 专业组名 </param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.#ctor(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            墨水屏-设备列表
            </summary>
            <param name="EquipmentId"> 设备id </param>
            <param name="EquipmentName"> 设备名称 </param>
            <param name="EquipmentCode"> 设备代号 </param>
            <param name="EquipmentModel"> 设备型号 </param>
            <param name="GroupName"> 专业组名 </param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.EquipmentId">
            <summary> 设备id </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.EquipmentName">
            <summary> 设备名称 </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.EquipmentCode">
            <summary> 设备代号 </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.EquipmentModel">
            <summary> 设备型号 </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto.GroupName">
            <summary> 专业组名 </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto">
            <summary>
            历史报警信息
            </summary>
            <param name="EquipmentId">设备id</param>
            <param name="WarnRecordId">报警记录id</param>
            <param name="WarnMsg">报警信息</param>
            <param name="WarnTime">报警时间</param>
            <param name="DealWithPerpon">处理人</param>
            <param name="DealwithTime">处理时间</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.#ctor(System.String,System.String,System.String,XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum,System.DateTime,System.String,System.Nullable{System.DateTime},XH.H82.Models.DeviceRelevantInformation.Enum.DealWithStateEnum)">
            <summary>
            历史报警信息
            </summary>
            <param name="EquipmentId">设备id</param>
            <param name="WarnRecordId">报警记录id</param>
            <param name="WarnMsg">报警信息</param>
            <param name="WarnTime">报警时间</param>
            <param name="DealWithPerpon">处理人</param>
            <param name="DealwithTime">处理时间</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.EquipmentId">
            <summary>设备id</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.WarnRecordId">
            <summary>报警记录id</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.WarnMsg">
            <summary>报警信息</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.WarnTime">
            <summary>报警时间</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.DealWithPerpon">
            <summary>处理人</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto.DealwithTime">
            <summary>处理时间</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto">
            <summary>
            墨水屏绑定信息列表
            </summary>
            <param name="InkScreenId">墨水屏id</param>
            <param name="BandStateValue">绑定状态</param>
            <param name="SortNo">排序号</param>
            <param name="Mac">mac地址</param>
            <param name="EquipmentCode">设备代号</param>
            <param name="GroupName">专业组</param>
            <param name="EquipmentId">设备id</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            墨水屏绑定信息列表
            </summary>
            <param name="InkScreenId">墨水屏id</param>
            <param name="BandStateValue">绑定状态</param>
            <param name="SortNo">排序号</param>
            <param name="Mac">mac地址</param>
            <param name="EquipmentCode">设备代号</param>
            <param name="GroupName">专业组</param>
            <param name="EquipmentId">设备id</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.InkScreenId">
            <summary>墨水屏id</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.BandStateValue">
            <summary>绑定状态</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.SortNo">
            <summary>排序号</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.Mac">
            <summary>mac地址</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.EquipmentCode">
            <summary>设备代号</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.GroupName">
            <summary>专业组</summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto.EquipmentId">
            <summary>设备id</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Dto.BindInkScreen">
             <summary>
            设备绑定标识
             </summary>
             <param name="EquipmentId">设备id</param>
        </member>
        <member name="M:XH.H82.Models.DeviceRelevantInformation.Dto.BindInkScreen.#ctor(System.String)">
             <summary>
            设备绑定标识
             </summary>
             <param name="EquipmentId">设备id</param>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.Dto.BindInkScreen.EquipmentId">
            <summary>设备id</summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.DealWithStateEnum">
            <summary>
            报警信息处理状态
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.DealWithStateEnum.Untreated">
            <summary>
            未处理
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.DealWithStateEnum.pProcessed">
            <summary>
            已处理
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum.NotEnabled">
            <summary>
            未启用
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum.Normal">
            <summary>
            启用
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum.Deactivated">
            <summary>
            停用
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum.Scrapped">
            <summary>
            报废
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum">
            <summary>
            设备报警信息(0故障、1需要保养、2需要校准、需要比对)
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum.Fault">
            <summary>
            故障
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum.Maintenance">
            <summary>
            需要保养
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum.Correct">
            <summary>
            需要校准
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum.Comparison">
            <summary>
            需要比对
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentSummaryEnum.Certificate">
            <summary>
            需要更新证书
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum">
            <summary>
            亮灯状态
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum.lightsOut">
            <summary>
            熄灯
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum.Lighting">
            <summary>
            亮灯
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum">
            <summary>
            墨水屏幕线上状态
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum.OutLine">
            <summary>
            离线
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum.OnLine">
            <summary>
            在线
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.Enum.BindState">
            <summary>
            墨水屏与设备的绑定状态
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.BindState.UnBind">
            <summary>
            未绑定
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.Enum.BindState.Bind">
            <summary>
            已绑定
            </summary>
        </member>
        <member name="T:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay">
            <summary>
            设备展示信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.Id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.EquipmentId">
            <summary>
            设备id
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.State">
            <summary>
            设备当前状态（中文）
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.ChangeStateTime">
            <summary>
            设备状态变更时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.MaintenanceType">
            <summary>
            保养类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.MaintenanceDate">
            <summary>
            保养预警多少天
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.MaintenanceState">
            <summary>
            提醒消息的状态   告警|预警|正常
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.CorrectDate">
            <summary>
            校预警多少天
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.CorrectState">
            <summary>
            提醒消息的状态   告警|预警|正常
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.ComparisonDate">
            <summary>
            比对预警多少天
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.ComparisonState">
            <summary>
            提醒消息的状态   告警|预警|正常
            </summary>
        </member>
        <member name="F:XH.H82.Models.DeviceRelevantInformation.EquipmentDisplay.Certificates">
            <summary>
            设备证书类型警告
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.CertificateWarn.CertificateName">
            <summary>
            证书名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.CertificateWarn.CertificateStatus">
            <summary>
            证书有效期状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.CertificateWarn.CertificateType">
            <summary>
            证书类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.CertificateWarn.CertificateDate">
            <summary>
            预警多少天
            </summary>
        </member>
        <member name="P:XH.H82.Models.DeviceRelevantInformation.CertificateWarn.CertificateState">
            <summary>
            提醒消息的状态   告警|预警|正常
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput">
            <summary>
            
            </summary>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput.#ctor(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput.Content">
            <summary>字典内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput.Remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto">
            <summary>
            授权记录添加input
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto.AUTHORIZE_PERSON_ID">
            <summary>
            授权人ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto.AUTHORIZED_PERSON">
            <summary>
            被授权人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto.AUTHORIZED_PERSON_IDS">
            <summary>
            被授权人ids ;分割
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto.AUTHORIZED_ROLE">
            <summary>
            授权权限
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto.AUTHORIZE_DATE">
            <summary>
            授权时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Base.ResultLogin.ResponseContent">
            <summary>
            返回内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Base.ResultLogin.ResponseStatus">
            <summary>
            返回状态
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Base.ResponseStatus">
            <summary>
            返回值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Base.ResponseStatus.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Base.ResponseStatus.Message">
            <summary>
            错误内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Base.ResponseStatus.Success">
            <summary>
            返回成功
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.EQUIPMENT_STATE">
            <summary>
            设备状态：0-未启用；1-启用；2-停用 3-报废
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.EQ_IN_PERSON">
            <summary>
            设备负责人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.EQUIPMENT_STATE_NAME">
            
            <summary>
            设备状态：0-未启用；1-启用；2-停用 3-报废
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.LAST_CORRECT">
            <summary>
             上次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.NEXT_CORRECT">
            <summary>
             下次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.DEALER_REPAIR_PERSON">
            <summary>
             经销商维修人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.MANUFACTURER_REPAIR_PERSON">
            <summary>
             制造商维修人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.E_TIME">
            <summary>
             启用时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.LAST_CORRECT_DEPT">
            <summary>
            上次校准检定结果
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.LAST_CORRECT_RESULT">
            <summary>
            上次校准检定单位
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.GROUP_ID">
            <summary>
            检验分组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.GROUP_NAME">
            <summary>
            检验分组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.DEALER_PHONE">
            <summary>
            供应商电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.MANUFACTURER_PHONE">
            <summary>
            制造商电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.DEBUG_ENGINEER">
            <summary>
            调试工程师
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.DEBUG_ENGINEER_PHONE">
            <summary>
            调试工程师电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.INSTALL_ENGINEER">
            <summary>
            安装工程师
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.INSTALL_ENGINEER_PHONE">
            <summary>
            安装工程师电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CardElementDto.VALID_CORRECT_DATE">
            <summary>
            校正有效期
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.CardTypeDto">
            <summary>
            标识卡模板类型
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Certificate.AttachmentDto">
            <summary>
            附件Dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.UploadFileId">
            <summary>
            附件id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.Path">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.PreviewPath">
            <summary>
            预览路径
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.FileSuffix">
            <summary>
            文件名后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.DocType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.AttachmentDto.CertificateId">
            <summary>
            证书信息ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.Id">
            <summary>
            id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CompanyId">
            <summary>
            供应商ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CompanyName">
            <summary>
            供应商名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CertificateTypeName">
            <summary>
            证书类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.Day">
            <summary>
            天数（长期为0） 正常、临期则为剩余多少天过期   超期则为超期天数  报废直接忽略
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CertificateStatus">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.ExpiryDate">
            <summary>
            过去时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CerDate">
            <summary>
            发证日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.CerWarnDate">
            <summary>
            提醒时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.Attachments">
            <summary>
            证书附件
            </summary>
        </member>
        <member name="M:XH.H82.Models.Dtos.Certificate.CompanyCertificatDto.#ctor(System.String,XH.H82.Models.BusinessModuleClient.CertificatDto)">
            <summary>
            构造函数
            </summary>
            <param name="dto"></param>
        </member>
        <member name="T:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto">
            <summary>
            证书Dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.Id">
            <summary>
            证书Id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.CertificateType">
            <summary>
            证书类型Id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.CertificateTypeName">
            <summary>
            证书类型名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.CertificateStatus">
            <summary>
            证书现在的状态（长期、废用、临近过期、停用）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.ExpiryDate">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.CerDate">
            <summary>
            发证日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.CerWarnDate">
            <summary>
            提醒时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.Days">
            <summary>
            对象状态的到截至日期的天数，如果是长期的证书则为 0 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.Attachments">
            <summary>
            附件列表
            </summary>
        </member>
        <member name="M:XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto.Create(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Boolean)">
            <summary>
            静态工厂创建方法
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput">
            <summary>
            创建设备正式信息输入
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput.CretificateType">
            <summary>
            证书类型ID（DATA_ID ）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput.ExpiryDate">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput.CerDate">
            <summary>
            发证时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput.CerWanrDate">
            <summary>
            提醒时间
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Change.ChangeInput">
            <summary>
            变更记录入参表单模型
            </summary>
            <param name="CHANGE_DATE">变更日期</param>
            <param name="CHANGE_PERSON">变更人员</param>
            <param name="CHANGE_CONTENT">变更内容</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Change.ChangeInput.#ctor(System.Nullable{System.DateTime},System.String,System.String,System.String)">
            <summary>
            变更记录入参表单模型
            </summary>
            <param name="CHANGE_DATE">变更日期</param>
            <param name="CHANGE_PERSON">变更人员</param>
            <param name="CHANGE_CONTENT">变更内容</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Change.ChangeInput.CHANGE_DATE">
            <summary>变更日期</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Change.ChangeInput.CHANGE_PERSON">
            <summary>变更人员</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Change.ChangeInput.CHANGE_CONTENT">
            <summary>变更内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Change.ChangeInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.ClaimsDto">
            <summary>
            token 解析后的对象
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.USER_NO">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.INSTANCE_ID">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.HOSPITAL_ID">
            <summary>
            机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.HOSPITAL_CNAME">
            <summary>
            机构名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.MGROUP_ID">
            <summary>
            专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.LOGID">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.USER_NAME">
            <summary>
            用户名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.MANAGE_CLASS">
            <summary>
            用户类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.TECH_POST">
            <summary>
            职称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.POWER">
            <summary>
            职务
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.PHONE_NO">
            <summary>
            手机
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.DISPLAY_NAME">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto.TOKENGUID">
            <summary>
            请求guid
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.ClaimsDto_Local">
            <summary>
            token 解析后的对象
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ClaimsDto_Local.USER_NO">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Comparison.ComparisonInput">
            <summary>
            比对记录入参模型
            </summary>
            <param name="COMPARISON_DATE">比对日期</param>
            <param name="COMPARISON_OBJECT">比对对象</param>
            <param name="COMPARISON_RESULT">比对结果</param>
            <param name="COMPARISON_PERSON">操作人</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Comparison.ComparisonInput.#ctor(System.Nullable{System.DateTime},System.String,System.String,System.String,System.String)">
            <summary>
            比对记录入参模型
            </summary>
            <param name="COMPARISON_DATE">比对日期</param>
            <param name="COMPARISON_OBJECT">比对对象</param>
            <param name="COMPARISON_RESULT">比对结果</param>
            <param name="COMPARISON_PERSON">操作人</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Comparison.ComparisonInput.COMPARISON_DATE">
            <summary>比对日期</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Comparison.ComparisonInput.COMPARISON_RESULT">
            <summary>比对结果</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Comparison.ComparisonInput.COMPARISON_PERSON">
            <summary>操作人</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Comparison.ComparisonInput.COMPARISON_OBJECT">
            <summary>比对对象</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Comparison.ComparisonInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.No">
            <summary>
            No 序号  也是前端渲染的主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.ReminderType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.EquipmentName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.SmblClass">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.EquipmentModel">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.Content">
            <summary>
            内容   特殊事件类型的才有值  优先显示特殊事件
            </summary>
            <returns></returns>
        </member>
        <member name="P:XH.H82.Models.Dtos.ConutReminderDto.Status">
            <summary>
            状态没有超期  只有特殊事件   待执行 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CorrectArchivingDto.CORRECT_DATE">
            <summary>
            校准日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CorrectArchivingDto.ISSUE_SOURCE">
            <summary>
            归档来源
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CorrectArchivingDto.ISSUE_FILES">
            <summary>
            归档附件
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Correct.CorrectInput">
            <summary>
            校准记录表单模型
            </summary>
            <param name="CORRECT_DATE">校准日期</param>
            <param name="CORRECT_DEPT">校准部门</param>
            <param name="CORRECT_PERSON">校准人员</param>
            <param name="CORRECT_RESULT">校准结果</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Correct.CorrectInput.#ctor(System.Nullable{System.DateTime},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            校准记录表单模型
            </summary>
            <param name="CORRECT_DATE">校准日期</param>
            <param name="CORRECT_DEPT">校准部门</param>
            <param name="CORRECT_PERSON">校准人员</param>
            <param name="CORRECT_RESULT">校准结果</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.CORRECT_DATE">
            <summary>校准日期</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.CORRECT_DEPT">
            <summary>校准部门</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.CORRECT_PERSON">
            <summary>校准人员</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.CORRECT_RESULT">
            <summary>校准结果</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.OCCUR_EVENT">
            <summary>产生事件</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Correct.CorrectInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.No">
            <summary>
            No
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.SbmlLabName">
            <summary>
            备案实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.SmblClassName">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.EquipmentName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.LastInspectionTime">
            <summary>
            上次年检时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.NextInspectionTime">
            <summary>
            下次年检时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.CountWorkPlanDto.Cycle">
            <summary>
            周期
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.DecontaminationFileDto">
            <summary>
            去污归档记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DecontaminationFileDto.DATE">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DecontaminationFileDto.ISSUE_SOURCE">
            <summary>
            归档来源
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DecontaminationFileDto.ISSUE_FILES">
            <summary>
            归档附件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.FILE_ID">
            <summary>
            文件主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_ID">
            <summary>
            文档id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.PGROUP_NAME">
            <summary>
            专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.PGROUP_ID">
            <summary>
            专业组id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_NAME">
            <summary>
            文档名称
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_TYPE">
            <summary>
            文档类型
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_CODE">
            <summary>
            文档编号
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_VERSION">
            <summary>
            版本号
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.DOC_KEYWORD">
            <summary>
            关键词
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.DocInfoDto.IF_SELECT">
            <summary>
            是否选中（0：未选；1：已选）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.Id">
            <summary>
            操作记录ID
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.InitiatorState">
            <summary>
            流程状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.InitiatorStateValue">
            <summary>
            流程状态中文
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.InitiatorName">
            <summary>
            流程发起人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.InitiatorDate">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.Content">
            <summary>
            处理内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.BaseCirculationRecordDto.ForwardId">
            <summary>
            操作记录ID
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.EmsWorkPlanCirculationRecordDto">
            <summary>
            流程处理记录Dto
            </summary>
            
        </member>
        <member name="P:XH.H82.Models.Dtos.EntranceEquipment.IsIn">
            <summary>
            门禁进出方向
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.CONTACT_NAME">
            <summary>
            经销商联系人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.PHONE_NO">
            <summary>
            经销商联系电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.ListPath">
            <summary>
            外观信息图片地址
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.EQ_OUT_TIME">
            <summary>
            出厂日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.DEPRECIATION_TIME">
            <summary>
            折旧日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.EQUIPMENT_CLASS_NAME">
            <summary>
            设备分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.IS_HIDE">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.EQ_SERVICE_LIFE">
            <summary>
            使用年限
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.SMBL_FLAG">
            <summary>
            生安标识
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.SMBL_LAB_ID">
            <summary>
            备案实验室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.SMBL_LAB_NAME">
            <summary>
            备案实验室
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.SMBL_CLASS">
            <summary>
            生安设备类型id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.SMBL_STATE">
            <summary>
            生安状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentInfoDto.EQUIPMENT_STAT_SBML">
            <summary>
            生安标识+设备状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.Id">
            <summary>
            设备 id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.Name">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.SmblLabName">
            <summary>
            实验室名称
            </summary>
            <returns></returns>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.SmblClass">
            <summary>
            生物安全设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.LastYearCheckTime">
            <summary>
            上次年检时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckDto.NextYearCheckTime">
            <summary>
            下次年检时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckCountDto.Year">
            <summary>
            年份
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EquipmentYearCheckCountDto.Count">
            <summary>
             数目
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.SECTION_NO">
            <summary>
            设备科编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.DEPT_SECTION_NO">
            <summary>
            科室设备科编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.SERIAL_NUMBER">
            <summary>
            设备序列号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.ENABLE_TIME">
            <summary>
            首次启用日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.EQUIPMENT_NUM">
            <summary>
            设备序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.MGROUP_NAME">
            <summary>
            所属专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.PROFESSIONAL_ClASS">
            <summary>
            专业分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.MANUFACTURER">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.DEALER">
            <summary>
            经销商
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.INSTALL_DATE">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.INSTALL_AREA">
            <summary>
            安装位置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.KEEP_PERSON">
            <summary>
            保管人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.CONTACT_PHONE">
            <summary>
            联系方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.ExportEquipmentDto.REGISTRATION_NUM">
            <summary>
            注册证号
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto">
            <summary>
            OnlyOffice模版样式批量添加  STYLE_INFO
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.LAB_PGROUP_TYPE">
            <summary>
            所属类型 0科室 1专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.STYLE_CLASS_CODE">
            <summary>
            模版样式类型 提供给业务模块做多种分类样式表过滤
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.STYLE_NAME">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.DATA_ID">
            <summary>
            来源数据ID  提供给业务模块冗余用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.STYLE_STATE">
            <summary>
            状态;0禁用1启用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateDto.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateBatchCreateFileDto">
            <summary>
            OnlyOffice模版样式批量添加  (多个模板信息+单个文件)
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto">
            <summary>
            OnlyOffice模版样式记录表 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.STYLE_ID">
            <summary>
            样式ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.LAB_PGROUP_TYPE">
            <summary>
            所属类型 0科室 1专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.CATALOG_NAME">
            <summary>
            所属目录名（只有一级目录）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.STYLE_CLASS_CODE">
            <summary>
            模版样式类型 提供给业务模块做多种分类样式表过滤
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.FILE_ID">
            <summary>
            文件上传ID OA_UPLOAD_FILE的FILE_ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.STYLE_NAME">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.DATA_ID">
            <summary>
            来源数据ID  提供给业务模块冗余用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.STYLE_STATE">
            <summary>
            状态;0禁用1启用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OaOfficeStyleTemplateDto.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto">
            <summary>
            文件转换上传类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.DIR_NAME">
            <summary>
            文件目录 规则是  MODULE_ID/DIR_NAME  或者 ROOT_PATH/DIR_NAME
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.IF_USE_UUID_AS_FILE_NAME">
            <summary>
            是否使用UUID作为文件命名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.IF_ADD_WATER_MARK">
            <summary>
            是否添加水印
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.FILE">
            <summary>
            采用multipart/form-data方式上传文件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.IF_CONVERT_PDF">
            <summary>
            是否转换成pdf
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.ORIGIN_TO_S28">
            <summary>
            原始文件是否也上传上S28
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.ADDN_REMARK">
            <summary>
            扩展信息字段  用于业务模块需要做些啥后续操作
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.IF_GET_COVER_IMG">
            <summary>
            是否获取封面base64字符串,仅在最终上传的是pdf文件的情况下才有
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.OOFileConvertPdfDto.COVER_IMG_TO_S28">
            <summary>
            封面图片是否也存到S28(如果上传S28,则),仅在最终上传的是pdf文件的情况下才有
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.PreviewTemplateDto">
            <summary>
            预览模板文件dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.PreviewTemplateDto.TemplateStream">
            <summary>
            模板文件流
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.PreviewTemplateDto.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.FileTemplate.StyleTemplateClassDataDto">
            <summary>
            模板字段数据DTO
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.StyleTemplateClassDataDto.CLASSE_CODE">
            <summary>
            类型ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.StyleTemplateClassDataDto.FIELDS">
            <summary>
            单字段数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.StyleTemplateClassDataDto.ARRAYS">
            <summary>
            列表数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.StyleTemplateFillDataDto.STYLE_ID">
            <summary>
            模板ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.FileTemplate.StyleTemplateFillDataDto.DATAS">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_CYCLE">
            <summary>
            使用周期;0 日/次  1月
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_PERSON">
            <summary>
            登记人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_DATA">
            <summary>
            使用时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_CONTEXT">
            <summary>
            登记内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_STATE">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Implement.ImplementDto.IMPLEMENT_SOURCE">
            <summary>
            数据来源   0 自己   1 监测   2 事务项
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.IssueFileDto">
            <summary>
            使用归档
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileDto.DISPLAY_TYPE">
            <summary>
            展示类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileDto.IssueFileInfo">
            <summary>
            归档附件信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ARCHIVING_TIME">
            <summary>
            保存时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ISSUE_FILE">
            <summary>
            文件地址
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ISSUE_SOURCE">
            <summary>
            附件来源（equipment：设备程序；tim：事务项归档）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ISSUE_TYPE">
            <summary>
            归档类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.OPER_PERSON">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ISSUE_NAME">
            <summary>
            附件名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.ISSUE_ID">
            <summary>
            附件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.IssueFileInfo.FILE_TYPE">
            <summary>
            附件类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.MaintainFormDto.MAINTAIN_CYCLE">
            <summary>
            保养周期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.MaintainFileDto.MAINTAIN_DATE">
            <summary>
            保养日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.MaintainFileDto.MAINTAIN_PERSON">
            <summary>
            保养人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.MaintainFileDto.MAINTAIN_FILES">
            <summary>
            保养附件
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Maintain.MaintainInput">
            <summary>
            保养记录入参模型
            </summary>
            <param name="MAINTAIN_CYCLE">保养周期</param>
            <param name="MAINTAIN_DATE">保养时间</param>
            <param name="MAINTAIN_PERSON">保养人员</param>
            <param name="MAINTAIN_CONTENT">保养内容</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Maintain.MaintainInput.#ctor(System.String,System.Nullable{System.DateTime},System.String,System.String,System.String,System.String)">
            <summary>
            保养记录入参模型
            </summary>
            <param name="MAINTAIN_CYCLE">保养周期</param>
            <param name="MAINTAIN_DATE">保养时间</param>
            <param name="MAINTAIN_PERSON">保养人员</param>
            <param name="MAINTAIN_CONTENT">保养内容</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.MAINTAIN_CYCLE">
            <summary>保养周期</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.MAINTAIN_DATE">
            <summary>保养时间</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.MAINTAIN_PERSON">
            <summary>保养人员</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.MAINTAIN_CONTENT">
            <summary>保养内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.OCCUR_EVENT">
            <summary>产生事件</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Maintain.MaintainInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewEquipClassTreeDto">
            <summary>
            设备分类树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.AREA_NAME">
            <summary>
            院区名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.TOTAL_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.TOTAL_SCRAP_AMOUNT">
            <summary>
            报废停用数量
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.TOTAL_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.SELF_PEND">
            <summary>
            本人待处理
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.SELF_PGROUP_PEND">
            <summary>
            本实验室待处理
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTreeDto.SecondStageTree">
            <summary>
            二级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewEquipClassTree">
            <summary>
            设备分类结点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.Num">
            <summary>
            自然增长数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.EQUIPMENT_CLASS_NAME">
            <summary>
            设备分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.EQUIPCLASS_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.EQUIPCLASS_SCRAP_AMOUNT">
            <summary>
            报废总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.EQUIPCLASS_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipClassTree.ThirdStageTree">
            <summary>
            三级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewClassMgroupTree">
            <summary>
            管理专业组结点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.Num">
            <summary>
            自然增长数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.MGROUP_ID">
            <summary>
            管理专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.MGROUP_NAME">
            <summary>
            管理专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.MGROUP_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.MGROUP_SCRAP_AMOUNT">
            <summary>
            报废总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.MGROUP_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassMgroupTree.FourthStageTree">
            <summary>
            四级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewClassPgroupTree">
            <summary>
            检验专业组结点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.Num">
            <summary>
            自然增长数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.PGROUP_NAME">
            <summary>
            检验专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.PGROUP_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.PGROUP_SCRAP_AMOUNT">
            <summary>
            报废总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.PGROUP_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassPgroupTree.FifthStageTree">
            <summary>
            五级树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.Num">
            <summary>
            自然增长数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.EQUIPMENT_STATE">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.IS_HIDE">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewClassEquipmentTree.SixthStageTree">
            <summary>
            六级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewOATreeDto">
            <summary>
            实验室列表树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.AREA_NAME">
            <summary>
            院区名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.TOTAL_SUBSCRIBE_AMOUNT">
            <summary>
            申购总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.TOTAL_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.TOTAL_SCRAP_AMOUNT">
            <summary>
            报废停用总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.SELF_PEND">
            <summary>
            本人待处理
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.SELF_PGROUP_PEND">
            <summary>
            本实验室待处理
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.TOTAL_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewOATreeDto.SecondStageTree">
            <summary>
            二级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewMgroupTreeDto">
            <summary>
            管理专业组树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_ID">
            <summary>
            管理专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_NAME">
            <summary>
            管理专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_SUBSCRIBE_AMOUNT">
            <summary>
            申购总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_SCRAP_AMOUNT">
            <summary>
            报废停用总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.MGROUP_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewMgroupTreeDto.ThirdStageTree">
            <summary>
            三级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewPgroupTreeDto">
            <summary>
            检验专业组树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_NAME">
            <summary>
            检验专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_SUBSCRIBE_AMOUNT">
            <summary>
            申购总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_EQUIPMENT_AMOUNT">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_SCRAP_AMOUNT">
            <summary>
            报废停用总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.PGROUP_INSTRUMENT_AMOUNT">
            <summary>
            检测仪器总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewPgroupTreeDto.FourthStageTree">
            <summary>
            四级树
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.NewEquipmentTreeDto">
            <summary>
            设备树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.EQUIPMENT_STATE">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.IS_HIDE">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.IsInstrument">
            <summary>
            是否仪器
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.NewEquipmentTreeDto.FifthStageTree">
            <summary>
            五级树
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.PgroupPipelineDto.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.PgroupPipelineDto.PGROUP_NAME">
            <summary>
            检验专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.PipelineDto.PIPELINE_ID">
            <summary>
            流水线ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.PipelineDto.PIPELINE_NAME">
            <summary>
            流水线名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RelationEventDto.RELATION_NO">
            <summary>
            关联单号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RelationEventDto.RELATION_PERSON">
            <summary>
            关联人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RelationEventDto.RELATION_CONTENT">
            <summary>
            关联内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RepairArchivingDto.REPAIR_DATE">
            <summary>
            维修日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RepairArchivingDto.ISSUE_SOURCE">
            <summary>
            归档来源
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.RepairArchivingDto.ISSUE_FILES">
            <summary>
            归档附件
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Repair.RepairInput">
            <summary>
            维修记录入参模型
            </summary>
            <param name="REPAIR_DATE">维修事件</param>
            <param name="REPAIR_PERSON">维修人员</param>
            <param name="REPAIR_CONTENT">维修内容</param>
            <param name="REPAIR_RESULT">维修结果</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Repair.RepairInput.#ctor(System.Nullable{System.DateTime},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            维修记录入参模型
            </summary>
            <param name="REPAIR_DATE">维修事件</param>
            <param name="REPAIR_PERSON">维修人员</param>
            <param name="REPAIR_CONTENT">维修内容</param>
            <param name="REPAIR_RESULT">维修结果</param>
            <param name="OCCUR_EVENT">产生事件</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.REPAIR_DATE">
            <summary>维修事件</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.REPAIR_PERSON">
            <summary>维修人员</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.REPAIR_CONTENT">
            <summary>维修内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.REPAIR_RESULT">
            <summary>维修结果</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.OCCUR_EVENT">
            <summary>产生事件</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Repair.RepairInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.StartTemplateDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.StartTemplateDto.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.SysDocDtos.DocumentSystemDto">
            <summary>
            链接文件系统弹窗信息模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.SysDocDtos.DocumentSystemDto.Nodes">
            <summary>
            科室/专业组结构节点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.SysDocDtos.DocumentSystemDto.Files">
            <summary>
            列表文件数据
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.SysDocDtos.FileSystemFileInfo">
            <summary>
            文件系统中的文件对象
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.SysDocDtos.SOPType">
            <summary>
            SOP类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_ID">
            <summary>
            执行记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WORK_MAINID">
            <summary>
            主体ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WORK_MAINNAME">
            <summary>
            事务主体名称/设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.FORM_VER_MAIN_ID">
            <summary>
            记录单版本ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.FORM_VER_NO">
            <summary>
            记录单版本
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WORK_ID">
            <summary>
            事务ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WORK_NAME">
            <summary>
            事务名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.CLASS_ID">
            <summary>
            记录单分类ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.CLASS_NAME">
            <summary>
            记录单分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.RECORD_TIME">
            <summary>
            填写时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.FORM_ID">
            <summary>
            记录单ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.FORM_NAME">
            <summary>
            记录单名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_PERSON">
            <summary>
            执行人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_RESULT">
            <summary>
            执行结果
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_TIME">
            <summary>
            执行时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.RECORD_PERSON">
            <summary>
            填写人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_DATE">
            <summary>
            执行日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WORK_PLAN_TYPE">
            <summary>
            保养类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.ExecRecord.WEXEC_STATE">
            <summary>
            执行状态
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Tim.TransactionForm">
            <summary>
            记事务录单
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.FORM_ID">
            <summary>
            记录单ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.FORM_NAME">
            <summary>
            记录单名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.CLASS_ID">
            <summary>
            记录单分类Id  
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.FORM_VER_ID">
            <summary>
            记录单版本id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.CLASS_NAME">
            <summary>
            记录单分名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionForm.WORK_MAINID">
            <summary>
            事务项主体ID/设备id
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Tim.TransactionItem">
            <summary>
            事务项
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionItem.WORK_ID">
            <summary>
            pk
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionItem.WORK_NAME">
            <summary>
            事务名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionItem.WORK_PLAN_TYPE">
            <summary>
            计划类型 1周保养2月保养3季保养4年保养
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionItem.TRANSACTION_ITEM_CLASS">
            <summary>
            事务计划类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tim.TransactionItem.FORM_VER_ID">
            <summary>
            记录单版本id
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Transaction.AddContentDictInput">
            <summary>
            
            </summary>
            <param name="ClassId">记录单类型  "保养记录、使用记录、校准记录、比对记录、维修记录、性能验证记录"</param>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Transaction.AddContentDictInput.#ctor(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="ClassId">记录单类型  "保养记录、使用记录、校准记录、比对记录、维修记录、性能验证记录"</param>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Transaction.AddContentDictInput.ClassId">
            <summary>记录单类型  "保养记录、使用记录、校准记录、比对记录、维修记录、性能验证记录"</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Transaction.AddContentDictInput.Content">
            <summary>字典内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Transaction.AddContentDictInput.Remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Transaction.UpdateContentDictInput">
            <summary>
            
            </summary>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Transaction.UpdateContentDictInput.#ctor(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="Content">字典内容</param>
            <param name="Remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Transaction.UpdateContentDictInput.Content">
            <summary>字典内容</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Transaction.UpdateContentDictInput.Remark">
            <summary>备注</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeId">
            <summary>
            数据主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeName">
            <summary>
            节点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeState">
            <summary>
            节点状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeStateName">
            <summary>
            节点状态名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeType">
            <summary>
            节点类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Tree.OrganizationalTreeNode.NodeTypeName">
            <summary>
            节点类型中文
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadFileDto.DOC_CLASS">
            <summary>
            设备文档分类(如：申购信息)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadFileDto.DOC_INFO_ID">
            <summary>
            设备信息ID（如：申购ID）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadFileDto.DOC_NAME">
            <summary>
            文档名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadFileDto.DOC_SUFFIX">
            <summary>
            文档后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadFileDto.FILE">
            <summary>
            multipart/form-data 文件上传信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadStagingAreaFileDto.DOC_CLASS">
            <summary>
            设备文档分类(如：申购信息)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadStagingAreaFileDto.DOC_NAME">
            <summary>
            文档名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadStagingAreaFileDto.DOC_SUFFIX">
            <summary>
            文档后缀
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.UploadStagingAreaFileDto.FILE">
            <summary>
            multipart/form-data 文件上传信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Dtos.Verification.VerificationInput">
            <summary>
            性能验证表单入参模型
            </summary>
            <param name="VERIFICATION_DATE">验证日期</param>
            <param name="VERIFICATION_PERSON">验证人员</param>
            <param name="VERIFICATION_RESULT">验证结果</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="M:XH.H82.Models.Dtos.Verification.VerificationInput.#ctor(System.Nullable{System.DateTime},System.String,System.String,System.String)">
            <summary>
            性能验证表单入参模型
            </summary>
            <param name="VERIFICATION_DATE">验证日期</param>
            <param name="VERIFICATION_PERSON">验证人员</param>
            <param name="VERIFICATION_RESULT">验证结果</param>
            <param name="REMARK">备注</param>
        </member>
        <member name="P:XH.H82.Models.Dtos.Verification.VerificationInput.VERIFICATION_DATE">
            <summary>验证日期</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Verification.VerificationInput.VERIFICATION_PERSON">
            <summary>验证人员</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Verification.VerificationInput.VERIFICATION_RESULT">
            <summary>验证结果</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.Verification.VerificationInput.REMARK">
            <summary>备注</summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.WorkPlanDto.SUBMIT_TIME">
            <summary>
            提交时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.WorkPlanDto.CURRENT_STATE">
            <summary>
            计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.WorkPlanDto.CURRENT_STATE_NAME">
            <summary>
            计划状态中文
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.WORK_PLAN_ID">
            <summary>
            工作计划主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.EQUIPMENT_ID">
            <summary>
            设备主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.CURRENT_STATE_NAME">
            <summary>
            计划状态中文
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.CURRENT_STATE">
            <summary>
            计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.MAINTAIN_INTERVALS">
            <summary>
            周保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.MAINTAIN_WARN_INTERVALS">
            <summary>
            周保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_MAINTAIN_DATE">
            <summary>
            最近一次周保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_MAINTAIN_DATE">
            <summary>
            下一次周保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.MONTHLY_MAINTAIN">
            <summary>
            月保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.MONTHLY_MAINTAIN_WARN">
            <summary>
            月保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_MONTHLY_MAINTAIN_DATE">
            <summary>
            最近一次月保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_MONTHLY_MAINTAIN_DATE">
            <summary>
            下一次月保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.QUARTERLY_MAINTAIN">
            <summary>
            季度保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.QUARTERLY_MAINTAIN_WARN">
            <summary>
            季度保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_QUARTERLY_MAINTAIN_DATE">
            <summary>
            最近一次季度保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_QUARTERLY_MAINTAIN_DATE">
            <summary>
            下一次季度保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.YEARLY_MAINTAIN">
            <summary>
            年保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.YEARLY_MAINTAIN_WARN">
            <summary>
            年保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_YEARLY_MAINTAIN_DATE">
            <summary>
            最近一次年保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_YEARLY_MAINTAIN_DATE">
            <summary>
            下一次年保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.CORRECT_INTERVALS">
            <summary>
            校准周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.CORRECT_WARN_INTERVALS">
            <summary>
            校准提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_CORRECT_INTERVALS_DATE">
            <summary>
            最近一次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_CORRECT_INTERVALS_DATE">
            <summary>
            下一次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.CORRECT_UNIT">
            <summary>
            校准单位
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.EQ_IN_PERSON">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.COMPARISON_INTERVALS">
            <summary>
            比对周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.COMPARISON_WARN_INTERVALS">
            <summary>
            比对提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.LAST_COMPARISON_INTERVALS_DATE">
            <summary>
            最近一次比对时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.NEXT_COMPARISON_INTERVALS_DATE">
            <summary>
            下一次比对时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.OPER_PERSON">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.OPER_TIME">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.AUDITOR_USER_NAME">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.AUDITOR_TIME">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.AUDITOR_CONTEXT">
            <summary>
            审核内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.REGISTRATION_NUM">
            <summary>
            注册证号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.REGISTRATION_NAME">
            <summary>
            注册人名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.SMBL_FLAG">
            <summary>
            生安标识
            </summary>
        </member>
        <member name="P:XH.H82.Models.Dtos.EmsWorkPlanDto.IS_ASSOCIATED">
            <summary>
            当前设备是否关联事务项
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_ID">
            <summary>
            仪器ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.SINSTRUMENT_ID">
            <summary>
            设备型号ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_SNUM">
            <summary>
            仪器序列号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_REGISTER">
            <summary>
            注册码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_POSITION">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_FACTORY">
            <summary>
            生产厂家
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_STATE">
            <summary>
            仪器状态（0：未启用；1：在用；2：停用；3：报废）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.LAST_MPERSON">
            <summary>
            最后登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.LAST_MTIME">
            <summary>
            最后登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.GROUP_ID">
            <summary>
            所属检验单元
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_TYPE">
            <summary>
            设备类型（1：检测仪器；2：流水线；3：虚拟手工）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.COMM_MODE">
            <summary>
            通讯方式（1：单向；2：双向；3：批量发送）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.GRAPH_TYPE">
            <summary>
            仪器图片比例
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTRUMENT_CNAME">
            <summary>
            自定义名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.IUNIT_ID">
            <summary>
            关联标本接收单元
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.NUMBERING_MODE">
            <summary>
            编号时机
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.CLIENT_ID">
            <summary>
            计算机名称ip
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.ORDER_SEND_STAGE">
            <summary>
            信息发送时机
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_INSTRUMENT_INFO.INSTANCE_ID">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_ID">
            <summary>
            设备型号ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.HOSPITAL_ID">
            <summary>
            医疗机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.LAB_ID">
            <summary>
            科室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_NAME">
            <summary>
            设备型号名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_SHORT">
            <summary>
            设备型号简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_NUM">
            <summary>
            设备型号编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_CODE">
            <summary>
            设备型号代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_MODE">
            <summary>
            自定义型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_DES">
            <summary>
            设备型号描述
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_STATE">
            <summary>
            设备型号状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.LAST_MPERSON">
            <summary>
            最后修改人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_CLASS">
            <summary>
            设备型号分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRU_SERIALID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SINSTRUMENT_TYPE">
            <summary>
            设备型号类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.INSTANCE_ID">
            <summary>
            系统实例ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.SHELF_TYPE">
            <summary>
            设备型号架子类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.LIS6_SINSTRUMENT_INFO.FACTORY_ID">
            <summary>
            
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE">
            <summary>
            OnlyOffice模版样式Excel导出样式配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.STYLE_ID">
            <summary>
            样式模板ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.COLUMN_JSON">
            <summary>
            表头配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.TABLE_JSON">
            <summary>
            表格配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.ADDN_JSON">
            <summary>
            其他配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.EXCEL_TPL_STATE">
            <summary>
            状态;0禁用1在用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_EXCEL_STYLE_TEMPLATE.ITEM_SETUP_JSON">
            <summary>
            配置的分类及字段(OA_FIELD_DICT),格式CLASS_CODE:FIELD_CODE1,FIELD_CODE2  数据项则固定CLASS_CODE=数据项三个字
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE">
            <summary>
            OnlyOffice模版样式记录表 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.STYLE_ID">
            <summary>
            上传文件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.AREA_ID">
            <summary>
            院区ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.LAB_PGROUP_TYPE">
            <summary>
            所属类型 0科室 1专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.CATALOG_NAME">
            <summary>
            所属目录名（只有一级目录）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.STYLE_CLASS_CODE">
            <summary>
            模版样式类型 提供给业务模块做多种分类样式表过滤
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.FILE_ID">
            <summary>
            文件上传ID OA_UPLOAD_FILE的FILE_ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.ORIGIN_FILE_ID">
            <summary>
            原始文件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.HEADER_ID">
            <summary>
            页眉页脚ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.STYLE_NAME">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.DATA_ID">
            <summary>
            来源数据ID  提供给业务模块冗余用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.STYLE_STATE">
            <summary>
            状态;0禁用1启用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.OA_OFFICE_STYLE_TEMPLATE.CLASS_COL_JSON">
            <summary>
            配置的分类及字段(OA_FIELD_DICT),格式CLASS_CODE:FIELD_CODE1,FIELD_CODE2
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.HOSPITAL_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.LAB_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.CLASS_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_SORT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_CNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_ENAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.HIS_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.CUSTOM_CODE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.SPELL_CODE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.FIRST_RPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.FIRST_RTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.LAST_MPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.LAST_MTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.REMARK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_SNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_SOURCE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.ONE_CLASS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.DATA_UNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.IF_REPEAT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_BASE_DATA.SYSTEM_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_CNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_ADDRESS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_IP">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.UPDATE_FLAG">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.FIRST_RPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.FIRST_RTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.LAST_MPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.LAST_MTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.REMARK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.HOSPITAL_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_CLASS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.OS_VER">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.OS_BITS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.OS_BOOT_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.DISK_INFO">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.MEMORY_INFO">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.CPU_INFO">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.OS_CLASS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.SERVER_SORT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SERVER_INFO.INSTANCE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_NO">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_CODE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_NAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.SOFT_KEY">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.FIRST_RPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.FIRST_RTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.LAST_MPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.LAST_MTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.REMARK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.REGISTER_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.REGISTER_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.PATIENT_CODE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_CNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.PROGRAM_TYPE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_PATH">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.DATABASE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.IIS_SERVER">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_VER">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_CLASS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.ROLE_TYPE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.PROGRAM_URL">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.SYSTEM_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.MODULE_PORT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.INSTANCE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.CALL_MODULE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.IF_MAIN_MODULE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.IF_MAIN_PROGRAM">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.IF_REGISTER">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.REGISTER_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.SETUP_UNIT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.TWO_FACTOR_MODE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.IF_USE_ELK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.DATABASE_ID2">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Common.SYS6_SOFT_MODULE_INFO.XT_MENU_CLASS">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_ID">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_NAME">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_CLASS">
            <summary>
            事件分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_SUBJECT">
            <summary>
            关联主体
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_CAUSE_CLASS">
            <summary>
            发生原因分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.MOBILE_NO">
            <summary>
            责任人联系方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.PGROUP_ID">
            <summary>
            责任实验室
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.LAB_ID">
            <summary>
            责任科室
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.RESPONSIBLE">
            <summary>
            责任人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_TIME">
            <summary>
            发生时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_LEVEL">
            <summary>
            事件等级
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_ADDR">
            <summary>
            发生地点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_CAUSE">
            <summary>
            发生原因
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_DESC">
            <summary>
            事件描述
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.CAUSE_ANALYZE">
            <summary>
            原因分析
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.DISPOSE_CONDITION">
            <summary>
            处置情况
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.DISPOSE_PERSON">
            <summary>
            处置人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.DISPOSE_TIME">
            <summary>
            处置时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.IMPROVE_PROJECT">
            <summary>
            改进方案
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.LAST_DISPOSE">
            <summary>
            最后处置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.NEXT_DISPOSE_PERSON">
            <summary>
            下一处置人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.ISSUED_FILE">
            <summary>
            归档文件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.EVENT_STATE">
            <summary>
            状态 0已登记 1已关闭 2保存
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.CLASS_MAP_SUBJECT">
            <summary>
            关联主体
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_ADVERSE_EVENT.AER_EVENT_SUBJECTs">
            <summary>
            主体列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.SUBJECT_ID">
            <summary>
            事件主体ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.EVENT_ID">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.SUBJECT_CORR_ID">
            <summary>
            主体ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.SUBJECT_NAME">
            <summary>
            事件主体名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.SUBJECT_STATE">
            <summary>
            状态 0禁用 1在用 2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.AER_EVENT_SUBJECT.SUBJECT_CLASS">
            <summary>
            主体分类
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.BaseField">
            <summary>
            基础的几个字段
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO">
            <summary>
            设备证书信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.CERTIFICATE_ID">
            <summary>
            PK
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.HOSPITAL_ID">
            <summary>
            医疗机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.EQUIPMENT_ID">
            <summary>
            设备id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.CRETIFICATE_TYPE">
            <summary>
            证书类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.CER_WANR_DATE">
            <summary>
            提醒时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.CER_DATE">
            <summary>
            发证时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.EXPIRY_DATE">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.CERTIFICATE_STATE">
            <summary>
            设备证书状态;0禁用、1在用、2删除、默认1
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.FIRST_RTIME">
            <summary>
            首次创建时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.FIRST_RPERSON">
            <summary>
            首次操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.LAST_MTIME">
            <summary>
            最后一次操作时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.LAST_MPERSON">
            <summary>
            最后一次操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Certificate.EMS_CERTIFICATE_INFO.ATTACHMENTS">
            <summary>
            附件列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZE_ID">
            <summary>
            pk
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.HOSPITAL_ID">
            <summary>
            机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.EQUIPMENT_ID">
            <summary>
            设备id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZE_PERSON">
            <summary>
            授权人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZED_PERSON">
            <summary>
            被授权人（多个）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZED_ROLE">
            <summary>
            授权权限
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZED_PERSON_POST">
            <summary>
            授权人职务
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZE_DATE">
            <summary>
            授权时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_AUTHORIZE_INFO.AUTHORIZE_STATE">
            <summary>
            数据状态 1 在用  2 删除  0 禁用（应该没有禁用）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_CORRECT_INFO.DURATION_CORRECT_DATE">
            <summary>
            校准有效期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.UNIT_NAME">
            <summary>
            单元名称
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.AREA_NAME" -->
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_ID">
            <summary>
            pk
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.UNIT_ID">
            <summary>
            检验单元id|检验专业组id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.HOSPITAL_ID">
            <summary>
            机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.LAB_ID">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.PROFESSIONAL_CLASS">
            <summary>
            专业类型 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.INSTRUMENT_ID">
            <summary>
            仪器id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.ESERIES_ID">
            <summary>
            系列id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_NUM">
            <summary>
            设备序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.DEPT_SECTION_NO">
            <summary>
             科室设备编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_ENAME">
            <summary>
            设备英文名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.VEST_PIPELINE">
            <summary>
            流水线所属（对应设备id）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.DEPT_NAME">
            <summary>
            所属部门
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.SECTION_NO">
            <summary>
            设备科室编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.FACTORY_NUM">
            <summary>
            出厂编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_FEATURE">
            <summary>
            设备参数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.BUY_DATE">
            <summary>
            购买日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_STATE">
            <summary>
            设备状态：0-未启用；1-启用；2-停用 3-报废 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.PROVIDER_ID">
            <summary>
            服务商id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.PROVIDER">
            <summary>
            服务商名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_UCODE">
            <summary>
            设备自身的自定义代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.Contacts">
            <summary>
            设备联系人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.WarnRecords">
            <summary>
            设备相关的警告记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.Inkscreen">
            <summary>
            设备绑定的水墨屏设备
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.Documents">
            <summary>
            设备其他附件信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.CertificateInfos">
            <summary>
            证书附件信息/外部链接文件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.SWITCH_STATUS">
            <summary>
            监测在线状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.IS_MONITOR">
            <summary>
            是否被生安监测
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.POSITION_ID">
            <summary>
            房间位置id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_EQUIPMENT_INFO.EQUIPMENT_JSON">
            <summary>
            拓展字段
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.CountryOriginEnum">
            <summary>
            设备产出地 （国产，进口）
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.CountryOriginEnum.Domestic">
            <summary>
            国产
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.CountryOriginEnum.Abroad">
            <summary>
            进口
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO.MGROUP_ID">
            <summary>
            检验专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.QUARTERLY_MAINTAIN">
            <summary>
            季保养周期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.QUARTERLY_MAINTAIN_WARN">
            <summary>
            季保养提醒周期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.YEARLY_MAINTAIN">
            <summary>
            年保养周期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.YEARLY_MAINTAIN_WARN">
            <summary>
            年保养提醒周期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.SUBMIT_USER_ID">
            <summary>
            提交人ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.SUBMIT_TIME">
            <summary>
            提交时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.EMS_WORK_PLAN.CURRENT_STATE">
            <summary>
            计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo">
            <summary>
            设备档案信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_ARCHIVE">
            <summary>
            档案名称 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_NAME">
            <summary>
            设备名称(中文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_NUM">
            <summary>
            设备序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.HOSPITAL_NAME">
            <summary>
            所属医疗机构
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_ENAME">
            <summary>
            设备名称(英文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.SECTION_NO">
            <summary>
            医院设备编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.LAB_NAME">
            <summary>
            所属科室
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.DEPT_SECTION_NO">
            <summary>
            科室设备编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.UNIT_ID">
            <summary>
            所属专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.REGISTRATION_NUM">
            <summary>
            注册证号(中文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.KEEP_PERSON">
            <summary>
            设备负责人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQUIPMENT_CLASS">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.REGISTRATION_ENUM">
            <summary>
            注册证号(英文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.CONTACT_PHONE">
            <summary>
            联系方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.PROFESSIONAL_CLASS">
            <summary>
            专业分类 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.SERIAL_NUMBER">
            <summary>
            设备序列号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.INSTALL_AREA">
            <summary>
            安装位置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQ_OUT_TIME">
            <summary>
            出厂日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQ_IN_TIME">
            <summary>
            到货日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.INSTALL_DATE">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.ENABLE_TIME">
            <summary>
            首次启用日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.DEPRECIATION_TIME">
            <summary>
            折旧年限(年)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.SELL_PRICE">
            <summary>
            设备价格(元)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EQ_SCRAP_TIME">
            <summary>
            报废日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.LAST_MAINTAIN_DATE">
            <summary>
            上次保养日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.MAINTAIN_TYPE">
            <summary>
            保养类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.NEXT_MAINTAIN_DATE">
            <summary>
            下次保养日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.LAST_CORRECT_DATE">
            <summary>
            上次校准日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.CORRECT_INTERVALS">
            <summary>
            校准有效期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.NEXT_CORRECT_DATE">
            <summary>
            下次校准日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.LAST_COMPARISON_DATE">
            <summary>
            上次对比日期 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.COMPARISON_INTERVALS">
            <summary>
            比对有效期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.NEXT_COMPARISON_DATE">
            <summary>
            下次比对日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.LAST_VERIFICATION_DATE">
            <summary>
            上次验证日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.VERIFICATION_INTERVALS">
            <summary>
            验证有效期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.NEXT_VERIFICATION_DATE">
            <summary>
            下次验证日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.DEALER">
            <summary>
            经销商(中文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.DEALER_ENAME">
            <summary>
            经销商(英文)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.MANUFACTURER">
            <summary>
            制造商（中文）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.MANUFACTURER_ENAME">
            <summary>
            制造商（英文）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentArchivesInfo.EnvironmentInfo">
            <summary>
            环境信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact">
            <summary>
            经销商联系人信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.NO">
            <summary>
            人员No.(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.CONTACT_TYPE">
            <summary>
            人员类型(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.CONTACT_POST">
            <summary>
            职务/岗位(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.CONTACT_NAME">
            <summary>
            联系人姓名(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.PHONE_NO">
            <summary>
            联系方式(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.CONTACT_WX">
            <summary>
            微信(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.E_MAIL">
            <summary>
            邮箱(经销商)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentDealerContact.REMARK">
            <summary>
            备注(经销商)
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentManufacturerContact">
            <summary>
            制造商联系人信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo">
            <summary>
            环境要求信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.LENGTH">
            <summary>
            长(尺寸(mm))
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.WIDTH">
            <summary>
            宽(尺寸(mm))
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.HEIGHT">
            <summary>
            高(尺寸(mm))
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.EQUIPMENT_WEIGHT">
            <summary>
            重量(kg)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.BEARING_REQUIRE">
            <summary>
            承重要求(kg)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.SPACE_REQUIRE">
            <summary>
            空间(m3)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.AIR_REQUIRE">
            <summary>
            空间洁净度(级)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.WATER_REQUIRE">
            <summary>
            水质要求(Ω)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.TEMP_MIN">
            <summary>
            最低温度(℃)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.TEMP_MAX">
            <summary>
            最高温度(℃)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.HUMI_MIN">
            <summary>
            低湿度(%)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.HUMI_MAX">
            <summary>
            最高湿度(%)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.AIR_PRESSURE_REQUIRE">
            <summary>
            气压(Pa)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.POWER_REQUIRE">
            <summary>
            功率(W)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.VOLTAGE_REQUIRE">
            <summary>
            电压(V)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.ELECTRICITY_REQUIRE">
            <summary>
            电流(A)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EnvironmentInfo.OTHER_REQUIRE">
            <summary>
            其他
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData">
            <summary>
            设备数据项
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.ComparisonReocrd">
            <summary>
            比对记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.CorrectReocrd">
            <summary>
            校准记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.InstallRecord">
            <summary>
            安装信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.OpenRecord">
            <summary>
            开箱记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.OpenPartRecord">
            <summary>
            配件记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.PlanRecord">
            <summary>
            工作计划记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.Startupinfo">
            <summary>
            开机性能验证报告
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.Procurement">
            <summary>
            采购信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.WeekWorkPlan">
            <summary>
            工作计划周保养
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.WeekWorkPlan.LAST_MAINTAIN_DATE">
            <summary>
            最近一次周保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.WeekWorkPlan.MAINTAIN_INTERVALS">
            <summary>
            周保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.WeekWorkPlan.MAINTAIN_WARN_INTERVALS">
            <summary>
            周保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.MonthWorkPlan.MONTHLY_MAINTAIN">
            <summary>
            月保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.MonthWorkPlan.MONTHLY_MAINTAIN_WARN">
            <summary>
            月保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.MonthWorkPlan.LAST_MONTHLY_MAINTAIN_DATE">
            <summary>
            最近一次月保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.YearWorkPlan.YEARLY_MAINTAIN">
            <summary>
            年保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.YearWorkPlan.YEARLY_MAINTAIN_WARN">
            <summary>
            年保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.YearWorkPlan.LAST_YEARLY_MAINTAIN_DATE">
            <summary>
            最近一次年保养时间
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.RunRecord">
            <summary>
            运行记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.Dealer">
            <summary>
            经销商联系人
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.Manufacturer">
            <summary>
            经销商联系人
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.TrainingRecord">
            <summary>
            培训记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.SubscriptionRecord">
            <summary>
            申购记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.EquipmentEnvironment">
            <summary>
            环境信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.MaintenanceRecord">
            <summary>
            保养记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.EquipmentInfoData.Verification">
            <summary>
            性能验证记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT">
            <summary>
            实验室表单模板字段定义表 
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_ID">
            <summary>
            上传文件ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_TYPE">
            <summary>
            数据分类 0:数据项 1:记录项
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.CLASSE_CODE">
            <summary>
            类型代码 数据库为必填
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.CLASSE_NAME">
            <summary>
            类型名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_CODE">
            <summary>
            字段代码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_NAME">
            <summary>
            字段名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_DESC">
            <summary>
            字段说明
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_SORT">
            <summary>
            排序号;001
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_DEFAULT">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_FORMAT">
            <summary>
            格式;YYYY
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_DOMAIN">
            <summary>
            数据的作用域，支持多个 如 证书类型是附件，且同时还是下拉.在word显示时需要有超链接，则如下 FILE, DICT, ALINK
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_DOMAIN_ORIGIN">
            <summary>
            数据源（目前用于字典，但兼容了扩展） [{"type":"DICT","origin:"",jsonData:""}origin  1,2,3  (固定枚举、进OA_BASE_DATA字典、走模块提供的接口)jsonData值示例1 : {"1":"男","2":女"}   2:"OA_BASE_DATA"     3:"/api/xxxxxx"  (根据模块名获取地址然后去取)"} ]
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIELD_STATE">
            <summary>
            状态;0禁用1启用2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.OA_FIELD_DICT.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo">
            <summary>
            导出的excel预览信息体
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.WORK_PLAN_ID">
            <summary>
            工作计划主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.EQUIPMENT_ID">
            <summary>
            设备主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.CURRENT_STATE_NAME">
            <summary>
            计划状态中文
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.CURRENT_STATE">
            <summary>
            计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.MAINTAIN_INTERVALS">
            <summary>
            周保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.MAINTAIN_WARN_INTERVALS">
            <summary>
            周保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.LAST_MAINTAIN_DATE">
            <summary>
            最近一次周保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.NEXT_MAINTAIN_DATE">
            <summary>
            下一次周保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.MONTHLY_MAINTAIN">
            <summary>
            月保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.MONTHLY_MAINTAIN_WARN">
            <summary>
            月保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.LAST_MONTHLY_MAINTAIN_DATE">
            <summary>
            最近一次月保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.NEXT_MONTHLY_MAINTAIN_DATE">
            <summary>
            下一次月保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.YEARLY_MAINTAIN">
            <summary>
            年保养周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.YEARLY_MAINTAIN_WARN">
            <summary>
            年保养提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.LAST_YEARLY_MAINTAIN_DATE">
            <summary>
            最近一次年保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.NEXT_YEARLY_MAINTAIN_DATE">
            <summary>
            下一次年保养时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.CORRECT_INTERVALS">
            <summary>
            校准周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.CORRECT_WARN_INTERVALS">
            <summary>
            校准提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.LAST_CORRECT_INTERVALS_DATE">
            <summary>
            最近一次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.NEXT_CORRECT_INTERVALS_DATE">
            <summary>
            下一次校准时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.CORRECT_UNIT">
            <summary>
            校准单位
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.EQ_IN_PERSON">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.COMPARISON_INTERVALS">
            <summary>
            比对周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.COMPARISON_WARN_INTERVALS">
            <summary>
            比对提醒周期/天
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.LAST_COMPARISON_INTERVALS_DATE">
            <summary>
            最近一次比对时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.NEXT_COMPARISON_INTERVALS_DATE">
            <summary>
            下一次比对时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.OPER_PERSON">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.OPER_TIME">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.AUDITOR_USER_NAME">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.AUDITOR_TIME">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.AUDITOR_CONTEXT">
            <summary>
            审核内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.REGISTRATION_NUM">
            <summary>
            注册证号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.FileTemplate.WorkPlansInfo.REGISTRATION_NAME">
            <summary>
            注册人名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.TEMPLATE_ID">
            <summary>
            模板id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.TEMPLATE_NAME">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.TEMPLATE_CONTENT">
            <summary>
            模板内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.TEMPLATE_TITLE">
            <summary>
            模板标题
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.SET_ABNORMAL">
            <summary>
            异常状态亮灯
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.SET_QR_CODE">
            <summary>
            是否设置二维码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.SET_WIREFRAME">
            <summary>
            是否设置表格线框
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.PGROUP_SID">
            <summary>
            应用的专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.InkScreen.EMS_INKSCREEN_TEMPLATE.TEMPLATE_STATE">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.HospitalId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.AreaId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.LabId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ModuleId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgCorrid">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceiveUnitId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceiveUnitType">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MagDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgClass">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgType">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgOperate">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgTitle">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgLevel">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgDisposeUrl">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgDisposeType">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgValidTime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.SendPerson">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.SendTime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.SendComputer">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceivePersonType">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceivePerson">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceiveTime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceiveComputer">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgOvertime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgState">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.Remark">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.SendUnitId">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.DelayTime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.ReceiveUnitName">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgDisposeUrlStyle">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgWarningTime">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.LIS6.LIS6_MSG_INFO.MsgAlarmTime">
            <summary>
            
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG">
            <summary>
            操作记录表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_MAIN_ID">
            <summary>
            数据id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_NAME">
            <summary>
            操作名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_PERSON_ID">
            <summary>
            操作人主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_PERSON">
            <summary>
            操作人名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.NEXT_OPER_PERSON_ID">
            <summary>
            下一个操作人id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.NEXT_PER_PERSON">
            <summary>
            下一个操作人名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.PREV_OPER_ID">
            <summary>
            前一个操作记录id    操作id为0时  代表是第一次操作
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_STATE">
            <summary>
            操作状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.OperationLog.EMS_OPER_LOG.OPER_CONTENT">
            <summary>
            操作意见
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.OperationLog.OperationStateEnum">
            <summary>
            流转操作枚举
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.OperationLog.OperationStateEnum.Overruled">
            <summary>
            驳回
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.OperationLog.OperationStateEnum.NotSubmitted">
            <summary>
            未提交
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.OperationLog.OperationStateEnum.Submitted">
            <summary>
            已提交
            </summary>
        </member>
        <member name="F:XH.H82.Models.Entities.OperationLog.OperationStateEnum.Audited">
            <summary>
            已审核
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PERSON_ID">
             <summary>
            人员ID
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PGROUP_ID">
             <summary>
            管理单元ID
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PGROUP_NAME">
             <summary>
            管理单元名称
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HOSPITAL_ID">
             <summary>
            医疗机构ID
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.USER_ID">
             <summary>
            账号ID
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.USER_TYPE">
             <summary>
            人员类型
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.USER_NAME">
             <summary>
            姓名
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.USER_ENAME">
            <summary>
            英文名
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.SEX">
             <summary>
            性别
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.AGE">
             <summary>
            年龄
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.BIRTHDAY">
             <summary>
            出生年月
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.NATION">
             <summary>
            民族
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.NATIVE_PLACE">
             <summary>
            籍贯
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.POLITICIAN">
             <summary>
            政治面貌
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PROFESSION">
             <summary>
            毕业专业
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HIGHEST_DEGREE">
             <summary>
            最高学历
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.DEGREE_TIME">
             <summary>
            学历取得时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HIGHEST_DIPLOMA">
             <summary>
            最高学位
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.DIPLOMA_TIME">
             <summary>
            学位取得时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.WORK_TIME">
             <summary>
            参加工作时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.LENGTH_SERVICE">
             <summary>
            连续工龄
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.IN_HOSPITAL_DATE">
             <summary>
            进院日期
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.LENGTH_HOSPITAL">
             <summary>
            院龄
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.DUTIES">
             <summary>
            行政职务
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.TECH_POST">
             <summary>
            职称级别
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.ACADEMIC_POST">
             <summary>
            职称名称 对应系统数据管理USER表的TECH_POST字段
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.COMM_ADDR">
             <summary>
            通信地址
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HOME_TEL">
             <summary>
            家庭电话
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PHONE">
             <summary>
            手机
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CORNET">
             <summary>
            短号
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.BIRTH_PLACE">
             <summary>
            出生地
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HEIGHT">
             <summary>
            身高
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EYESIGHT">
             <summary>
            视力
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EYESIGHT_LEFT">
             <summary>
            左视力
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EYESIGHT_RIGHT">
             <summary>
            右视力
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.ENGLISH_RANK_SCORE">
             <summary>
            英语等级成绩
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.MARITAL_STATUS">
             <summary>
            婚姻状况
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CHILDREN_CONDITION">
             <summary>
            有无子女
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CARD_TYPE">
             <summary>
            证件类型
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.ID_CARD">
             <summary>
            身份证
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.DOMICILE_PLACE">
             <summary>
            户籍所在地
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EMERGENCY_CONTACT">
             <summary>
            紧急联系人
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.ECONTACT_RELACTION">
             <summary>
            与紧急联系人的关系
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.ECONTACT_PHONE">
             <summary>
            紧急联系人电话
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CURRENT_ADDRESS">
             <summary>
            现居住地
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HEALTH">
             <summary>
            健康状况
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.E_MAIL">
             <summary>
            邮箱
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.OFFICE_PHONE">
             <summary>
            办公电话
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EMPLOYMENT_UNIT">
             <summary>
            聘任职称评定单位
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.TECHNOLOGY_TYPE">
             <summary>
            职称类型
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.TECH_CERTIFICE_TIME">
             <summary>
            专业技术资格取得时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.TECH_POST_PROFESSION">
             <summary>
            职称专业
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EMPLOYMENT_SOURE">
             <summary>
            招聘来源
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PROFESSION_EXPERTISE">
             <summary>
            专业特长
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.EMPLOY_TIME">
             <summary>
            聘用时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.RETIRE_TIME">
             <summary>
            退休时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.IN_HOSPITAL_TIME">
             <summary>
            来院时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.OUT_HOSPITAL_TIME">
             <summary>
            离院时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.REEMPLOY_TIME">
             <summary>
            返聘时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.REGISTER_MODE">
             <summary>
            登记模式
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.REGISTER_PERSON">
             <summary>
            登记人员
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.REGISTER_TIME">
             <summary>
            登记时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.SUBMIT_PERSON">
             <summary>
            提交人员
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.SUBMIT_TIME">
             <summary>
            提交时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CHECK_PERSON">
             <summary>
            审核人员
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CHECK_TIME">
             <summary>
            审核时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.CHECK_COMPUTER">
             <summary>
            审核电脑
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.DOC_PLACE">
             <summary>
            放置场所
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PERSON_DOC_STATE">
             <summary>
            人员性质
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.PERSON_STATE">
             <summary>
            状态
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.FIRST_RPERSON">
             <summary>
            首次登记人
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.FIRST_RTIME">
             <summary>
            首次登记时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.LAST_MPERSON">
             <summary>
            最后修改人员
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.LAST_MTIME">
             <summary>
            最后修改时间
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.REMARK">
             <summary>
            备注
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.HIS_ID">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.GRADUATE_SCHOOL">
             <summary>
            毕业院校
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.GRADUATE_DATE">
             <summary>
            毕业日期
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.COLOR_DEFICIENCY">
             <summary>
            颜色视觉障碍（0-正常  1-色弱 2-色盲）
             </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.PMS.PMS_PERSON_INFO.RECORD_DATA">
            <summary>
            扩展字段
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.INTERFACE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.MODULE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.APP_SECRET">
            <summary>
            应用APP_SECRETY
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.APP_KEY">
            <summary>
            应用APP_KEY
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.MODULE_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.FIRST_RPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.FIRST_RTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.LAST_MPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.LAST_MTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_INTERFACE_MODULE.REMARK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.POSITION_ID">
            <summary>
            位置id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.HOSPITAL_ID">
            <summary>
            机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.LAB_ID">
            <summary>
            科室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.AREA_ID">
            <summary>
            院区id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.POSITION_NAME">
            <summary>
            位置名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.TOWER_NO">
            <summary>
            楼号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.FLOOR_NO">
            <summary>
            层数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.ROOM_NO">
            <summary>
            房号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.POSITION_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.SMBL_LAB_ID">
            <summary>
            备案实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.SYS6_POSITION_DICT.POSITION_STATE">
            <summary>
            状态 0 禁用   1再用  2 删除
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST">
            <summary>
            异常记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_ID">
            <summary>
            异常处理id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.MONITOR_ID">
            <summary>
            监测记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_TYPE">
            <summary>
            异常类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_PERSON">
            <summary>
            处理人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_TIME">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_COMPUTER">
            <summary>
            处理电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_CAUSE_CLASS">
            <summary>
            原因分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_CAUSE">
            <summary>
            原因
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.OPER_STATE">
            <summary>
            状态(0未处理1已处理)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.EQUIPMENT_ID">
            <summary>
            ths 设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.ALARM_TIME">
            <summary>
            报警时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_ABNORMAL_OPER_LIST.ALARM_CONTINUOUS_TIME">
            <summary>
            报警时长
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO">
            <summary>
            设备信息表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_ID">
            <summary>
            温湿度设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_DID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_CODE">
            <summary>
            设备代号EQUIPMENT_CODE[EMS_EQUIPMENT_INFO_NEW]
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_POSITION">
            <summary>
            设备位置INSTRUMENT_POSITION[LIS5_INSTRUMENT_INFO]
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.SHELF_NUM">
            <summary>
            设备层架数量
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.COORDINATE_PARAM">
            <summary>
            坐标参数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.IF_MAJOR_EQUIPMENT">
            <summary>
            是否重点设备0否1是
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.RECORD_TIMING">
            <summary>
            记录时间点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.ALARM_TYPE">
            <summary>
            报警联系方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.ALARM_LINKMAN">
            <summary>
            报警联系人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.ALARM_PHONE">
            <summary>
            报警联系电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_STATE">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_TYPE">
            <summary>
            设备类型1设备2房间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_INFO.EQUIPMENT_SN">
            <summary>
            设备对应SN
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT">
            <summary>
            设备监测点信息表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_NAME">
            <summary>
            监测点名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_SNAME">
            <summary>
            监测点简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_NUM">
            <summary>
            对应编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.OBTAIN_MODE">
            <summary>
            数据获取方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.ONLINE_STATE">
            <summary>
            在线状态1在线2离线
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.ABNORMAL_STATE" -->
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.POINT_STATE">
            <summary>
            状态0禁用1在用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.ONLINE_TIME_LIMIT">
            <summary>
            设备离线判断时限 例子：30分钟
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.OBTAIN_FREQUENCY">
            <summary>
            数据获取频率(分钟)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.EQUIPMENT_MID">
            <summary>
            温度计编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT.LAST_TIME_POINT">
            <summary>
            最后监测数据时间
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM">
            <summary>
            设备监测点指标信息表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ITEM_ID">
            <summary>
            指标ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ITEM_NAME">
            <summary>
            指标名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ITEM_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.RULE_ID">
            <summary>
            报警规则ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ONLINE_ITEM_STATE">
            <summary>
            在线状态1在线2离线
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ABNORMAL_ITEM_STATE">
            <summary>
            异常状态1正常2异常
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ITEM_STATE">
            <summary>
            状态0禁用1在用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.COORDINATE_PARAM">
            <summary>
            坐标参数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_EQUIPMENT_POINT_ITEM.ITEM_CHANNEL">
            <summary>
            对应通道号
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_MONITOR_DAY">
            <summary>
            设备检测点每日监测汇总表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.MONITOR_DAYID">
            <summary>
            日监测记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.ITEM_ID">
            <summary>
            监测指标ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.MONITOR_DATE">
            <summary>
            记录日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.TIME_POINT">
            <summary>
            监测记录总数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.NORMAL_NUM">
            <summary>
            正常记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.ABNORMAL_NUM">
            <summary>
            异常记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.MAX_VALUE">
            <summary>
            最高值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.MIN_VALUE">
            <summary>
            最低值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.AVG_VALUE">
            <summary>
            平均值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.ACCUM_VALUE">
            <summary>
            累计值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.ALARM_NUM">
            <summary>
            报警记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.OPER_PERSON">
            <summary>
            处理人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.OPER_TIME">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.OPER_COMPUTER">
            <summary>
            处理电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.MONITOR_DAY_STATE">
            <summary>
            状态1未确认2已确认
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_DAY.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MONITOR_HOURID">
            <summary>
            阶段监测记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.ITEM_ID">
            <summary>
            监测指标ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MONITOR_TYPE">
            <summary>
            监测类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MONITOR_DATE">
            <summary>
            记录日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.TIME_POINT_HOUR">
            <summary>
            监测记录小时范围
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.TOTAL_NUM">
            <summary>
            总记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.NORMAL_NUM">
            <summary>
            正常记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.ABNORMAL_NUM">
            <summary>
            异常记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MAX_VALUE">
            <summary>
            最高值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MIN_VALUE">
            <summary>
            最低值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.AVG_VALUE">
            <summary>
            平均值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.ACCUM_VALUE">
            <summary>
            累计时长
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.ALARM_NUM">
            <summary>
            报警记录数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.OPER_PERSON">
            <summary>
            处理人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.OPER_TIME">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.OPER_COMPUTER">
            <summary>
            处理电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_HOUR.MONITOR_HOUR_STATE">
            <summary>
            状态;1未确认 2已确认
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM">
            <summary>
            设备监测指标表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_ID">
            <summary>
            监测指标ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_NAME">
            <summary>
            监测指标名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_UNIT">
            <summary>
            监测指标单位
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_PRECISION">
            <summary>
            监测指标精度(0,1,2,3,4)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.ITEM_STATE">
            <summary>
            状态0禁用1在用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_ITEM.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_MONITOR_LIST">
            <summary>
            设备检测点监测记录表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_ID">
            <summary>
            监测记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.POINT_ID">
            <summary>
            监测点ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_DATE">
            <summary>
            记录日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.TIME_POINT">
            <summary>
            监测时间点
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_PERSON">
            <summary>
            记录人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_TIME">
            <summary>
            记录时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_COMPUTER">
            <summary>
            记录电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_TID">
            <summary>
            监测临时记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.MONITOR_STATE">
            <summary>
            状态1正常2异常
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.OPER_PERSON">
            <summary>
            处理人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.OPER_TIME">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.OPER_COMPUTER">
            <summary>
            处理电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_LIST.ThsMonitorResults">
            <summary>
            关联的监测值结果
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT">
            <summary>
            设备检测点监测记录表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.MONITOR_ID">
            <summary>
            监测记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.ITEM_ID">
            <summary>
            监测指标ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.ITEM_VALUE">
            <summary>
            监测值
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.ITEM_STATUS">
            <summary>
            判断状态L偏低H偏高Z正常
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.ITEM_STATE">
            <summary>
            状态1正常2异常
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.ALARM_RANGE">
            <summary>
            报警范围
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.RULE_ID">
            <summary>
            报警规则ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_MONITOR_RESULT.CHART_STATE">
            <summary>
            图表展示状态(1正常2异常)
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.THS.THS_UNIT_INFO">
            <summary>
            单元信息表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.UNIT_ID">
            <summary>
            管理单元ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.UNIT_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.THS.THS_UNIT_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.FORM_MAIN_ID">
            <summary>
            记录单主体ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.WORK_MAINID">
            <summary>
            事务主体ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.MAIN_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.MAIN_STATE">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.FORM_ID">
            <summary>
            记录单ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_FORM_MAIN_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.LAB_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.ISSUE_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.FIRST_RPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.LAST_MTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.UNIT_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WORK_MAINID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.FORM_VER_MAIN_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WORK_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.RECORD_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.FORM_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_RESULT">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.ISSUE_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.IF_MUST">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.MODULE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.RECORD_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.ISSUE_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.REMARK">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_DATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_COMPUTER">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.ISSUE_PERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WEXEC_STATE">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.FIRST_RTIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.LAST_MPERSON">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.WORK_MAINNAME">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.PGROUP_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.HOSPITAL_ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_EXEC_LIST.DESIGN_WEXEC_PERSON">
            <summary>
            指定执行人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_ID">
            <summary>
            记录单ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FILL_LAYOUT">
            <summary>
            填写页面布局1左右结构2上下结构
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.LAB_ID">
            <summary>
            科室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.IF_AUTO_ISSUED">
            <summary>
            自动归档0否1是
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.IF_MAIN">
            <summary>
            是否有主体0无1有
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.COL_NUM">
            <summary>
            纵向多列数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_STATE">
            <summary>
            记录单状态0废止1在用
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.MAIN_TYPE">
            <summary>
            主体类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.ISSUED_OPPORTUNITY">
            <summary>
            归档时机
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_NAME">
            <summary>
            记录单名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_DESC">
            <summary>
            记录单描述
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.ISSUED_PLAN">
            <summary>
            归档计划1日2周3月4季5年0每次
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.NO_FORMAT">
            <summary>
            序号格式
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.DOC_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.HEADER_FOOTER">
            <summary>
            页眉/页脚设置
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.CLASS_ID">
            <summary>
            记录单分类ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_SNAME">
            <summary>
            记录单简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_CLASS">
            <summary>
            归属分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.ISSUED_HOUR">
            <summary>
            归档时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.PGROUP_ID">
            <summary>
            检验专业组ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FORM_NO">
            <summary>
            记录单编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FILL_TYPE">
            <summary>
            归档类型1单次单表2多次汇总
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM.FILL_METHOD">
            <summary>
            填写方式(1事务项 2上传 3线上填写,默认1)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_ID">
            <summary>
            记录单版本ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_ID">
            <summary>
            记录单ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ISSUED_COMPUTER">
            <summary>
            发布电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.CHECK_PERSON">
            <summary>
            审核者
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.SUBMIT_PERSON">
            <summary>
            提交者
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_REVISE_NO">
            <summary>
            修订号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.APPROVAL_PERSON">
            <summary>
            批准者
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ITEM_SETUP_JSON">
            <summary>
            数据项JSON
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ISSUED_NO">
            <summary>
            发布号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.START_DATE">
            <summary>
            启用日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ASSIGN_APPROVAL">
            <summary>
            指定审核人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.APPROVAL_TIME">
            <summary>
            批准日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_DESC">
            <summary>
            记录单版本描述
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ISSUED_TIME">
            <summary>
            发布日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_FILE">
            <summary>
            记录单文件
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_NO">
            <summary>
            版本号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.APPROVAL_COMPUTER">
            <summary>
            批准电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_STATE">
            <summary>
            记录单版本状态0未提交1已提交2已审核未通过3待审批4已审批未通过5待发布6已发布-0废止
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.IF_INHERIT_OVER">
            <summary>
            继承上一版本0否1是
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ISSUED_PERSON">
            <summary>
            发布者
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FORM_VER_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.SUBMIT_TIME">
            <summary>
            提交日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.SUBMIT_COMPUTER">
            <summary>
            提交电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.ASSIGN_CHECK">
            <summary>
            指定审核人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.CHECK_TIME">
            <summary>
            审核日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.EXECUTE_DATE">
            <summary>
            实施日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_FORM_VER.CHECK_COMPUTER">
            <summary>
            审核电脑
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_ID">
            <summary>
            事务ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_NO">
            <summary>
            事务编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_SORT">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_LAYOUT">
            <summary>
            归档布局1纵向单列2纵向多列3横向
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_STATE">
            <summary>
            事务状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.FORM_VER_ID">
            <summary>
            记录单版本ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_EXPLIAN">
            <summary>
            事务说明
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.MODULE_ID">
            <summary>
            模块ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.ISSUED_ROW">
            <summary>
            归档行数
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_NAME">
            <summary>
            事务名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.FREQUENCY_SETUP">
            <summary>
            执行频率内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_FREQUENCY">
            <summary>
            执行频率类型1按需2天3周4月5季6年7按天多次
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.NEXT_WORK_PERSON">
            <summary>
            指定下一级事务操作人
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_DESC">
            <summary>
            事务描述
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.EXCLUDE_WEEK">
            <summary>
            排除星期(设置6,7)
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.PROMPT_TYPE">
            <summary>
            周末提醒方式（1向前提醒2向后提醒）
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Tim.TIM_WORK_INFO.WORK_PLAN_TYPE">
            <summary>
            计划类型（1周保养2月保养3季保养4年保养）
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.TIM_WORK_FORM_ISSUE">
            <summary>
            记录单归档信息表
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO">
            <summary>
            设备事务使用记录
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_ID">
            <summary>
            PK
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.HOSPITAL_ID">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.EQUIPMENT_ID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_CYCLE">
            <summary>
            使用周期;0 日/次  1月
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_PERSON">
            <summary>
            登记人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_DATA">
            <summary>
            使用时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_CONTEXT">
            <summary>
            登记内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_STATE">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO.IMPLEMENT_SOURCE">
            <summary>
            数据来源   0 自己   1 监测   2 事务项
            </summary>
        </member>
        <member name="T:XH.H82.Models.Entities.Transaction.RecordContentDict">
            <summary>
            设备记录内容字典
            </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.MonitorTypeEnum.Cumulative">
            <summary>
            累计
            </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.MonitorTypeEnum.Monitor">
            <summary>
            监测
            </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.MonitorTypeEnum.Conut">
            <summary>
            数量
            </summary>
        </member>
        <member name="T:XH.H82.Models.Enums.ServiceAreaEnum">
            <summary>
            供应商服务范围
            </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.EQUIPMENT">
             <summary>
            设备
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.MATERIAL">
             <summary>
            物资
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.EQUIP_MAT">
             <summary>
            设备+物资
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.INFO_SYS">
             <summary>
            信息系统
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.CHECK">
             <summary>
            检测服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.ServiceAreaEnum.MAINTENANCE">
             <summary>
            维保服务
             </summary>
        </member>
        <member name="T:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum">
            <summary>
            院内供应商服务范围
            </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.CLEAN">
             <summary>
            清洁
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.EQUIP_INFORMATION">
             <summary>
            信息服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.EQUIP_MANAGE">
             <summary>
            设备管理服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.EQUIP_MAINT">
             <summary>
            设备维修服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.COMPUTER_MANAGE">
             <summary>
            电脑管理服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.DISINFECT">
             <summary>
            消毒和供应服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.ENGINEERING">
             <summary>
            工程服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.TRANSPORT">
             <summary>
            运送服务
             </summary>
        </member>
        <member name="F:XH.H82.Models.Enums.HospitalInternalServiceAreaEnum.GUARANTEE">
             <summary>
            工人后勤保障服务
             </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto">
            <summary>
            设备类型档案记录展示Dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.EqpArchivesId">
            <summary>
            档案记录id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.EqpArchivesName">
            <summary>
            档案记录细分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.EqpArchivesPid">
            <summary>
            档案记录父级id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.EqpArchivesPName">
            <summary>
            档案记录父级名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.EqpArchivesType">
            <summary>
            档案记录类型;0:固定 1:扩展 默认0
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.ClassLinkArchivesDto.IsSubdivision">
            <summary>
            是否是细分类
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD">
            <summary>
            设备附加记录表
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EqpRecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EquipmentId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EqpArchivesId">
            <summary>
            档案记录字典ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EquitmentJson">
            <summary>
            附加信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EqpRecordAffix">
            <summary>
            附件
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EqpRecordSort">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.EqpRecordState">
            <summary>
            状态;0禁用 1在用 2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ADDN_RECORD.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT">
            <summary>
            设备档案记录字典表
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesId">
            <summary>
            档案记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesPid">
            <summary>
            档案记录父级ID;细分类的父级id 父级id可为空
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesPName">
            <summary>
            档案记录父级名称;细分类的父级名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesType">
            <summary>
            档案记录类型;0:固定 1:扩展 默认0
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesName">
            <summary>
            档案名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesSort">
            <summary>
            档案顺序
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesState">
            <summary>
            档案记录状态;0 禁用  1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.FormSetupId">
            <summary>
            表单配置记录id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.TableSetupId">
            <summary>
            表格配置记录id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.EqpArchivesJson">
            <summary>
            用于存储不同档案记录的拓展功能：如是否上传附件
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.ClassArchives">
            <summary>
            关联关系
            </summary>
        </member>
        <member name="M:XH.H82.Models.EquipmengtClassNew.EMS_EQP_ARCHIVES_DICT.CreatInit(System.String)">
            <summary>
            创建固定数据
            </summary>
            <param name="hospitalId"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES">
            <summary>
            设备类型档案记录关联表
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.ClassArchivesId">
            <summary>
            关联id主键
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.EqpClassId">
            <summary>
            设备类型id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.EqpArchivesId">
            <summary>
            档案记录字典id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_CLASS_ARCHIVES.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT">
            <summary>
            设备设置字典表
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupId">
            <summary>
            设置ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupCname">
            <summary>
            自定义名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupType">
            <summary>
            设置类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupJson">
            <summary>
            设置属性;扩展属性
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupNsort">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.EqpSetupNstate">
            <summary>
            状态;0禁用  1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQP_SETUP_DICT.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT">
            <summary>
            设备分类表
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassId">
            <summary>
            设备分类id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ParentClassId">
            <summary>
            父级设备分类id;父级节点为固定大类的固定id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassName">
            <summary>
            设备分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassSname">
            <summary>
            设备分类简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassTag">
            <summary>
            设备分类标签;0 iso 1生物安全  2POCT 3高等级
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassStyle">
            <summary>
            设备分样式;用于存储图案颜色
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassLevel">
            <summary>
            设备分优先级;根节点为0 逐级递增1  结合自定义代号作模板选择权重
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassState">
            <summary>
            设备分类状态;0 禁用   1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.ClassArchives">
            <summary>
            关联关系
            </summary>
        </member>
        <member name="M:XH.H82.Models.EquipmengtClassNew.EMS_EQUIPMENT_CLASS_DICT.CreatInit(System.String)">
            <summary>
            初始化一级分类的数据
            </summary>
            <param name="hospitalId"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto">
            <summary>
            档案记录Dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesSort">
            <summary>
            档案顺序
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesId">
            <summary>
            档案记录ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesState">
            <summary>
            档案记录状态;0 禁用  1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesStateName">
            <summary>
            档案记录状态;0 禁用  1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesName">
            <summary>
            档案名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesPid">
            <summary>
            档案记录父级id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesPidName">
            <summary>
            档案记录父级名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.EqpArchivesType">
            <summary>
            档案记录类型;0:固定 1:扩展 默认0
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.IsUpload">
            <summary>
            是否上传附件
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.ApplyEquipmentClassNames">
            <summary>
            适用设备类型名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EqpArchivesDto.IsSubdivision">
            <summary>
            是否是细分类
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto">
            <summary>
            设备分类Dto
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassId">
            <summary>
            设备分类id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassName">
            <summary>
            设备分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ParentClassId">
            <summary>
            父级设备分类id;父级节点为固定大类的固定id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ParentClassName">
            <summary>
            父级设备分类名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassSname">
            <summary>
            设备分类简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassTag">
            <summary>
            设备分类标签;0 iso 1生物安全  2POCT 3高等级
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassStyle">
            <summary>
            设备分样式;用于存储图案颜色
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassLevel">
            <summary>
            设备分优先级;根节点为0 逐级递增1  结合自定义代号作模板选择权重
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassState">
            <summary>
            设备分类状态;0 禁用   1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassStateName">
            <summary>
            设备分类状态;0 禁用   1在用  2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.LAST_MTIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.LAST_MPERSON">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.FIRST_RTIME">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.FIRST_RPERSON">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EquipmentClassDto.ClassArchivesName">
            <summary>
            档案记录
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto">
            <summary>
            添加设备类型模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.ParentClassId">
            <summary>
            父级设备分类id;父级节点为固定大类的固定id  
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.ClassName">
            <summary>
            设备类型名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.ClassSname">
            <summary>
            设备分类简称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.ClassTag">
            <summary>
            设备分类标签;0 iso 1生物安全  2POCT 3高等级
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.ClassStyle">
            <summary>
            设备分样式;用于存储图案颜色
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto.IsUseParentRecord">
            <summary>
            缺省上一层级标签内容
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT">
            <summary>
            设备自定义名称模板
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoId">
            <summary>
            自定义名称模板id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoName">
            <summary>
            设备自定义名称模板名;模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoLevel">
            <summary>
            模板优先级;模板优先级、数值越大、优先级越高
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoClass">
            <summary>
            应用设备类型;0 全部 设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpDisplayJson">
            <summary>
            设备代号配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoApplys">
            <summary>
            应用范围
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.EqpNoState">
            <summary>
            数据状态;0 禁用   1在用   2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.FirstRperson">
            <summary>
            首次登记人
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.FirstRtime">
            <summary>
            首次登记时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.LastMperson">
            <summary>
            最后修改人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.LastMtime">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmengtClassNew.EMS_EQPNO_FORMAT_DICT.Remark">
            <summary>
            备注0
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.DisplayContent.DisplayContentString">
            <summary>
            显示内容中文({设备编码}_{设备名称})
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.DisplayContent.DisplayContentCode">
            <summary>
            展示内容对应的字段  （;拼接 如{EQUIPMENT_CODE}_{EQUIPMENT_NAME}）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.DisplayContent.FixedFieldDisplays">
            <summary>
            固定字段值修改显示（机构、科室、专业组、管理专业组、备案实验室、院区）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoId">
            <summary>
            自定义名称模板id
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.HospitalId">
            <summary>
            医疗机构ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoName">
            <summary>
            设备自定义名称模板名;模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoLevel">
            <summary>
            模板优先级;模板优先级、数值越大、优先级越高
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoClass">
            <summary>
            应用设备类型;0 全部 设备类型（多个类型;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoClassList">
            <summary>
            应用设备类型;0 全部 设备类型（多个类型;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoClassName">
            <summary>
             应用设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpDisplayJson">
            <summary>
            设备代号配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.DisplayContent">
            <summary>
            设备展示内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.DisplayContentName">
            <summary>
            显示内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoApplys">
            <summary>
            应用范围（专业组id ;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoApplyList">
            <summary>
            应用范围（专业组id ;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoApplysName">
            <summary>
            应用范围中文;分割
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoState">
            <summary>
            数据状态;0 禁用   1在用   2删除
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentCodeCustomDict.EqpNoStateName">
            <summary>
            数据状态名字; 禁用   在用   删除
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto">
            <summary>
            添加自定义编号字典模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpNoName">
            <summary>
            设备自定义名称模板名;模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpNoLevel">
            <summary>
            模板优先级;模板优先级、数值越大、优先级越高
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpNoClass">
            <summary>
            应用设备类型;0 全部 设备类型（多个类型;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpDisplayJson">
            <summary>
            设备代号配置
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.DisplayContent">
            <summary>
            设备展示内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpNoApplys">
            <summary>
            应用范围（专业组id ;拼接）
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto.EqpNoState">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmentCodeCustom.UpdateEquipmentCodeCustomDictDto">
            <summary>
            更新自定义编号字典模型
            </summary>
        </member>
        <member name="T:XH.H82.Models.EquipmentCodeCustom.EquipmentDictCode">
            <summary>
            设备自定义模板选项模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentDictCode.UNIT_NAME">
             <summary>
             检验专业组名称
             </summary>
            
        </member>
        <member name="P:XH.H82.Models.EquipmentCodeCustom.EquipmentDictCode.SMBL_LAB_NAME">
            <summary>
            备案实验室名称
            </summary>
        </member>
        <member name="T:XH.H82.Models.ExecutingChangeSqlHelper">
            <summary>
            表名转换类
            </summary>
        </member>
        <member name="P:XH.H82.Models.Flow.ApprovalInput.flowOperateType">
            <summary>
            操作类型  2 审批通过 3 审批不通过 5 审批驳回
            </summary>
        </member>
        <member name="P:XH.H82.Models.Flow.ApprovalInput.assignPerson">
            <summary>
            下一步审批人  当下一步审批人为指定审核人时才需要传
            </summary>
        </member>
        <member name="P:XH.H82.Models.Flow.ApprovalInput.targetNodeId">
            <summary>
            目标节点	  当操作为驳回时，才需要传
            </summary>
        </member>
        <member name="P:XH.H82.Models.Flow.ApprovalInput.rejectInfo">
            <summary>
            驳回意见 当操作为驳回时才需要传
            </summary>
        </member>
        <member name="P:XH.H82.Models.Flow.ApprovalInput.nodeRemark">
            <summary>
            审批意见	
            </summary>
        </member>
        <member name="M:XH.H82.Models.H120Client.H120Request``1(System.String,RestSharp.Method,System.Object)">
            <summary>
            调用H120方法
            </summary>
            <param name="url">api接口</param>
            <param name="method">请求方法</param>
            <param name="body">请求体</param>
            <typeparam name="T">出差模型</typeparam>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:XH.H82.Models.H120Client.StartFlowInstanceByCode(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            初始化流程
            </summary>
            <param name="flowCode">流程唯一标识</param>
            <param name="userNos">审批人（多个userNo）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.H120Client.GetFlowInstanceDetail(System.String)">
            <summary>
            获取实例日志
            </summary>
            <param name="flowInstanceId">流程实例id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.H120Client.GetFlowDetailByCode(System.String)">
            <summary>
            获取最新流程定义
            </summary>
            <param name="flowCode"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.H120Client.NextFlow(System.String,System.String,XH.H82.Models.Flow.ApprovalInput)">
            <summary>
            进入下一个审批流程
            </summary>
            <param name="flowInstanceId"></param>
            <param name="flowInstanceNodeId"></param>
            <param name="approvalInput"><see cref="T:XH.H82.Models.Flow.ApprovalInput"/></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.IBeRecordEvent">
            <summary>
            被产生事件记录模型
            </summary>
        </member>
        <member name="M:XH.H82.Models.IRecord.GetId">
            <summary>
            记录id
            </summary>
        </member>
        <member name="M:XH.H82.Models.IRecord.GetType">
            <summary>
            记录类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.IRecord.GetRecordDate">
            <summary>
            记录时间
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput">
            <summary>
            输入模型
            </summary>
            <param name="templateName">模板名称</param>
            <param name="remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput.#ctor(System.String,System.String)">
            <summary>
            输入模型
            </summary>
            <param name="templateName">模板名称</param>
            <param name="remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput.templateName">
            <summary>模板名称</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput.remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput">
            <summary>
            输入模型
            </summary>
            <param name="templateName">模板名称</param>
            <param name="remark">备注</param>
        </member>
        <member name="M:XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput.#ctor(System.String,System.String)">
            <summary>
            输入模型
            </summary>
            <param name="templateName">模板名称</param>
            <param name="remark">备注</param>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput.templateName">
            <summary>模板名称</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput.remark">
            <summary>备注</summary>
        </member>
        <member name="T:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput">
             <summary>
            保存模型内容信息
             </summary>
             <param name="tempContent">模板内容json字符串</param>
             <param name="tempTitle">模板标题</param>
             <param name="setAbnormal">异常亮灯</param>
             <param name="setQRCode">二维码</param>
             <param name="SetWireframe">表格线框</param>
        </member>
        <member name="M:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.#ctor(System.String,System.String,System.Boolean,System.Boolean,System.Boolean)">
             <summary>
            保存模型内容信息
             </summary>
             <param name="tempContent">模板内容json字符串</param>
             <param name="tempTitle">模板标题</param>
             <param name="setAbnormal">异常亮灯</param>
             <param name="setQRCode">二维码</param>
             <param name="SetWireframe">表格线框</param>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.tempContent">
            <summary>模板内容json字符串</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.tempTitle">
            <summary>模板标题</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.setAbnormal">
            <summary>异常亮灯</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.setQRCode">
            <summary>二维码</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput.SetWireframe">
            <summary>表格线框</summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.TEMPLATE_ID">
            <summary>
            模板id
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.TEMPLATE_NAME">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.TEMPLATE_CONTENT">
            <summary>
            模板内容
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.TEMPLATE_TITLE">
            <summary>
            模板标题
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.SET_ABNORMAL">
            <summary>
            异常状态亮灯
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.SET_QR_CODE">
            <summary>
            是否设置二维码
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.SET_WIREFRAME">
            <summary>
            是否设置表格线框
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.PGROUP_SID">
            <summary>
            应用的专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto.APPLICATION_GROUPS_NAME">
            <summary>
            应用的专业组中文
            </summary>
        </member>
        <member name="T:XH.H82.Models.IRecordEvent">
            <summary>
            可产生事件记录模型
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.AER_ADVERSE_EVENT">
            <summary>
            不良事件主表
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.AER_EVENT_SUBJECT">
            <summary>
            不良事件主体
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.SYS6_USER">
            <summary>
            人员基本信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.SYS6_BASE_DATA">
            <summary>
            固定基础数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.LIS_INSPECTION_LAB">
            <summary>
            科室信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.SYS6_INSPECTION_PGROUP">
            <summary>
            专业组信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.SYS_MENU_INFO_DICT">
            <summary>
            系统菜单设置字典表
            </summary>
        </member>
        <member name="P:XH.H82.Models.MainDBContext.LIS5_SAMPLE_INFO_DICT">
             <summary>
            输入信息设置字典表
             </summary>
        </member>
        <member name="T:XH.H82.Models.Smbl.Dto.SmblEquipmentDto">
            <summary>
            生安设备列表
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.EQUIPMENT_ID">
            <summary>
            设备id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.IS_HIDE">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.SMBL_FLAG">
            <summary>
            是否属于生安设备
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.SMBL_LAB_ID">
            <summary>
            备案实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.SMBL_LAB_NAME">
            <summary>
            备案实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.SMBL_CLASS">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.Dto.SmblEquipmentDto.SMBL_STATE">
            <summary>
            生安设备状态
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.HOSPITAL_ID">
            <summary>
            医疗机构
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.HOSPITAL_USCC">
            <summary>
            社会信用码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.HOSPITAL_NAME">
            <summary>
            机构名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.HOSPITAL_CNAME">
            <summary>
            机构自定义名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.REGISTER_CODE">
            <summary>
            注册码
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.REGISTER_TIME">
            <summary>
            注册时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.REGISTER_PERSON">
            <summary>
            注册人员
            </summary>
        </member>
        <member name="P:XH.H82.Models.Smbl.SMBL_HOSPITAL.REGISTER_STATE">
            <summary>
            注册状态  0 已注册   1 未注册
            </summary>
        </member>
        <member name="P:XH.H82.Models.SugarDbContext.SugarDbContext_Base.AER_ADVERSE_EVENT">
            <summary>
            不良事件主表
            </summary>
        </member>
        <member name="P:XH.H82.Models.SugarDbContext.SugarDbContext_Base.AER_EVENT_SUBJECT">
            <summary>
            不良事件主体
            </summary>
        </member>
        <member name="P:XH.H82.Models.SugarDbContext.SugarDbContext_Base.SYS6_USER">
            <summary>
            人员基本信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.SugarDbContext.SugarDbContext_Base.SYS6_BASE_DATA">
            <summary>
            固定基础数据
            </summary>
        </member>
        <member name="P:XH.H82.Models.SugarDbContext.SugarDbContext_Base.LIS_INSPECTION_LAB">
            <summary>
            科室信息
            </summary>
        </member>
        <member name="M:XH.H82.Models.SugarDbContext.SugarDbContextExt.SelectHospitalId(H.BASE.SqlSugarInfra.Uow.ISqlSugarUow{XH.H82.Models.SugarDbContext.SugarDbContext_Master},H.Utility.ClaimsDto)">
            <summary>
            根据机构id查询
            </summary>
            <param name="context"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.Models.SugarDbContext.SugarDbContextExt.NoLog(H.BASE.SqlSugarInfra.Uow.ISqlSugarUow{XH.H82.Models.SugarDbContext.SugarDbContext_Master})">
            <summary>
            不打印日志
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.UNIT_ID">
            <summary>
            所属专业组
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.HOSPITAL_ID">
            <summary>
            机构id
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.LAB_ID">
            <summary>
            实验室id
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.PROFESSIONAL_CLASS">
            <summary>
            专业类型 
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_NUM">
            <summary>
            设备序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_NAME">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.DEPT_SECTION_NO">
            <summary>
             科室设备编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_ENAME">
            <summary>
            设备英文名称
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_MODEL">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.VEST_PIPELINE">
            <summary>
            流水线所属（对应设备id）
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_CLASS">
            <summary>
            设备分类
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SECTION_NO">
            <summary>
            医院设备编号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SELL_PRICE">
            <summary>
            设备价格(元）
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.KEEP_PERSON">
            <summary>
            设备责任人
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.INSTALL_DATE">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.INSTALL_AREA">
            <summary>
            安装位置
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.DEPRECIATION_TIME">
            <summary>
            折旧时间
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.CONTACT_PHONE">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.REGISTRATION_NUM">
            <summary>
            注册证号（中文）
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.REGISTRATION_ENUM">
            <summary>
            注册证号（英文）
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_STATE">
            <summary>
            设备状态：0-未启用；1-启用；2-停用 3-报废 
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.REMARK">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQ_IN_TIME">
            <summary>
            到货日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQ_OUT_TIME">
            <summary>
            出厂日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SERIAL_NUMBER">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_CODE">
            <summary>
            设备代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.ENABLE_TIME">
            <summary>
            首次启用日期
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQ_SERVICE_LIFE">
            <summary>
            使用年限
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SMBL_FLAG">
            <summary>
            生物安全
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SMBL_LAB_ID">
            <summary>
            生安监测实验室ID
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.EQUIPMENT_UCODE">
            <summary>
            设备自身的自定义代号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.SMBL_CLASS">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Equipment.COUNTRY_ORIGIN">
            <summary>
            设备产出地 （国产，进口）
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.formName">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.formCname">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.formCode">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.ifNew">
            <summary>
            是否新增
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.ifShow">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleShow">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleColor">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleSize">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleStyle">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleBackground">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleAlign">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentLine">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentMaxLine">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentHeightClass">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentHeightRatio">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentAlign">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentColor">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentFontSize">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentStyle">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentBackground">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.titleAndContentType">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.contentEnlarge">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.ifRequired">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.replaceField">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.onlyRead">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.default">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.resetContent">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.dataType">
            <summary>
            输入方式
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.dataClass">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.allowMaintainDropDownData">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.formDesc">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.suffix">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.sort">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.editeState">
            <summary>
            
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.Form.unitFlag">
            <summary>
            
            </summary>
        </member>
        <member name="T:XH.H82.Models.TemplateDesign.PageSettingForm">
            <summary>
            工具箱表单信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.TemplateDesign.PageSettingForm.form">
            <summary>
            
            </summary>
        </member>
        <member name="T:XH.H82.Models.ViewDtos.PrintInfo">
            <summary>
            接口获取打印信息
            </summary>
        </member>
        <member name="T:XH.H82.Models.ViewDtos.UserInfo">
            <summary>
            接口获取用户信息
            </summary>
        </member>
        <member name="P:XH.H82.Models.ViewDtos.UserInfo.PERSON_PHOTO_PATH">
            <summary>
            人员图片路径
            </summary>
        </member>
        <member name="T:XH.H82.Services.AuthorizationRecord.TableUserInfoDto">
            <summary>
            悬浮框表格的用户信息
            </summary>
        </member>
        <member name="P:XH.H82.Services.AuthorizationRecord.TableUserInfoDto.Id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:XH.H82.Services.AuthorizationRecord.TableUserInfoDto.PGroupId">
            <summary>
            专业组id
            </summary>
        </member>
        <member name="P:XH.H82.Services.AuthorizationRecord.TableUserInfoDto.PGroupName">
            <summary>
            专业组名称
            </summary>
        </member>
        <member name="P:XH.H82.Services.AuthorizationRecord.TableUserInfoDto.UserName">
            <summary>
            用户名字
            </summary>
        </member>
        <member name="P:XH.H82.Services.AuthorizationRecord.TableUserInfoDto.UserNo">
            <summary>
            用户UserNo
            </summary>
        </member>
        <member name="T:XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto">
            <summary>
            档案记录编辑模型
            </summary>
        </member>
        <member name="P:XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto.EqpArchivesPid">
            <summary>
            档案记录父级ID;细分类的父级id 父级id可为空
            </summary>
        </member>
        <member name="P:XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto.EqpArchivesName">
            <summary>
            档案记录名称
            </summary>
        </member>
        <member name="P:XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto.IsIpload">
            <summary>
            是否上传附件
            </summary>
        </member>
        <member name="P:XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto.Remark">
            <summary>
            备注
            </summary>
        </member>
    </members>
</doc>
