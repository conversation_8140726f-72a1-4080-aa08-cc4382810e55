﻿using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using SqlSugar;
using XH.H82.Base.Tree;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using SYS6_HOSPITAL_INFO = XH.LAB.UTILS.Models.SYS6_HOSPITAL_INFO;

namespace XH.H82.Services
{
    public class AuthorityContext
    {
        private readonly ISqlSugarUow _dbContext;
        private string _modelId { get; set; } = "H82";
        private ClaimsDto _user { get; set; }
        private readonly IAuthorityService _authorityService;
        private readonly IAuthorityService2 _authorityService2;
        private List<SYS6_INSPECTION_PGROUP> _pgroups = new();
        private List<SYS6_INSPECTION_LAB> _labs = new();
        private List<SMBL_LAB> _smblLabs  = new();
        public AuthorityContext(ISqlSugarUow dbContext, IAuthorityService2 authorityService2 , IAuthorityService authorityService  = null)
        {
            _dbContext = dbContext;
            _authorityService = authorityService;
            _authorityService2 = authorityService2;
        }

        public void SetUser(ClaimsDto user , string? labId , string? areaId = null)
        {
            _user = user;
            _pgroups.AddRange(GetAccessibleProfessionalGroups(labId, areaId));
            //CheckedUserAuth();
        }

        /// <summary>
        /// 根据用户所在的机构，查询当前用户能访问的所有专业组
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="aearId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_PGROUP> GetAccessibleProfessionalGroups(string? labId, string? aearId)
        {
            if (_pgroups.Count() == 0)
            {
                var groupList = _authorityService2.GetUserPermissionPgroup(_dbContext, 
                        new ()
                        {
                            hospital_id = _user.HOSPITAL_ID,
                            lab_id = labId,
                            area_id = aearId
                        },
                         AppSettingsProvider.CurrModuleId
                        )
                     .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.PGROUP_CLASS != "4")
                     .WhereIF(aearId.IsNotNullOrEmpty(), x => x.AREA_ID == aearId)
                    .ToList();
                return groupList;
            }
            return _pgroups
                .WhereIF(labId.IsNotNullOrEmpty(), x => x.LAB_ID == labId)
                .WhereIF(aearId.IsNotNullOrEmpty(), x => x.AREA_ID == aearId)
                .ToList();
        }

        public List<SYS6_INSPECTION_MGROUP> GetManagerGroups(string? labId, string? aearId, string? mgroupId)
        {
            var pgroups = GetAccessibleProfessionalGroups(labId, aearId);
            var mgroupIds = pgroups
                .WhereIF(mgroupId.IsNotNullOrEmpty(), pg => pg.MGROUP_ID == mgroupId)
                .Where(pg=>pg.MGROUP_ID != null)
                .Select(pg => pg.MGROUP_ID)
                .Distinct();
            var result = _dbContext.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(mg => mg.MGROUP_STATE == "1" && mgroupIds.Contains(mg.MGROUP_ID))
                .OrderBy(mg => mg.MGROUP_SORT)
                
                .ToList();
            if (result is null)
            {
                return new List<SYS6_INSPECTION_MGROUP>();
            }
            return result;
        }

        /// <summary>
        /// 根据用户所在的机构，查询当前用户能访问的所有科室
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_AREA> GetAccessibleAreas(string? labId)
        {
            var areaIds = GetAccessibleProfessionalGroups(labId, null).Select(x => x.AREA_ID).Distinct();
            var areaList = _dbContext.Db.Queryable<SYS6_INSPECTION_AREA>()
                .Where(x=>areaIds.Contains(x.AREA_ID))
                .OrderBy(x=>x.AREA_ID)
              .ToList();
            var result = new List<SYS6_INSPECTION_AREA>();
            if (areaList is not null)
            {
                result.AddRange(areaList);
            }
            return result;
        }

        public List<SYS6_INSPECTION_LAB> GetUserLabList()
        {
            if (_labs.Count() == 0)
            {
                var result = _authorityService2.GetUserPermissionLab(_dbContext,
                    new()
                    {
                        hospital_id = _user.HOSPITAL_ID,
                    },
                    _modelId
                );
                _labs.AddRange(result);
                return result;
            }
            return _labs;
        }

        public List<SMBL_LAB> GetUserSmbls()
        {
            
            if (_smblLabs.Count() == 0)
            {
                var result = _authorityService2.GetUserPermissionSmblLabs(_dbContext,
                    new()
                    {
                        
                    },
                    _modelId
                );
                _smblLabs.AddRange(result);
                return result;
            }
            return _smblLabs;
        }

        public List<(string? DEPT_CODE , string USER_NO ,  string HIS_NAME)> GetPermissionUsers(string permissionId)
        {
            var dict = new Dictionary<string, (string, string, string)>();
            var users = _authorityService2.GetH07MenuApplyUser(permissionId);
            foreach (var user in users)     
            {
                if (!dict.TryGetValue(user.USER_NO, out var userInfo))
                {
                    dict.Add( user.USER_NO, (user.DEPT_CODE, user.USER_NO, $"{user.LOGID}_{user.USERNAME}"));
                }
            }
            var result = dict.Values.ToList();

            return result;
        }

        public void CheckedUserAuth(string? labId = null, string? areaId = null, string? mgId = null, string? pgId = null)
        {

            bool authorizationSuccessful = true;
            if (labId.IsNotNullOrEmpty())
            {
                authorizationSuccessful = GetUserLabList().Any(x => x.LAB_ID == labId);
            }
            if (areaId.IsNotNullOrEmpty())
            {
                authorizationSuccessful = GetAccessibleAreas(labId).Any(x => x.AREA_ID == areaId);
            }
            if (labId.IsNotNullOrEmpty() && pgId.IsNotNullOrEmpty())
            {
                authorizationSuccessful = GetAccessibleProfessionalGroups(labId, areaId).Any(x => x.PGROUP_ID == pgId);
            }
            if (labId.IsNotNullOrEmpty() && mgId.IsNotNullOrEmpty())
            {
                authorizationSuccessful = GetAccessibleProfessionalGroups(labId, areaId).Any(x => x.MGROUP_ID == mgId);
            }

            if (!authorizationSuccessful)
            {
                throw new UnauthorizedAccessException("权限异常");
            }
        }
        /// <summary>
        /// 获取院区-专业组树|下拉选项
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="modelId"></param>
        /// <returns></returns>

        public RootNode GetAreaTree(string labId, string? areaId, string? pgroupId, string? modelId)
        {
            var (root, nodes) = GetTheBaseTree(labId, areaId, null, null, modelId, false);
            //剔除专业组节点
            if (root.CHILDREN.Count() > 0)
            {
                root.CHILDREN.RemoveAll(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB || x.NODE_TYPE == TreeNodeTypeEnum.MGROUP);
            }
            var areaNodes = nodes.Where(x => x.NODE_TYPE == TreeNodeTypeEnum.AREA).OrderBy(x => x.AREA_ID);
            var pgNodes = nodes.Where(x => x.NODE_TYPE == TreeNodeTypeEnum.PGROUP);

            foreach (var node in areaNodes)
            {
                var areaPgNode = pgNodes.Where(x => x.AREA_ID == node.AREA_ID).ToList();
                areaPgNode.ForEach(x => x.ISLEAVES = true);
                node.AddChilds(areaPgNode);
            }
            
            root.NUM = root.ConutChildrens(root);
            return root;
        }

        public RootNode GetUserTree(string? labId, string? aearId, string? mgroupId, string? pgroupId, string? modelId, bool isNeedEntity = false)
        {
            var (root, nodes) = GetTheBaseTree(labId, aearId, mgroupId, pgroupId, modelId, isNeedEntity);
            var users = _dbContext.Db.Queryable<SYS6_USER>()
                .Where(a => a.DEPT_CODE != null)
                .ToList();
            int nodeNo = 1;
            if (nodes.Any())
            {
                nodeNo = nodes.Max(a => a.NODE_NO);
            }
            if (root.CHILDREN.Count() > 0)
            {
                root.CHILDREN.RemoveAll(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB || x.NODE_TYPE == TreeNodeTypeEnum.MGROUP);
            }

            foreach (var user in users)
            {
                var node = nodes.FirstOrDefault(x => x.SOURCE_ID == user.DEPT_CODE);
                if (node is not null)
                {
                    var userNode = AddUserNode(node, user, isNeedEntity, ref nodeNo);
                }
            }
            var pgNodes = nodes.Where(x => x.NODE_TYPE == TreeNodeTypeEnum.PGROUP).ToList();
            root.AddChilds(pgNodes);

            root.NUM = root.ConutChildrens(root);
            root.CHILDREN.RemoveAll(x => x.NUM == 0);
            return root;
        }
        public RootNode GetEquipmentTree(string? labId, string? aearId, string? mgroupId, string? pgroupId, string? modelId, bool isNeedEntity = false)
        {
            var (root, nodes) = GetTheBaseTree(labId, aearId, mgroupId, pgroupId, modelId, isNeedEntity);
            int nodeNo = nodes.Max(a => a.NODE_NO);
            var pgIds = nodes.Where(x => x.NODE_TYPE == TreeNodeTypeEnum.PGROUP).Select(x => x.SOURCE_ID);
            //剔除专业组节点
            if (root.CHILDREN.Count() > 0)
            {
                root.CHILDREN.RemoveAll(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB || x.NODE_TYPE == TreeNodeTypeEnum.AREA );
            }
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                                            .Where(x => pgIds.Contains(x.UNIT_ID))
                                            .OrderBy(x => x.EQUIPMENT_CLASS)
                                            .ToList();
            var equipmentNodes = new List<ITreeNode>();
            foreach (var equipment in equipments.Where(x=>x.VEST_PIPELINE is null).ToList())
            {
                var node = nodes.FirstOrDefault(x => x.SOURCE_ID == equipment.UNIT_ID);
                if (node is not null)
                {
                   var equipmentNode =  AddEquipmentNode(node, equipment, true, ref nodeNo);
                   equipmentNodes.Add(equipmentNode);
                }
            }
            foreach (var equipment in equipments.Where(x=>x.VEST_PIPELINE  is not null).ToList())
            {
                var node = equipmentNodes.FirstOrDefault(x => x.SOURCE_ID == equipment.VEST_PIPELINE);
                if (node is not null)
                {
                    AddEquipmentNode(node, equipment, true, ref nodeNo);
                }
            }
            
            root.NUM = root.ConutChildrens(root);
            root.CHILDREN.RemoveAll(x => x.NUM == 0);
            return root;
        }
        public (RootNode rootRoot, List<ITreeNode> nodes) GetTheBaseTree(string? labId, string? areaId, string? mgroupId, string? pgroupId, string? modelId, bool isNeedEntity = false, bool isNeedPublicNode = false)
        {
            var result = new RootNode();
            int nodeNo = 1;
            if (modelId is not null)
            {
                _modelId = modelId;
            }
            var pgroups = GetAccessibleProfessionalGroups(labId, areaId)
                .WhereIF(pgroupId.IsNotNullOrEmpty(), pg => pg.PGROUP_ID == pgroupId)
                .OrderBy(pg => pg.PGROUP_SORT)
                .ThenBy(pg => pg.PGROUP_ID)
                .ToList();
            
            var areaIds = pgroups.OrderBy(x => x.AREA_ID).Select(x => x.AREA_ID).Distinct();
            
            if (pgroups is null)
            {
                pgroups = new();
            }

            var mgroups = GetManagerGroups(labId, areaId, mgroupId);

            List<ITreeNode> nodes = new List<ITreeNode>();

            if (labId is not null)
            {
                var labNode = AddLabNode(result, labId, isNeedEntity, ref nodeNo);
                nodes.Add(labNode);
                if (isNeedPublicNode)
                {
                    nodes.Add(AddPublicNode(labNode, labNode.SOURCE_ID, "公共类型", TreeNodeTypeEnum.LABPUBLIC, ref nodeNo));
                }
            }

            foreach (var id in areaIds)
            {
                var areaNode = AddAreaNode(result, id, isNeedEntity, ref nodeNo);
                if (areaNode  is not  null)
                {
                    nodes.Add(areaNode);
                }
            }
            foreach (var mgroup in mgroups)
            {
                var mgNode = AddMGproug(result, mgroup, isNeedEntity, ref nodeNo);
                nodes.Add(mgNode);
                if (isNeedPublicNode)
                {
                    nodes.Add(AddPublicNode(mgNode, mgNode.SOURCE_ID, "公共类型", TreeNodeTypeEnum.MGROUPPUBLIC, ref nodeNo));
                }
            }

            foreach (var pgroup in pgroups)
            {
                ITreeNode node = null;
                if (pgroup.MGROUP_ID is null)
                {
                    node = nodes.Where(x => x.SOURCE_ID == pgroup.LAB_ID).FirstOrDefault();
                }
                else
                {
                    node = nodes.Where(x => x.SOURCE_ID == pgroup.MGROUP_ID).FirstOrDefault();
                }
                if (node is not null)
                {
                    var pgNode = AddPGproug(node, pgroup, isNeedEntity, ref nodeNo);
                    nodes.Add(pgNode);
                }
            }
            nodes.RemoveAll(x => x is null);
            return (result, nodes);
        }
        public ITreeNode AddLabNode(ITreeNode tree, string labId, bool isNeedEntity, ref int nodeNum)
        {
            var lab = _dbContext.Db.Queryable<SYS6_INSPECTION_LAB>().Where(a => a.LAB_ID == labId && a.STATE_FLAG == "1").First();
            if (lab != null)
            {
                var labNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.LAB,// 节点类型
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc(),//"科室"
                    SOURCE_ID = lab.LAB_ID,
                    SOURCE = isNeedEntity ? lab : null,
                    LAB_ID = lab.LAB_ID,
                    NAME = lab.LAB_NAME,
                    SORT = lab.LAB_SORT
                };
                //labNode.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{labNode.NODE_NO}";
                //var sourePath = tree is RootNode ? labNode.SOURCE_ID : $"{((AllTreeNode)tree).SOURCE_PATH}/{labNode.SOURCE_ID}";
                //labNode.SOURCE_PATH = $"{sourePath}";
                tree.AddChild(labNode);

                return labNode;
            }
            return null;
        }
        public ITreeNode AddLabNode(ITreeNode tree, SYS6_INSPECTION_LAB lab, bool isNeedEntity, ref int nodeNum)
        {
                var labNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.LAB,// 节点类型
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc(),//"科室"
                    SOURCE_ID = lab.LAB_ID,
                    SOURCE = isNeedEntity ? lab : null,
                    LAB_ID = lab.LAB_ID,
                    NAME = lab.LAB_NAME,
                    SORT = lab.LAB_SORT
                };
                //labNode.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{labNode.NODE_NO}";
                //var sourePath = tree is RootNode ? labNode.SOURCE_ID : $"{((AllTreeNode)tree).SOURCE_PATH}/{labNode.SOURCE_ID}";
                //labNode.SOURCE_PATH = $"{sourePath}";
                tree.AddChild(labNode);
                return labNode;
        }
        public ITreeNode AddAreaNode(ITreeNode tree, string areaId, bool isNeedEntity, ref int nodeNum)
        {
            var area = _dbContext.Db.Queryable<SYS6_INSPECTION_AREA>()
                .Where(a => a.HOSPITAL_ID == _user.HOSPITAL_ID && a.AREA_ID == areaId && a.STATE_FLAG == "1").First();
            if (area != null)
            {
                var areaNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.AREA,// 节点类型
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc(),//"院区"
                    SOURCE_ID = area.AREA_ID,
                    SOURCE = isNeedEntity ? area : null,
                    AREA_ID = area.AREA_ID,
                    NAME = area.AREA_NAME,
                    SORT = area.AREA_SORT
                };
                //areaNode.NODE_PATH = $"{tree.NODE_PATH},{areaNode.NODE_NO}";
                //var sourePath = tree is RootNode ? areaNode.SOURCE_ID : $"{((AllTreeNode)tree).SOURCE_PATH}/{areaNode.SOURCE_ID}";

                //areaNode.SOURCE_PATH = $"{sourePath}";

                tree.AddChild(areaNode);

                return areaNode;
            }
            return null;
        }
        public ITreeNode AddAreaNode(ITreeNode tree, SYS6_INSPECTION_AREA area, bool isNeedEntity, ref int nodeNum)
        {
                var areaNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.AREA,// 节点类型
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.AREA.ToDesc(),//"院区"
                    SOURCE_ID = area.AREA_ID,
                    SOURCE = isNeedEntity ? area : null,
                    AREA_ID = area.AREA_ID,
                    NAME = area.AREA_NAME,
                    SORT = area.AREA_SORT
                };
                tree.AddChild(areaNode);
                return areaNode;
        }
        /// <summary>
        /// 添加生安机构节点数据
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="hospitalId"></param>
        /// <param name="isNeedEntity"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddSmblHospitalNode(ITreeNode tree , string hospitalId , bool isNeedEntity ,  ref int nodeNum)
        {
            var hospital = _dbContext.Db.Queryable<SYS6_HOSPITAL_INFO>().Where(a => a.HOSPITAL_ID == hospitalId).First();
            if (hospital != null)
            {
                var smblHospitalNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.HOSPITAL,// 节点类型
                    NODE_TYPE_NAME = TreeNodeTypeEnum.HOSPITAL.ToDesc(),//"院区"
                    SOURCE_ID = hospital.HOSPITAL_ID,
                    SOURCE = isNeedEntity ? hospital : null,
                    NAME = hospital.HOSPITAL_CNAME,
                    SORT = "0"
                };
                tree.AddChild(smblHospitalNode);
                return smblHospitalNode;
            }
            return null;
        }
        /// <summary>
        /// 添加生安备案实验室
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="smblLabId"></param>
        /// <param name="isNeedEntity"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddSmblLabNode(ITreeNode tree , string smblLabId,bool isNeedEntity ,  ref int nodeNum)
        {
            var smblLab = _dbContext.Db.Queryable<SMBL_LAB>().Where(x=>x.SMBL_LAB_STATE == "1").Where(a => a.SMBL_LAB_ID == smblLabId).First();
            if (smblLab != null)
            {
                var smblLabNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.SMBLLAB,// 节点类型
                    NODE_TYPE_NAME = TreeNodeTypeEnum.SMBLLAB.ToDesc(),
                    SOURCE_ID = smblLab.SMBL_LAB_ID,
                    SOURCE = isNeedEntity ? smblLab : null,
                    NAME = smblLab.SMBL_LAB_CNAME,
                    AREA_ID = smblLab.AREA_ID,
                    LAB_ID = smblLab.LAB_ID
                };
                tree.AddChild(smblLabNode);
                return smblLabNode;
            }
            return null;
        }
        /// <summary>
        /// 添加生安备案实验室
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="smblLabId"></param>
        /// <param name="isNeedEntity"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddSmblLabNode(ITreeNode tree , SMBL_LAB smblLab,bool isNeedEntity ,  ref int nodeNum)
        {
                var smblLabNode = new AllTreeNode
                {
                    NODE_NO = nodeNum++,
                    NODE_TYPE = TreeNodeTypeEnum.SMBLLAB,// 节点类型
                    NODE_TYPE_NAME = TreeNodeTypeEnum.SMBLLAB.ToDesc(),
                    SOURCE_ID = smblLab.SMBL_LAB_ID,
                    SOURCE = isNeedEntity ? smblLab : null,
                    NAME = smblLab.SMBL_LAB_CNAME,
                    AREA_ID = smblLab.AREA_ID,
                    LAB_ID = smblLab.LAB_ID
                };
                tree.AddChild(smblLabNode);
                return smblLabNode;
        }
        public ITreeNode AddMGproug(ITreeNode tree, SYS6_INSPECTION_MGROUP mGproug, bool isNeedEntity, ref int nodeNum)
        {
            var mGroupNode = new AllTreeNode
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = TreeNodeTypeEnum.MGROUP,
                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.MGROUP.ToDesc(),
                SOURCE_ID = mGproug.MGROUP_ID,
                SOURCE = isNeedEntity ? mGproug : null,
                LAB_ID = mGproug.LAB_ID,
                NAME = mGproug.MGROUP_NAME,
                SORT = mGproug.MGROUP_SORT
            };
            tree.AddChild(mGroupNode);
            //mGroupNode.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{mGroupNode.NODE_NO}";
            //var sourePath = tree is RootNode ? mGroupNode.SOURCE_ID : $"{((AllTreeNode)tree).SOURCE_PATH}/{mGroupNode.SOURCE_ID}";
            //mGroupNode.SOURCE_PATH = $"{sourePath}";

            return mGroupNode;
        }
        public ITreeNode AddPGproug(ITreeNode tree, SYS6_INSPECTION_PGROUP pGproug, bool isNeedEntity, ref int nodeNum)
        {
            var pGroupNode = new AllTreeNode
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = TreeNodeTypeEnum.PGROUP,
                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc(),
                SOURCE_ID = pGproug.PGROUP_ID,
                SOURCE = isNeedEntity ? pGproug : null,
                LAB_ID = pGproug.LAB_ID,
                AREA_ID = pGproug.AREA_ID,
                NAME = pGproug.PGROUP_NAME,
                SORT = pGproug.PGROUP_SORT,

            };
            //pGroupNode.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{pGroupNode.NODE_NO}";
            //var sourePath = tree is RootNode ? pGroupNode.SOURCE_ID : $"{((AllTreeNode)tree).SOURCE_PATH}/{pGroupNode.SOURCE_ID}";
            //pGroupNode.SOURCE_PATH = $"{sourePath}";
            tree.AddChild(pGroupNode);
            return pGroupNode;
        }
        public ITreeNode AddUserNode(ITreeNode tree, SYS6_USER user, bool isNeedEntity, ref int nodeNum)
        {

            var userNode = new AllTreeNode
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = TreeNodeTypeEnum.USER,
                NODE_TYPE_NAME = TreeNodeTypeEnum.USER.ToDesc(),
                SOURCE_ID = user.USER_NO,
                SOURCE = isNeedEntity ? user : null,
                NAME = user.USERNAME,
                SORT = user.USER_NO,
                ISLEAVES = true
            };
            //userNode.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{userNode.NODE_NO}";
            //userNode.SOURCE_PATH = $"{((AllTreeNode)tree).SOURCE_PATH}/{userNode.SOURCE_ID}";
            tree.AddChild(userNode);
            return userNode;

        }
        /// <summary>
        /// 添加公共节点
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="id"></param>
        /// <param name="nodeName"></param>
        /// <param name="treeNodeTypeEnum"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddPublicNode(ITreeNode tree, string id, string? nodeName, TreeNodeTypeEnum treeNodeTypeEnum, ref int nodeNum)
        {
            var node = new AllTreeNode
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = treeNodeTypeEnum,
                NODE_TYPE_NAME = treeNodeTypeEnum.ToDesc(),
                SOURCE_ID = id,
                SOURCE = null,
                NAME = nodeName ?? "公共类型",
                SORT = null
            };
            //node.NODE_PATH = $"{((AllTreeNode)tree).NODE_PATH},{node.NODE_NO}";
            //node.SOURCE_PATH = $"{((AllTreeNode)tree).SOURCE_PATH}/{node.NODE_TYPE.ToID()}";
            tree.AddChild(node);
            return node;
        }
        
        /// <summary>
        /// 添加生安设备分类节点
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="data"></param>
        /// <param name="isNeedEntity"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddEquipmentSmblClassNode(ITreeNode tree ,SYS6_BASE_DATA data , bool isNeedEntity, ref int nodeNum)
        {
            var entity = new
            {
                data.DATA_ID,
                data.DATA_CNAME,
                data.DATA_SNAME,
                data.CLASS_ID
            };
            
            var classNode = new AllTreeNode()
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = TreeNodeTypeEnum.EQUIPMENTCLASS,
                NODE_TYPE_NAME = TreeNodeTypeEnum.EQUIPMENTCLASS.ToDesc(),
                SOURCE_ID = data.DATA_ID,
                SOURCE = isNeedEntity ? entity : null,
                NAME = data.DATA_CNAME ?? "",
                SORT = data.DATA_SORT ?? "",
                ISLEAVES = false
            };
            tree.AddChild(classNode);
            return classNode;
        }
        /// <summary>
        /// 添加设备分类节点
        /// </summary>
        /// <param name="tree"></param>
        /// <param name="data"></param>
        /// <param name="isNeedEntity"></param>
        /// <param name="nodeNum"></param>
        /// <returns></returns>
        public ITreeNode AddEquipmentClassNode(ITreeNode tree ,SYS6_BASE_DATA data , bool isNeedEntity, ref int nodeNum)
        {
            var entity = new
            {
                data.DATA_ID,
                data.DATA_CNAME,
                data.DATA_SNAME,
                data.CLASS_ID
            };
            
            var classNode = new AllTreeNode()
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = data.DATA_ID switch
                {
                    "1"=> TreeNodeTypeEnum.Conventional,
                    "2"=>TreeNodeTypeEnum.Specimens,
                    "3"=>TreeNodeTypeEnum.Auxiliary,
                    "4"=> TreeNodeTypeEnum.POCT,
                    "5"=>TreeNodeTypeEnum.Storage,
                    "6"=>TreeNodeTypeEnum.Metering,
                    "7"=> TreeNodeTypeEnum.Information,
                    "8"=> TreeNodeTypeEnum.Scientific,
                    "9"=>TreeNodeTypeEnum.Assessment,
                    "10"=>TreeNodeTypeEnum.Special,
                    "11"=> TreeNodeTypeEnum.Monitoring,
                    _ => TreeNodeTypeEnum.EQUIPMENTCLASS
                },
                NODE_TYPE_NAME = data.DATA_ID switch
                {
                    "1"=> TreeNodeTypeEnum.Conventional.ToDesc(),
                    "2"=>TreeNodeTypeEnum.Specimens.ToDesc(),
                    "3"=>TreeNodeTypeEnum.Auxiliary.ToDesc(),
                    "4"=> TreeNodeTypeEnum.POCT.ToDesc(),
                    "5"=>TreeNodeTypeEnum.Storage.ToDesc(),
                    "6"=>TreeNodeTypeEnum.Metering.ToDesc(),
                    "7"=> TreeNodeTypeEnum.Information.ToDesc(),
                    "8"=> TreeNodeTypeEnum.Scientific.ToDesc(),
                    "9"=>TreeNodeTypeEnum.Assessment.ToDesc(),
                    "10"=>TreeNodeTypeEnum.Special.ToDesc(),
                    "11"=> TreeNodeTypeEnum.Monitoring.ToDesc(),
                    _ => TreeNodeTypeEnum.EQUIPMENTCLASS.ToDesc()
                },
                SOURCE_ID = data.DATA_ID,
                SOURCE = isNeedEntity ? entity : null,
                NAME = data.DATA_CNAME ?? "",
                SORT = data.DATA_SORT ?? "",
                ISLEAVES = false
            };
            tree.AddChild(classNode);
            return classNode;
        }

        public ITreeNode AddEquipmentNode(ITreeNode tree , EMS_EQUIPMENT_INFO equipment, bool isNeedEntity, ref int nodeNum)
        {
            equipment.IS_HIDE  = equipment.IS_HIDE ==  "1" ? "1" : "0";
            var entity = new
            {
                equipment.EQUIPMENT_ID,
                equipment.EQUIPMENT_CODE,
                equipment.EQUIPMENT_NAME,
                equipment.EQUIPMENT_STATE,
                equipment.EQUIPMENT_CLASS,
                equipment.EQUIPMENT_TYPE,
                equipment.INSTRUMENT_ID,
                equipment.VEST_PIPELINE,
                equipment.DEPT_SECTION_NO,
                equipment.UNIT_ID,
                equipment.IS_HIDE 
            };
            
            var equipmentNode = new AllTreeNode
            {
                NODE_NO = nodeNum++,
                NODE_TYPE = equipment.EQUIPMENT_CLASS switch
                {
                    "1"=> TreeNodeTypeEnum.Conventional,
                    "2"=>TreeNodeTypeEnum.Specimens,
                    "3"=>TreeNodeTypeEnum.Auxiliary,
                    "4"=> TreeNodeTypeEnum.POCT,
                    "5"=>TreeNodeTypeEnum.Storage,
                    "6"=>TreeNodeTypeEnum.Metering,
                    "7"=> TreeNodeTypeEnum.Information,
                    "8"=> TreeNodeTypeEnum.Scientific,
                    "9"=>TreeNodeTypeEnum.Assessment,
                    "10"=>TreeNodeTypeEnum.Special,
                    "11"=> TreeNodeTypeEnum.Monitoring,
                    _ => TreeNodeTypeEnum.Conventional
                },
                NODE_TYPE_NAME = equipment.EQUIPMENT_CLASS switch
                {
                    "1"=> TreeNodeTypeEnum.Conventional.ToDesc(),
                    "2"=>TreeNodeTypeEnum.Specimens.ToDesc(),
                    "3"=>TreeNodeTypeEnum.Auxiliary.ToDesc(),
                    "4"=> TreeNodeTypeEnum.POCT.ToDesc(),
                    "5"=>TreeNodeTypeEnum.Storage.ToDesc(),
                    "6"=>TreeNodeTypeEnum.Metering.ToDesc(),
                    "7"=> TreeNodeTypeEnum.Information.ToDesc(),
                    "8"=> TreeNodeTypeEnum.Scientific.ToDesc(),
                    "9"=>TreeNodeTypeEnum.Assessment.ToDesc(),
                    "10"=>TreeNodeTypeEnum.Special.ToDesc(),
                    "11"=> TreeNodeTypeEnum.Monitoring.ToDesc(),
                    _ => TreeNodeTypeEnum.Conventional.ToDesc()
                },
                SOURCE_ID = equipment.EQUIPMENT_ID,
                SOURCE = isNeedEntity ? entity : null,
                NAME = equipment.EQUIPMENT_CODE ?? equipment.EQUIPMENT_NAME,
                SORT = equipment.EQUIPMENT_SORT,
                ISLEAVES = true
            };
            tree.AddChild(equipmentNode);
            return equipmentNode;
        }
    }
}
